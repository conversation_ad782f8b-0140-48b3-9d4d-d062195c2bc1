[0.013s] Invoking command in '/home/<USER>/xugong_web_ws/build/web': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/web -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/web
[0.063s] -- The C compiler identification is GNU 11.4.0
[0.109s] -- The CXX compiler identification is GNU 11.4.0
[0.117s] -- Detecting C compiler ABI info
[0.176s] -- Detecting C compiler ABI info - done
[0.181s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.181s] -- Detecting C compile features
[0.182s] -- Detecting C compile features - done
[0.184s] -- Detecting CXX compiler ABI info
[0.251s] -- Detecting CXX compiler ABI info - done
[0.257s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.257s] -- Detecting CXX compile features
[0.258s] -- Detecting CXX compile features - done
[0.260s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.382s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.466s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.501s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.505s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.511s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.520s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.531s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.570s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.573s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.652s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.677s] -- Found FastRTPS: /opt/ros/humble/include  
[0.714s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.725s] -- Looking for pthread.h
[0.785s] -- Looking for pthread.h - found
[0.785s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[0.848s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[0.849s] -- Found Threads: TRUE  
[0.894s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.917s] -- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)
[0.933s] -- Found hv: /usr/local/include  
[0.935s] -- Found nlohmann_json: /usr/lib/cmake/nlohmann_json/nlohmann_jsonConfig.cmake (found version "3.10.5") 
[0.938s] -- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)
[0.948s] -- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
[0.963s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[0.964s] -- Found Eigen3: TRUE (found version "3.4.0") 
[0.964s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[1.054s] -- Checking for module 'eigen3'
[1.065s] --   Found eigen3, version 3.4.0
[1.094s] -- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") 
[1.094s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[1.115s] -- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found suitable version "1.74.0", minimum required is "1.65.0") found components: system filesystem date_time iostreams serialization 
[1.166s] -- Checking for module 'libusb-1.0'
[1.181s] --   Found libusb-1.0, version 1.0.25
[1.210s] -- Found libusb: /usr/lib/x86_64-linux-gnu/libusb-1.0.so  
[1.212s] -- Found OpenNI: /usr/lib/libOpenNI.so;libusb::libusb (found version "1.5.4.0") 
[1.212s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[1.260s] -- Found OpenNI2: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb (found version "2.2.0.33") 
[1.260s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
[1.260s] [0m** WARNING ** io features related to pcap will be disabled[0m
[1.260s] [0m** WARNING ** io features related to png will be disabled[0m
[1.289s] -- Found GLEW: /usr/lib/x86_64-linux-gnu/libGLEW.so  
[1.294s] -- Found OpenGL: /usr/lib/x86_64-linux-gnu/libOpenGL.so  found components: OpenGL GLX 
[1.424s] -- Found MPI_C: /usr/lib/x86_64-linux-gnu/libmpi.so (found version "3.1") 
[1.424s] -- Found MPI: TRUE (found version "3.1") found components: C 
[1.430s] -- Found JsonCpp: /usr/lib/x86_64-linux-gnu/libjsoncpp.so (found suitable version "1.9.5", minimum required is "0.7.0") 
[1.447s] -- Found ZLIB: /usr/lib/x86_64-linux-gnu/libz.so (found version "1.2.11") 
[1.467s] -- Found PNG: /usr/lib/x86_64-linux-gnu/libpng.so (found version "1.6.37") 
[1.484s] -- Found Eigen3: /usr/include/eigen3 (found version "3.4.0") 
[1.560s] -- Found X11: /usr/include   
[1.562s] -- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so
[1.626s] -- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so - found
[1.626s] -- Looking for gethostbyname
[1.723s] -- Looking for gethostbyname - found
[1.723s] -- Looking for connect
[1.824s] -- Looking for connect - found
[1.824s] -- Looking for remove
[1.925s] -- Looking for remove - found
[1.925s] -- Looking for shmat
[2.018s] -- Looking for shmat - found
[2.018s] -- Looking for IceConnectionNumber in ICE
[2.126s] -- Looking for IceConnectionNumber in ICE - found
[2.204s] -- Found EXPAT: /usr/lib/x86_64-linux-gnu/libexpat.so (found version "2.4.7") 
[2.206s] -- Found double-conversion: /usr/lib/x86_64-linux-gnu/libdouble-conversion.so  
[2.208s] -- Found LZ4: /usr/lib/x86_64-linux-gnu/liblz4.so (found version "1.9.3") 
[2.210s] -- Found LZMA: /usr/lib/x86_64-linux-gnu/liblzma.so (found version "5.2.5") 
[2.222s] -- Found JPEG: /usr/lib/x86_64-linux-gnu/libjpeg.so (found version "80") 
[2.227s] -- Found TIFF: /usr/lib/x86_64-linux-gnu/libtiff.so (found version "4.3.0")  
[2.231s] -- Found Freetype: /usr/lib/x86_64-linux-gnu/libfreetype.so (found version "2.11.1") 
[2.237s] -- Found utf8cpp: /usr/include/utf8cpp  
[2.266s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[2.283s] -- Checking for module 'flann'
[2.305s] --   Found flann, version 1.9.1
[2.366s] -- Found FLANN: /usr/lib/x86_64-linux-gnu/libflann_cpp.so  
[2.366s] -- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)
[2.368s] -- looking for PCL_COMMON
[2.369s] -- Found PCL_COMMON: /usr/lib/x86_64-linux-gnu/libpcl_common.so  
[2.369s] -- looking for PCL_OCTREE
[2.370s] -- Found PCL_OCTREE: /usr/lib/x86_64-linux-gnu/libpcl_octree.so  
[2.370s] -- looking for PCL_IO
[2.372s] -- Found PCL_IO: /usr/lib/x86_64-linux-gnu/libpcl_io.so  
[2.372s] -- looking for PCL_KDTREE
[2.373s] -- Found PCL_KDTREE: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so  
[2.373s] -- looking for PCL_SEARCH
[2.374s] -- Found PCL_SEARCH: /usr/lib/x86_64-linux-gnu/libpcl_search.so  
[2.374s] -- looking for PCL_SAMPLE_CONSENSUS
[2.374s] -- Found PCL_SAMPLE_CONSENSUS: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so  
[2.374s] -- looking for PCL_FILTERS
[2.375s] -- Found PCL_FILTERS: /usr/lib/x86_64-linux-gnu/libpcl_filters.so  
[2.375s] -- Found PCL: pcl_common;pcl_octree;pcl_io;pcl_kdtree;pcl_search;pcl_sample_consensus;pcl_filters;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;/usr/lib/libOpenNI.so;libusb::libusb;/usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb;VTK::ChartsCore;VTK::CommonColor;VTK::CommonComputationalGeometry;VTK::CommonCore;VTK::CommonDataModel;VTK::CommonExecutionModel;VTK::CommonMath;VTK::CommonMisc;VTK::CommonTransforms;VTK::FiltersCore;VTK::FiltersExtraction;VTK::FiltersGeneral;VTK::FiltersGeometry;VTK::FiltersModeling;VTK::FiltersSources;VTK::ImagingCore;VTK::ImagingSources;VTK::InteractionImage;VTK::InteractionStyle;VTK::InteractionWidgets;VTK::IOCore;VTK::IOGeometry;VTK::IOImage;VTK::IOLegacy;VTK::IOPLY;VTK::RenderingAnnotation;VTK::RenderingCore;VTK::RenderingContext2D;VTK::RenderingLOD;VTK::RenderingFreeType;VTK::ViewsCore;VTK::ViewsContext2D;VTK::RenderingOpenGL2;VTK::GUISupportQt;FLANN::FLANN  
[2.378s] -- Found pcl_conversions: 2.4.5 (/opt/ros/humble/share/pcl_conversions/cmake)
[2.398s] -- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
[2.405s] -- Found keyframe_msgs: 0.0.0 (/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake)
[2.418s] -- Found can_bridge: 0.0.0 (/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake)
[2.433s] -- Found remotecontrol_msgs: 0.0.0 (/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake)
[2.445s] -- Found global_traj_generate: 0.0.0 (/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake)
[2.467s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[2.534s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[2.535s] -- Configured cppcheck include dirs: 
[2.535s] -- Configured cppcheck exclude dirs and/or files: 
[2.536s] -- Added test 'flake8' to check Python code syntax and style conventions
[2.537s] -- Added test 'lint_cmake' to check CMake code style
[2.538s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[2.539s] -- Added test 'uncrustify' to check C / C++ code style
[2.540s] -- Configured uncrustify additional arguments: 
[2.540s] -- Added test 'xmllint' to check XML markup files
[2.555s] -- Configuring done
[2.603s] -- Generating done
[2.609s] -- Build files have been written to: /home/<USER>/xugong_web_ws/build/web
[2.624s] Invoked command in '/home/<USER>/xugong_web_ws/build/web' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/web -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/web
[2.625s] Invoking command in '/home/<USER>/xugong_web_ws/build/web': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/web -- -j16 -l16
[2.680s] [ 50%] [32mBuilding CXX object CMakeFiles/web_node.dir/src/web_node.cpp.o[0m
[8.727s] In file included from [01m[K/home/<USER>/xugong_web_ws/src/web/src/web_node.cpp:3[m[K:
[8.727s] [01m[K/home/<USER>/xugong_web_ws/src/web/src/websocket_server.h:[m[K In member function ‘[01m[Kvoid WebSocketServerSingleton::avoidance_remind_callback(std_msgs::msg::Int8_<std::allocator<void> >::SharedPtr)[m[K’:
[8.727s] [01m[K/home/<USER>/xugong_web_ws/src/web/src/websocket_server.h:1270:73:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kmsg[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[8.727s]  1270 |     void avoidance_remind_callback([01;35m[Kconst std_msgs::msg::Int8::SharedPtr msg[m[K)
[8.727s]       |                                    [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~[m[K
[55.224s] [100%] [32m[1mLinking CXX executable web_node[0m
[60.794s] [100%] Built target web_node
[60.812s] Invoked command in '/home/<USER>/xugong_web_ws/build/web' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/web -- -j16 -l16
[60.815s] Invoking command in '/home/<USER>/xugong_web_ws/build/web': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/web
[60.829s] -- Install configuration: ""
[60.829s] -- Installing: /home/<USER>/xugong_web_ws/install/web/lib/web/web_node
[60.856s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/web/lib/web/web_node" to ""
[60.856s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/launch
[60.856s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/launch/web_launch.py
[60.857s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/package_run_dependencies/web
[60.857s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/parent_prefix_path/web
[60.857s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/environment/ament_prefix_path.sh
[60.857s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/environment/ament_prefix_path.dsv
[60.857s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/environment/path.sh
[60.857s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/environment/path.dsv
[60.857s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.bash
[60.857s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.sh
[60.857s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.zsh
[60.857s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.dsv
[60.857s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/package.dsv
[60.858s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/packages/web
[60.858s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/cmake/webConfig.cmake
[60.858s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/cmake/webConfig-version.cmake
[60.858s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/package.xml
[60.860s] Invoked command in '/home/<USER>/xugong_web_ws/build/web' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/web
