-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") 
-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
-- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") 
-- Using PYTHON_EXECUTABLE: /usr/bin/python3
-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
-- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so
-- Found PythonExtra: .so  
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/xugong_web_ws/build/keyframe_msgs
[  1%] [34m[1mGenerating C code for ROS interfaces[0m
[  2%] [34m[1mGenerating C++ code for ROS interfaces[0m
[  2%] Built target ament_cmake_python_copy_keyframe_msgs
running egg_info
creating keyframe_msgs.egg-info
writing keyframe_msgs.egg-info/PKG-INFO
writing dependency_links to keyframe_msgs.egg-info/dependency_links.txt
writing top-level names to keyframe_msgs.egg-info/top_level.txt
writing manifest file 'keyframe_msgs.egg-info/SOURCES.txt'
reading manifest file 'keyframe_msgs.egg-info/SOURCES.txt'
writing manifest file 'keyframe_msgs.egg-info/SOURCES.txt'
[  2%] Built target ament_cmake_python_build_keyframe_msgs_egg
[  2%] Built target keyframe_msgs__cpp
[  3%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[  4%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[  5%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[  6%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/key_frame__functions.c.o[0m
[  7%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/key_frame_pose_array__functions.c.o[0m
[  8%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/key_frame_pose__functions.c.o[0m
[  9%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/vehicle_control_flags__functions.c.o[0m
[ 10%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/joystick_state__functions.c.o[0m
[ 12%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/vehicle_diagnostic__functions.c.o[0m
[ 12%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/vehicle_motion__functions.c.o[0m
[ 13%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/key_state__functions.c.o[0m
[ 14%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/lateral_deviation__functions.c.o[0m
[ 15%] [32m[1mLinking C shared library libkeyframe_msgs__rosidl_generator_c.so[0m
[ 15%] Built target keyframe_msgs__rosidl_generator_c
[ 16%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[ 17%] [34m[1mGenerating C introspection for ROS interfaces[0m
[ 18%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
[ 20%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/vehicle_diagnostic__type_support.cpp.o[0m
[ 23%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/vehicle_control_flags__type_support.cpp.o[0m
[ 21%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/key_frame_pose_array__type_support.cpp.o[0m
[ 22%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/key_frame__type_support.cpp.o[0m
[ 24%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/key_frame_pose__type_support.cpp.o[0m
[ 26%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/vehicle_motion__type_support.cpp.o[0m
[ 25%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/joystick_state__type_support.cpp.o[0m
[ 27%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/key_state__type_support.cpp.o[0m
[ 28%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/lateral_deviation__type_support.cpp.o[0m
[ 29%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/key_frame_pose_array__type_support.cpp.o[0m
[ 30%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/key_frame_pose__type_support.cpp.o[0m
[ 31%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/key_frame__type_support.cpp.o[0m
[ 32%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/vehicle_control_flags__type_support.cpp.o[0m
[ 33%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/vehicle_diagnostic__type_support.cpp.o[0m
[ 34%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/vehicle_motion__type_support.cpp.o[0m
[ 35%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/joystick_state__type_support.cpp.o[0m
[ 36%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/key_state__type_support.cpp.o[0m
[ 37%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/lateral_deviation__type_support.cpp.o[0m
[ 38%] [32m[1mLinking CXX shared library libkeyframe_msgs__rosidl_typesupport_cpp.so[0m
[ 38%] Built target keyframe_msgs__rosidl_typesupport_cpp
[ 40%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/key_frame__type_support.cpp.o[0m
[ 41%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/key_frame_pose_array__type_support.cpp.o[0m
[ 42%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/key_frame_pose__type_support.cpp.o[0m
[ 43%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/key_frame__type_support.c.o[0m
[ 44%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/vehicle_control_flags__type_support.cpp.o[0m
[ 45%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/key_frame_pose__type_support.c.o[0m
[ 46%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/vehicle_diagnostic__type_support.cpp.o[0m
[ 47%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/vehicle_control_flags__type_support.c.o[0m
[ 48%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/key_frame_pose_array__type_support.c.o[0m
[ 49%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/key_frame__type_support.cpp.o[0m
[ 50%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/vehicle_diagnostic__type_support.c.o[0m
[ 51%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/vehicle_motion__type_support.cpp.o[0m
[ 52%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/key_frame_pose__type_support.cpp.o[0m
[ 53%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/joystick_state__type_support.cpp.o[0m
[ 54%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/key_state__type_support.cpp.o[0m
[ 55%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/vehicle_motion__type_support.c.o[0m
[ 56%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/key_state__type_support.c.o[0m
[ 57%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/joystick_state__type_support.c.o[0m
[ 58%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/lateral_deviation__type_support.cpp.o[0m
[ 60%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/key_frame__type_support_c.cpp.o[0m
[ 61%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/key_frame_pose_array__type_support.cpp.o[0m
[ 62%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/vehicle_control_flags__type_support.cpp.o[0m
[ 63%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/lateral_deviation__type_support.c.o[0m
[ 64%] [32m[1mLinking CXX shared library libkeyframe_msgs__rosidl_typesupport_c.so[0m
[ 65%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/key_frame_pose__type_support_c.cpp.o[0m
[ 66%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/key_frame_pose_array__type_support_c.cpp.o[0m
[ 67%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/vehicle_diagnostic__type_support.cpp.o[0m
[ 68%] [32m[1mLinking C shared library libkeyframe_msgs__rosidl_typesupport_introspection_c.so[0m
[ 69%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/vehicle_motion__type_support.cpp.o[0m
[ 70%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/joystick_state__type_support.cpp.o[0m
[ 71%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/vehicle_control_flags__type_support_c.cpp.o[0m
[ 71%] Built target keyframe_msgs__rosidl_typesupport_c
[ 72%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/vehicle_diagnostic__type_support_c.cpp.o[0m
[ 72%] Built target keyframe_msgs__rosidl_typesupport_introspection_c
[ 73%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/vehicle_motion__type_support_c.cpp.o[0m
[ 74%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/key_state__type_support.cpp.o[0m
[ 75%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/joystick_state__type_support_c.cpp.o[0m
[ 76%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/key_state__type_support_c.cpp.o[0m
[ 77%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/lateral_deviation__type_support_c.cpp.o[0m
[ 78%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/lateral_deviation__type_support.cpp.o[0m
[ 80%] [32m[1mLinking CXX shared library libkeyframe_msgs__rosidl_typesupport_introspection_cpp.so[0m
[ 80%] Built target keyframe_msgs__rosidl_typesupport_introspection_cpp
[ 81%] [32m[1mLinking CXX shared library libkeyframe_msgs__rosidl_typesupport_fastrtps_c.so[0m
[ 82%] [32m[1mLinking CXX shared library libkeyframe_msgs__rosidl_typesupport_fastrtps_cpp.so[0m
[ 82%] Built target keyframe_msgs__rosidl_typesupport_fastrtps_c
[ 82%] Built target keyframe_msgs__rosidl_typesupport_fastrtps_cpp
[ 82%] Built target keyframe_msgs
[ 83%] [34m[1mGenerating Python code for ROS interfaces[0m
