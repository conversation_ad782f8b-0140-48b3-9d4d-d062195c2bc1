-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
-- Found FastRTPS: /opt/ros/humble/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
-- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)
-- Found hv: /usr/local/include  
-- Found nlohmann_json: /usr/lib/cmake/nlohmann_json/nlohmann_jsonConfig.cmake (found version "3.10.5") 
-- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)
-- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Found Eigen3: TRUE (found version "3.4.0") 
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Checking for module 'eigen3'
--   Found eigen3, version 3.4.0
-- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") 
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found suitable version "1.74.0", minimum required is "1.65.0") found components: system filesystem date_time iostreams serialization 
-- Checking for module 'libusb-1.0'
--   Found libusb-1.0, version 1.0.25
-- Found libusb: /usr/lib/x86_64-linux-gnu/libusb-1.0.so  
-- Found OpenNI: /usr/lib/libOpenNI.so;libusb::libusb (found version "1.5.4.0") 
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- Found OpenNI2: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb (found version "2.2.0.33") 
-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
[0m** WARNING ** io features related to pcap will be disabled[0m
[0m** WARNING ** io features related to png will be disabled[0m
-- Found GLEW: /usr/lib/x86_64-linux-gnu/libGLEW.so  
-- Found OpenGL: /usr/lib/x86_64-linux-gnu/libOpenGL.so  found components: OpenGL GLX 
-- Found MPI_C: /usr/lib/x86_64-linux-gnu/libmpi.so (found version "3.1") 
-- Found MPI: TRUE (found version "3.1") found components: C 
-- Found JsonCpp: /usr/lib/x86_64-linux-gnu/libjsoncpp.so (found suitable version "1.9.5", minimum required is "0.7.0") 
-- Found ZLIB: /usr/lib/x86_64-linux-gnu/libz.so (found version "1.2.11") 
-- Found PNG: /usr/lib/x86_64-linux-gnu/libpng.so (found version "1.6.37") 
-- Found Eigen3: /usr/include/eigen3 (found version "3.4.0") 
-- Found X11: /usr/include   
-- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so
-- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so - found
-- Looking for gethostbyname
-- Looking for gethostbyname - found
-- Looking for connect
-- Looking for connect - found
-- Looking for remove
-- Looking for remove - found
-- Looking for shmat
-- Looking for shmat - found
-- Looking for IceConnectionNumber in ICE
-- Looking for IceConnectionNumber in ICE - found
-- Found EXPAT: /usr/lib/x86_64-linux-gnu/libexpat.so (found version "2.4.7") 
-- Found double-conversion: /usr/lib/x86_64-linux-gnu/libdouble-conversion.so  
-- Found LZ4: /usr/lib/x86_64-linux-gnu/liblz4.so (found version "1.9.3") 
-- Found LZMA: /usr/lib/x86_64-linux-gnu/liblzma.so (found version "5.2.5") 
-- Found JPEG: /usr/lib/x86_64-linux-gnu/libjpeg.so (found version "80") 
-- Found TIFF: /usr/lib/x86_64-linux-gnu/libtiff.so (found version "4.3.0")  
-- Found Freetype: /usr/lib/x86_64-linux-gnu/libfreetype.so (found version "2.11.1") 
-- Found utf8cpp: /usr/include/utf8cpp  
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- Checking for module 'flann'
--   Found flann, version 1.9.1
-- Found FLANN: /usr/lib/x86_64-linux-gnu/libflann_cpp.so  
-- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)
-- looking for PCL_COMMON
-- Found PCL_COMMON: /usr/lib/x86_64-linux-gnu/libpcl_common.so  
-- looking for PCL_OCTREE
-- Found PCL_OCTREE: /usr/lib/x86_64-linux-gnu/libpcl_octree.so  
-- looking for PCL_IO
-- Found PCL_IO: /usr/lib/x86_64-linux-gnu/libpcl_io.so  
-- looking for PCL_KDTREE
-- Found PCL_KDTREE: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so  
-- looking for PCL_SEARCH
-- Found PCL_SEARCH: /usr/lib/x86_64-linux-gnu/libpcl_search.so  
-- looking for PCL_SAMPLE_CONSENSUS
-- Found PCL_SAMPLE_CONSENSUS: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so  
-- looking for PCL_FILTERS
-- Found PCL_FILTERS: /usr/lib/x86_64-linux-gnu/libpcl_filters.so  
-- Found PCL: pcl_common;pcl_octree;pcl_io;pcl_kdtree;pcl_search;pcl_sample_consensus;pcl_filters;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;/usr/lib/libOpenNI.so;libusb::libusb;/usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb;VTK::ChartsCore;VTK::CommonColor;VTK::CommonComputationalGeometry;VTK::CommonCore;VTK::CommonDataModel;VTK::CommonExecutionModel;VTK::CommonMath;VTK::CommonMisc;VTK::CommonTransforms;VTK::FiltersCore;VTK::FiltersExtraction;VTK::FiltersGeneral;VTK::FiltersGeometry;VTK::FiltersModeling;VTK::FiltersSources;VTK::ImagingCore;VTK::ImagingSources;VTK::InteractionImage;VTK::InteractionStyle;VTK::InteractionWidgets;VTK::IOCore;VTK::IOGeometry;VTK::IOImage;VTK::IOLegacy;VTK::IOPLY;VTK::RenderingAnnotation;VTK::RenderingCore;VTK::RenderingContext2D;VTK::RenderingLOD;VTK::RenderingFreeType;VTK::ViewsCore;VTK::ViewsContext2D;VTK::RenderingOpenGL2;VTK::GUISupportQt;FLANN::FLANN  
-- Found pcl_conversions: 2.4.5 (/opt/ros/humble/share/pcl_conversions/cmake)
-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
-- Found keyframe_msgs: 0.0.0 (/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake)
-- Found can_bridge: 0.0.0 (/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake)
-- Found remotecontrol_msgs: 0.0.0 (/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake)
-- Found global_traj_generate: 0.0.0 (/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake)
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: 
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/xugong_web_ws/build/web
[ 50%] [32mBuilding CXX object CMakeFiles/web_node.dir/src/web_node.cpp.o[0m
In file included from [01m[K/home/<USER>/xugong_web_ws/src/web/src/web_node.cpp:3[m[K:
[01m[K/home/<USER>/xugong_web_ws/src/web/src/websocket_server.h:[m[K In member function ‘[01m[Kvoid WebSocketServerSingleton::avoidance_remind_callback(std_msgs::msg::Int8_<std::allocator<void> >::SharedPtr)[m[K’:
[01m[K/home/<USER>/xugong_web_ws/src/web/src/websocket_server.h:1270:73:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kmsg[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
 1270 |     void avoidance_remind_callback([01;35m[Kconst std_msgs::msg::Int8::SharedPtr msg[m[K)
      |                                    [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~[m[K
[100%] [32m[1mLinking CXX executable web_node[0m
[100%] Built target web_node
-- Install configuration: ""
-- Installing: /home/<USER>/xugong_web_ws/install/web/lib/web/web_node
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/web/lib/web/web_node" to ""
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/launch
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/launch/web_launch.py
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/package_run_dependencies/web
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/parent_prefix_path/web
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/environment/path.sh
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/environment/path.dsv
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.bash
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.sh
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.zsh
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.dsv
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/package.dsv
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/packages/web
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/cmake/webConfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/cmake/webConfig-version.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/package.xml
