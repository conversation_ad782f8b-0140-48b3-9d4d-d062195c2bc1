from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import SetEnvironmentVariable
from ament_index_python.packages import get_package_share_directory
import os

def generate_launch_description():
    config_file = os.path.join(
        get_package_share_directory('web'),
        'config',
        'params.yaml'
    )

    parameters = []
    if os.path.isfile(config_file):
        parameters.append(config_file)

    return LaunchDescription([
        # Suppress D-Bus warnings
        SetEnvironmentVariable('DBUS_FATAL_WARNINGS', '0'),
        SetEnvironmentVariable('G_MESSAGES_DEBUG', ''),

        Node(
            package='web',
            executable='web_node',
            name='web_node',
            parameters=parameters,
            output='screen',
            # Additional environment variables to suppress D-Bus errors
            environment={
                'DBUS_FATAL_WARNINGS': '0',
                'G_MESSAGES_DEBUG': ''
            }
        )
    ])
