[0.018s] Invoking command in '/home/<USER>/xugong_web_ws/build/can_bridge': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/can_bridge -- -j16 -l16
[0.080s] [  2%] Built target can_bridge__cpp
[0.087s] [ 12%] Built target can_bridge__rosidl_generator_c
[0.093s] [ 12%] Built target ament_cmake_python_copy_can_bridge
[0.111s] [ 23%] Built target can_bridge__rosidl_typesupport_fastrtps_cpp
[0.117s] [ 34%] Built target can_bridge__rosidl_typesupport_introspection_cpp
[0.121s] [ 44%] Built target can_bridge__rosidl_typesupport_cpp
[0.121s] [ 55%] Built target can_bridge__rosidl_typesupport_c
[0.122s] [ 65%] Built target can_bridge__rosidl_typesupport_introspection_c
[0.127s] [ 76%] Built target can_bridge__rosidl_typesupport_fastrtps_c
[0.140s] [ 76%] Built target can_bridge
[0.155s] [ 78%] Built target can_bridge__py
[0.173s] [ 87%] Built target can_bridge__rosidl_generator_py
[0.192s] [ 91%] Built target can_bridge__rosidl_typesupport_fastrtps_c__pyext
[0.193s] [ 95%] Built target can_bridge__rosidl_typesupport_c__pyext
[0.195s] [100%] Built target can_bridge__rosidl_typesupport_introspection_c__pyext
[0.281s] running egg_info
[0.282s] writing can_bridge.egg-info/PKG-INFO
[0.282s] writing dependency_links to can_bridge.egg-info/dependency_links.txt
[0.282s] writing top-level names to can_bridge.egg-info/top_level.txt
[0.283s] reading manifest file 'can_bridge.egg-info/SOURCES.txt'
[0.284s] writing manifest file 'can_bridge.egg-info/SOURCES.txt'
[0.311s] [100%] Built target ament_cmake_python_build_can_bridge_egg
[0.329s] Invoked command in '/home/<USER>/xugong_web_ws/build/can_bridge' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/can_bridge -- -j16 -l16
[0.330s] Invoking command in '/home/<USER>/xugong_web_ws/build/can_bridge': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/can_bridge
[0.343s] -- Install configuration: ""
[0.344s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/rosidl_interfaces/can_bridge
[0.345s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
[0.345s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
[0.345s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_diagnostic.h
[0.345s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_generator_c__visibility_control.h
[0.345s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_motion.h
[0.345s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_control_flags.h
[0.345s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
[0.345s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.h
[0.345s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.h
[0.345s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__functions.h
[0.346s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__struct.h
[0.346s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__functions.c
[0.346s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.h
[0.346s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__functions.c
[0.347s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__functions.c
[0.347s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__struct.h
[0.347s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__functions.h
[0.347s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__functions.h
[0.347s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__struct.h
[0.348s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/library_path.sh
[0.348s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/library_path.dsv
[0.348s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_generator_c.so
[0.348s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
[0.348s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
[0.349s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
[0.349s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
[0.349s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_fastrtps_c.h
[0.349s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_fastrtps_c.h
[0.349s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_fastrtps_c.h
[0.349s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_fastrtps_c.so
[0.349s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
[0.349s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
[0.349s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_control_flags.hpp
[0.349s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_generator_cpp__visibility_control.hpp
[0.349s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_diagnostic.hpp
[0.350s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
[0.350s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__traits.hpp
[0.350s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.hpp
[0.350s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__traits.hpp
[0.350s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.hpp
[0.350s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__traits.hpp
[0.350s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__struct.hpp
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__builder.hpp
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__builder.hpp
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.hpp
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__builder.hpp
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__struct.hpp
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__struct.hpp
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_motion.hpp
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/dds_fastrtps
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_fastrtps_cpp.hpp
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_fastrtps_cpp.hpp
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_fastrtps_cpp.hpp
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_fastrtps_cpp.so
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
[0.351s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_typesupport_introspection_c__visibility_control.h
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_introspection_c.h
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_introspection_c.h
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_introspection_c.h
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.c
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.c
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.c
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_introspection_c.so
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_c.so
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.cpp
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_introspection_cpp.hpp
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.cpp
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_introspection_cpp.hpp
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.cpp
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_introspection_cpp.hpp
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_introspection_cpp.so
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_cpp.so
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/pythonpath.sh
[0.352s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/pythonpath.dsv
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info
[0.353s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/dependency_links.txt
[0.353s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/SOURCES.txt
[0.353s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/top_level.txt
[0.353s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/PKG-INFO
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/_can_bridge_s.ep.rosidl_typesupport_c.c
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/_can_bridge_s.ep.rosidl_typesupport_fastrtps_c.c
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_diagnostic_s.c
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_motion_s.c
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_motion.py
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_diagnostic.py
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_control_flags_s.c
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_control_flags.py
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/__init__.py
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/__init__.py
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/libcan_bridge__rosidl_generator_py.so
[0.353s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/_can_bridge_s.ep.rosidl_typesupport_introspection_c.c
[0.368s] Listing '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge'...
[0.368s] Listing '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg'...
[0.371s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
[0.372s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
[0.372s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
[0.372s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_generator_py.so
[0.374s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleControlFlags.idl
[0.374s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleDiagnostic.idl
[0.374s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleMotion.idl
[0.374s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleControlFlags.msg
[0.374s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleDiagnostic.msg
[0.374s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleMotion.msg
[0.374s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/package_run_dependencies/can_bridge
[0.374s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/parent_prefix_path/can_bridge
[0.374s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/ament_prefix_path.sh
[0.375s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/ament_prefix_path.dsv
[0.375s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/path.sh
[0.375s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/path.dsv
[0.375s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.bash
[0.375s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.sh
[0.375s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.zsh
[0.375s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.dsv
[0.376s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.dsv
[0.376s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/packages/can_bridge
[0.376s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_cExport.cmake
[0.376s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_cExport-noconfig.cmake
[0.376s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cExport.cmake
[0.376s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cExport-noconfig.cmake
[0.377s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_cppExport.cmake
[0.377s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cppExport.cmake
[0.377s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake
[0.377s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cExport.cmake
[0.377s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cExport-noconfig.cmake
[0.377s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cExport.cmake
[0.377s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cExport-noconfig.cmake
[0.377s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cppExport.cmake
[0.377s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cppExport-noconfig.cmake
[0.377s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cppExport.cmake
[0.377s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cppExport-noconfig.cmake
[0.378s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_pyExport.cmake
[0.378s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_pyExport-noconfig.cmake
[0.378s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/rosidl_cmake-extras.cmake
[0.378s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_dependencies-extras.cmake
[0.378s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_include_directories-extras.cmake
[0.378s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_libraries-extras.cmake
[0.378s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_targets-extras.cmake
[0.378s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
[0.379s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
[0.379s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridgeConfig.cmake
[0.379s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridgeConfig-version.cmake
[0.379s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.xml
[0.405s] Invoked command in '/home/<USER>/xugong_web_ws/build/can_bridge' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/can_bridge
