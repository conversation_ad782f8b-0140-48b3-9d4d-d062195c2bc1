[35m[1mConsolidate compiler generated dependencies of target web_node[0m
[100%] Built target web_node
-- Install configuration: ""
-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/lib/web/web_node
-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/launch
-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/launch/web_launch.py
-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/package_run_dependencies/web
-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/parent_prefix_path/web
-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/environment/ament_prefix_path.sh
-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/environment/path.sh
-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/environment/path.dsv
-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.bash
-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.sh
-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.zsh
-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.dsv
-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/package.dsv
-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/packages/web
-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/cmake/webConfig.cmake
-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/cmake/webConfig-version.cmake
-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/package.xml
