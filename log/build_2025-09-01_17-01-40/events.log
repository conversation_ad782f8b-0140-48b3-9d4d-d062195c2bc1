[0.000000] (-) TimerEvent: {}
[0.000653] (can_bridge) JobQueued: {'identifier': 'can_bridge', 'dependencies': OrderedDict()}
[0.000702] (global_traj_generate) JobQueued: {'identifier': 'global_traj_generate', 'dependencies': OrderedDict()}
[0.000751] (keyframe_msgs) JobQueued: {'identifier': 'keyframe_msgs', 'dependencies': OrderedDict()}
[0.000784] (remotecontrol_msgs) JobQueued: {'identifier': 'remotecontrol_msgs', 'dependencies': OrderedDict()}
[0.000815] (web) JobQueued: {'identifier': 'web', 'dependencies': OrderedDict([('can_bridge', '/home/<USER>/xugong_web_ws/install/can_bridge'), ('global_traj_generate', '/home/<USER>/xugong_web_ws/install/global_traj_generate'), ('keyframe_msgs', '/home/<USER>/xugong_web_ws/install/keyframe_msgs'), ('remotecontrol_msgs', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs')])}
[0.000923] (can_bridge) JobStarted: {'identifier': 'can_bridge'}
[0.004552] (global_traj_generate) JobStarted: {'identifier': 'global_traj_generate'}
[0.007208] (keyframe_msgs) JobStarted: {'identifier': 'keyframe_msgs'}
[0.009747] (remotecontrol_msgs) JobStarted: {'identifier': 'remotecontrol_msgs'}
[0.013364] (can_bridge) JobProgress: {'identifier': 'can_bridge', 'progress': 'cmake'}
[0.013870] (can_bridge) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/xugong_web_ws/src/can_bridge', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/can_bridge'], 'cwd': '/home/<USER>/xugong_web_ws/build/can_bridge', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/can_bridge'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[0.016066] (global_traj_generate) JobProgress: {'identifier': 'global_traj_generate', 'progress': 'cmake'}
[0.016248] (global_traj_generate) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/xugong_web_ws/src/global_traj_generate', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/global_traj_generate'], 'cwd': '/home/<USER>/xugong_web_ws/build/global_traj_generate', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/global_traj_generate'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[0.017445] (keyframe_msgs) JobProgress: {'identifier': 'keyframe_msgs', 'progress': 'cmake'}
[0.017663] (keyframe_msgs) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/xugong_web_ws/src/keyframe_msgs', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/keyframe_msgs'], 'cwd': '/home/<USER>/xugong_web_ws/build/keyframe_msgs', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/keyframe_msgs'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[0.019918] (remotecontrol_msgs) JobProgress: {'identifier': 'remotecontrol_msgs', 'progress': 'cmake'}
[0.020222] (remotecontrol_msgs) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/xugong_web_ws/src/remotecontrol_msgs', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs'], 'cwd': '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[0.099664] (-) TimerEvent: {}
[0.113399] (keyframe_msgs) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.113891] (can_bridge) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.114037] (remotecontrol_msgs) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.115054] (global_traj_generate) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.180306] (remotecontrol_msgs) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.181077] (can_bridge) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.181222] (global_traj_generate) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.181845] (keyframe_msgs) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.188144] (global_traj_generate) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.188328] (can_bridge) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.188405] (keyframe_msgs) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.188454] (remotecontrol_msgs) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.199738] (-) TimerEvent: {}
[0.238287] (can_bridge) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.238514] (global_traj_generate) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.238634] (keyframe_msgs) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.243598] (can_bridge) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.243831] (global_traj_generate) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.244924] (global_traj_generate) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.244990] (can_bridge) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.245369] (can_bridge) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.245412] (global_traj_generate) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.246675] (keyframe_msgs) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.246814] (keyframe_msgs) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.247121] (keyframe_msgs) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.248034] (can_bridge) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.248080] (global_traj_generate) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.249337] (keyframe_msgs) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.250071] (remotecontrol_msgs) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.255235] (remotecontrol_msgs) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.255384] (remotecontrol_msgs) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.255656] (remotecontrol_msgs) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.257903] (remotecontrol_msgs) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.299858] (-) TimerEvent: {}
[0.302843] (can_bridge) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.304042] (keyframe_msgs) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.308907] (can_bridge) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.309142] (can_bridge) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.309552] (can_bridge) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.310041] (global_traj_generate) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.312088] (keyframe_msgs) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.312422] (keyframe_msgs) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.312977] (keyframe_msgs) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.315506] (keyframe_msgs) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.315649] (can_bridge) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.317578] (global_traj_generate) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.317820] (global_traj_generate) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.318109] (global_traj_generate) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.320110] (global_traj_generate) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.321489] (remotecontrol_msgs) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.326839] (remotecontrol_msgs) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.327062] (remotecontrol_msgs) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.327427] (remotecontrol_msgs) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.329577] (remotecontrol_msgs) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.400057] (-) TimerEvent: {}
[0.424923] (can_bridge) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[0.434674] (keyframe_msgs) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[0.438299] (global_traj_generate) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[0.442724] (remotecontrol_msgs) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[0.500169] (-) TimerEvent: {}
[0.500809] (can_bridge) StdoutLine: {'line': b'-- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)\n'}
[0.500974] (global_traj_generate) StdoutLine: {'line': b'-- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)\n'}
[0.501184] (keyframe_msgs) StdoutLine: {'line': b'-- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)\n'}
[0.519992] (remotecontrol_msgs) StdoutLine: {'line': b'-- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)\n'}
[0.530732] (can_bridge) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.530891] (keyframe_msgs) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.530945] (global_traj_generate) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.535043] (keyframe_msgs) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.535164] (global_traj_generate) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.535207] (can_bridge) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.542222] (can_bridge) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.542334] (keyframe_msgs) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.542379] (global_traj_generate) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.544701] (remotecontrol_msgs) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.548333] (remotecontrol_msgs) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.552234] (global_traj_generate) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.552430] (keyframe_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.552537] (can_bridge) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.554060] (remotecontrol_msgs) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.562136] (remotecontrol_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.565378] (can_bridge) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.565585] (global_traj_generate) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.565695] (keyframe_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.572819] (remotecontrol_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.585086] (can_bridge) StdoutLine: {'line': b'-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)\n'}
[0.585248] (global_traj_generate) StdoutLine: {'line': b'-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)\n'}
[0.585358] (keyframe_msgs) StdoutLine: {'line': b'-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)\n'}
[0.590065] (remotecontrol_msgs) StdoutLine: {'line': b'-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)\n'}
[0.595019] (can_bridge) StdoutLine: {'line': b'-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[0.595167] (global_traj_generate) StdoutLine: {'line': b'-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[0.595270] (keyframe_msgs) StdoutLine: {'line': b'-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[0.598777] (remotecontrol_msgs) StdoutLine: {'line': b'-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[0.600229] (-) TimerEvent: {}
[0.608116] (can_bridge) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)\n'}
[0.608298] (keyframe_msgs) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)\n'}
[0.608444] (global_traj_generate) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)\n'}
[0.610326] (remotecontrol_msgs) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)\n'}
[0.700326] (-) TimerEvent: {}
[0.800594] (-) TimerEvent: {}
[0.838757] (can_bridge) StdoutLine: {'line': b'-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)\n'}
[0.842124] (global_traj_generate) StdoutLine: {'line': b'-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)\n'}
[0.868111] (remotecontrol_msgs) StdoutLine: {'line': b'-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)\n'}
[0.889623] (keyframe_msgs) StdoutLine: {'line': b'-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)\n'}
[0.900668] (-) TimerEvent: {}
[1.000892] (-) TimerEvent: {}
[1.101171] (-) TimerEvent: {}
[1.201397] (-) TimerEvent: {}
[1.201846] (can_bridge) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.204532] (remotecontrol_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.208707] (global_traj_generate) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.234889] (keyframe_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.301527] (-) TimerEvent: {}
[1.335186] (global_traj_generate) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.337165] (remotecontrol_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.339368] (can_bridge) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.353131] (global_traj_generate) StdoutLine: {'line': b'-- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") \n'}
[1.355443] (global_traj_generate) StdoutLine: {'line': b'-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)\n'}
[1.357514] (remotecontrol_msgs) StdoutLine: {'line': b'-- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") \n'}
[1.358765] (remotecontrol_msgs) StdoutLine: {'line': b'-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)\n'}
[1.358969] (can_bridge) StdoutLine: {'line': b'-- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") \n'}
[1.360106] (can_bridge) StdoutLine: {'line': b'-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)\n'}
[1.366897] (keyframe_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.378110] (remotecontrol_msgs) StdoutLine: {'line': b'-- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") \n'}
[1.378259] (remotecontrol_msgs) StdoutLine: {'line': b'-- Using PYTHON_EXECUTABLE: /usr/bin/python3\n'}
[1.378303] (remotecontrol_msgs) StdoutLine: {'line': b'-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10\n'}
[1.378340] (remotecontrol_msgs) StdoutLine: {'line': b'-- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so\n'}
[1.378652] (global_traj_generate) StdoutLine: {'line': b'-- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") \n'}
[1.378718] (global_traj_generate) StdoutLine: {'line': b'-- Using PYTHON_EXECUTABLE: /usr/bin/python3\n'}
[1.378785] (global_traj_generate) StdoutLine: {'line': b'-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10\n'}
[1.378822] (global_traj_generate) StdoutLine: {'line': b'-- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so\n'}
[1.380830] (can_bridge) StdoutLine: {'line': b'-- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") \n'}
[1.380878] (can_bridge) StdoutLine: {'line': b'-- Using PYTHON_EXECUTABLE: /usr/bin/python3\n'}
[1.380910] (can_bridge) StdoutLine: {'line': b'-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10\n'}
[1.380942] (can_bridge) StdoutLine: {'line': b'-- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so\n'}
[1.383802] (keyframe_msgs) StdoutLine: {'line': b'-- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") \n'}
[1.384953] (keyframe_msgs) StdoutLine: {'line': b'-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)\n'}
[1.394365] (global_traj_generate) StdoutLine: {'line': b'-- Found PythonExtra: .so  \n'}
[1.394981] (remotecontrol_msgs) StdoutLine: {'line': b'-- Found PythonExtra: .so  \n'}
[1.395113] (can_bridge) StdoutLine: {'line': b'-- Found PythonExtra: .so  \n'}
[1.401606] (-) TimerEvent: {}
[1.402773] (keyframe_msgs) StdoutLine: {'line': b'-- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") \n'}
[1.402947] (keyframe_msgs) StdoutLine: {'line': b'-- Using PYTHON_EXECUTABLE: /usr/bin/python3\n'}
[1.403055] (keyframe_msgs) StdoutLine: {'line': b'-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10\n'}
[1.403154] (keyframe_msgs) StdoutLine: {'line': b'-- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so\n'}
[1.420826] (keyframe_msgs) StdoutLine: {'line': b'-- Found PythonExtra: .so  \n'}
[1.446766] (remotecontrol_msgs) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[1.447071] (can_bridge) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[1.447197] (global_traj_generate) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[1.464898] (global_traj_generate) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[1.465174] (remotecontrol_msgs) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[1.465629] (global_traj_generate) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[1.465775] (remotecontrol_msgs) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[1.467450] (remotecontrol_msgs) StdoutLine: {'line': b'-- Configuring done\n'}
[1.467549] (global_traj_generate) StdoutLine: {'line': b'-- Configuring done\n'}
[1.469202] (can_bridge) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[1.470433] (can_bridge) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[1.473345] (can_bridge) StdoutLine: {'line': b'-- Configuring done\n'}
[1.474543] (keyframe_msgs) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[1.486983] (keyframe_msgs) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[1.487577] (keyframe_msgs) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[1.488896] (keyframe_msgs) StdoutLine: {'line': b'-- Configuring done\n'}
[1.489818] (global_traj_generate) StdoutLine: {'line': b'-- Generating done\n'}
[1.490427] (remotecontrol_msgs) StdoutLine: {'line': b'-- Generating done\n'}
[1.497776] (global_traj_generate) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/xugong_web_ws/build/global_traj_generate\n'}
[1.498211] (remotecontrol_msgs) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/xugong_web_ws/build/remotecontrol_msgs\n'}
[1.499474] (can_bridge) StdoutLine: {'line': b'-- Generating done\n'}
[1.501678] (-) TimerEvent: {}
[1.504742] (global_traj_generate) CommandEnded: {'returncode': 0}
[1.505450] (global_traj_generate) JobProgress: {'identifier': 'global_traj_generate', 'progress': 'build'}
[1.506799] (global_traj_generate) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/xugong_web_ws/build/global_traj_generate', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/xugong_web_ws/build/global_traj_generate', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/global_traj_generate'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[1.507795] (remotecontrol_msgs) CommandEnded: {'returncode': 0}
[1.507980] (can_bridge) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/xugong_web_ws/build/can_bridge\n'}
[1.508168] (remotecontrol_msgs) JobProgress: {'identifier': 'remotecontrol_msgs', 'progress': 'build'}
[1.508501] (remotecontrol_msgs) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[1.515457] (can_bridge) CommandEnded: {'returncode': 0}
[1.515893] (can_bridge) JobProgress: {'identifier': 'can_bridge', 'progress': 'build'}
[1.516186] (can_bridge) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/xugong_web_ws/build/can_bridge', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/xugong_web_ws/build/can_bridge', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/can_bridge'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[1.520918] (keyframe_msgs) StdoutLine: {'line': b'-- Generating done\n'}
[1.530314] (keyframe_msgs) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/xugong_web_ws/build/keyframe_msgs\n'}
[1.534563] (global_traj_generate) StdoutLine: {'line': b'[  3%] \x1b[34m\x1b[1mGenerating C code for ROS interfaces\x1b[0m\n'}
[1.539193] (keyframe_msgs) CommandEnded: {'returncode': 0}
[1.539931] (keyframe_msgs) JobProgress: {'identifier': 'keyframe_msgs', 'progress': 'build'}
[1.540419] (keyframe_msgs) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/xugong_web_ws/build/keyframe_msgs', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/xugong_web_ws/build/keyframe_msgs', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/keyframe_msgs'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[1.541917] (global_traj_generate) StdoutLine: {'line': b'[  6%] \x1b[34m\x1b[1mGenerating C++ code for ROS interfaces\x1b[0m\n'}
[1.542506] (remotecontrol_msgs) StdoutLine: {'line': b'[  2%] \x1b[34m\x1b[1mGenerating C code for ROS interfaces\x1b[0m\n'}
[1.548803] (can_bridge) StdoutLine: {'line': b'[  2%] \x1b[34m\x1b[1mGenerating C code for ROS interfaces\x1b[0m\n'}
[1.549017] (remotecontrol_msgs) StdoutLine: {'line': b'[  5%] \x1b[34m\x1b[1mGenerating C++ code for ROS interfaces\x1b[0m\n'}
[1.552686] (global_traj_generate) StdoutLine: {'line': b'[  6%] Built target ament_cmake_python_copy_global_traj_generate\n'}
[1.555423] (can_bridge) StdoutLine: {'line': b'[  4%] \x1b[34m\x1b[1mGenerating C++ code for ROS interfaces\x1b[0m\n'}
[1.560477] (remotecontrol_msgs) StdoutLine: {'line': b'[  5%] Built target ament_cmake_python_copy_remotecontrol_msgs\n'}
[1.567246] (can_bridge) StdoutLine: {'line': b'[  4%] Built target ament_cmake_python_copy_can_bridge\n'}
[1.587676] (keyframe_msgs) StdoutLine: {'line': b'[  1%] \x1b[34m\x1b[1mGenerating C code for ROS interfaces\x1b[0m\n'}
[1.596488] (keyframe_msgs) StdoutLine: {'line': b'[  2%] \x1b[34m\x1b[1mGenerating C++ code for ROS interfaces\x1b[0m\n'}
[1.601772] (-) TimerEvent: {}
[1.604405] (keyframe_msgs) StdoutLine: {'line': b'[  2%] Built target ament_cmake_python_copy_keyframe_msgs\n'}
[1.701858] (-) TimerEvent: {}
[1.729359] (can_bridge) StdoutLine: {'line': b'running egg_info\n'}
[1.729875] (can_bridge) StdoutLine: {'line': b'creating can_bridge.egg-info\n'}
[1.730099] (can_bridge) StdoutLine: {'line': b'writing can_bridge.egg-info/PKG-INFO\n'}
[1.730258] (can_bridge) StdoutLine: {'line': b'writing dependency_links to can_bridge.egg-info/dependency_links.txt\n'}
[1.730389] (can_bridge) StdoutLine: {'line': b'writing top-level names to can_bridge.egg-info/top_level.txt\n'}
[1.730596] (can_bridge) StdoutLine: {'line': b"writing manifest file 'can_bridge.egg-info/SOURCES.txt'\n"}
[1.732017] (can_bridge) StdoutLine: {'line': b"reading manifest file 'can_bridge.egg-info/SOURCES.txt'\n"}
[1.732414] (can_bridge) StdoutLine: {'line': b"writing manifest file 'can_bridge.egg-info/SOURCES.txt'\n"}
[1.760079] (can_bridge) StdoutLine: {'line': b'[  4%] Built target ament_cmake_python_build_can_bridge_egg\n'}
[1.800895] (global_traj_generate) StdoutLine: {'line': b'[  9%] \x1b[32mBuilding C object CMakeFiles/global_traj_generate__rosidl_generator_c.dir/rosidl_generator_c/global_traj_generate/msg/detail/lateral_deviation__functions.c.o\x1b[0m\n'}
[1.801939] (-) TimerEvent: {}
[1.818717] (keyframe_msgs) StdoutLine: {'line': b'running egg_info\n'}
[1.819201] (keyframe_msgs) StdoutLine: {'line': b'creating keyframe_msgs.egg-info\n'}
[1.819372] (keyframe_msgs) StdoutLine: {'line': b'writing keyframe_msgs.egg-info/PKG-INFO\n'}
[1.819524] (keyframe_msgs) StdoutLine: {'line': b'writing dependency_links to keyframe_msgs.egg-info/dependency_links.txt\n'}
[1.819632] (keyframe_msgs) StdoutLine: {'line': b'writing top-level names to keyframe_msgs.egg-info/top_level.txt\n'}
[1.819786] (keyframe_msgs) StdoutLine: {'line': b"writing manifest file 'keyframe_msgs.egg-info/SOURCES.txt'\n"}
[1.821354] (keyframe_msgs) StdoutLine: {'line': b"reading manifest file 'keyframe_msgs.egg-info/SOURCES.txt'\n"}
[1.821744] (keyframe_msgs) StdoutLine: {'line': b"writing manifest file 'keyframe_msgs.egg-info/SOURCES.txt'\n"}
[1.850522] (keyframe_msgs) StdoutLine: {'line': b'[  2%] Built target ament_cmake_python_build_keyframe_msgs_egg\n'}
[1.851345] (global_traj_generate) StdoutLine: {'line': b'running egg_info\n'}
[1.851782] (global_traj_generate) StdoutLine: {'line': b'creating global_traj_generate.egg-info\n'}
[1.855073] (global_traj_generate) StdoutLine: {'line': b'writing global_traj_generate.egg-info/PKG-INFO\n'}
[1.855251] (global_traj_generate) StdoutLine: {'line': b'writing dependency_links to global_traj_generate.egg-info/dependency_links.txt\n'}
[1.855317] (global_traj_generate) StdoutLine: {'line': b'writing top-level names to global_traj_generate.egg-info/top_level.txt\n'}
[1.855377] (global_traj_generate) StdoutLine: {'line': b"writing manifest file 'global_traj_generate.egg-info/SOURCES.txt'\n"}
[1.855440] (global_traj_generate) StdoutLine: {'line': b"reading manifest file 'global_traj_generate.egg-info/SOURCES.txt'\n"}
[1.855496] (global_traj_generate) StdoutLine: {'line': b"writing manifest file 'global_traj_generate.egg-info/SOURCES.txt'\n"}
[1.857376] (global_traj_generate) StdoutLine: {'line': b'[ 12%] \x1b[32m\x1b[1mLinking C shared library libglobal_traj_generate__rosidl_generator_c.so\x1b[0m\n'}
[1.877143] (global_traj_generate) StdoutLine: {'line': b'[ 12%] Built target global_traj_generate__cpp\n'}
[1.884826] (remotecontrol_msgs) StdoutLine: {'line': b'running egg_info\n'}
[1.887690] (remotecontrol_msgs) StdoutLine: {'line': b'creating remotecontrol_msgs.egg-info\n'}
[1.887905] (remotecontrol_msgs) StdoutLine: {'line': b'writing remotecontrol_msgs.egg-info/PKG-INFO\n'}
[1.888002] (remotecontrol_msgs) StdoutLine: {'line': b'writing dependency_links to remotecontrol_msgs.egg-info/dependency_links.txt\n'}
[1.888119] (remotecontrol_msgs) StdoutLine: {'line': b'writing top-level names to remotecontrol_msgs.egg-info/top_level.txt\n'}
[1.888207] (remotecontrol_msgs) StdoutLine: {'line': b"writing manifest file 'remotecontrol_msgs.egg-info/SOURCES.txt'\n"}
[1.890254] (remotecontrol_msgs) StdoutLine: {'line': b"reading manifest file 'remotecontrol_msgs.egg-info/SOURCES.txt'\n"}
[1.891326] (remotecontrol_msgs) StdoutLine: {'line': b"writing manifest file 'remotecontrol_msgs.egg-info/SOURCES.txt'\n"}
[1.891878] (global_traj_generate) StdoutLine: {'line': b'[ 16%] \x1b[34m\x1b[1mGenerating C++ type support for eProsima Fast-RTPS\x1b[0m\n'}
[1.891978] (global_traj_generate) StdoutLine: {'line': b'[ 16%] Built target ament_cmake_python_build_global_traj_generate_egg\n'}
[1.894415] (global_traj_generate) StdoutLine: {'line': b'[ 19%] \x1b[34m\x1b[1mGenerating C++ type support dispatch for ROS interfaces\x1b[0m\n'}
[1.895593] (global_traj_generate) StdoutLine: {'line': b'[ 22%] \x1b[34m\x1b[1mGenerating C++ introspection for ROS interfaces\x1b[0m\n'}
[1.901026] (can_bridge) StdoutLine: {'line': b'[  4%] Built target can_bridge__cpp\n'}
[1.901193] (global_traj_generate) StdoutLine: {'line': b'[ 22%] Built target global_traj_generate__rosidl_generator_c\n'}
[1.903008] (-) TimerEvent: {}
[1.905275] (can_bridge) StdoutLine: {'line': b'[  8%] \x1b[32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_c.dir/rosidl_generator_c/can_bridge/msg/detail/vehicle_control_flags__functions.c.o\x1b[0m\n'}
[1.905750] (can_bridge) StdoutLine: {'line': b'[ 10%] \x1b[32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_c.dir/rosidl_generator_c/can_bridge/msg/detail/vehicle_diagnostic__functions.c.o\x1b[0m\n'}
[1.907709] (can_bridge) StdoutLine: {'line': b'[ 10%] \x1b[32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_c.dir/rosidl_generator_c/can_bridge/msg/detail/vehicle_motion__functions.c.o\x1b[0m\n'}
[1.908424] (global_traj_generate) StdoutLine: {'line': b'[ 25%] \x1b[34m\x1b[1mGenerating C type support for eProsima Fast-RTPS\x1b[0m\n'}
[1.910457] (can_bridge) StdoutLine: {'line': b'[ 12%] \x1b[34m\x1b[1mGenerating C++ type support for eProsima Fast-RTPS\x1b[0m\n'}
[1.911248] (global_traj_generate) StdoutLine: {'line': b'[ 29%] \x1b[34m\x1b[1mGenerating C type support dispatch for ROS interfaces\x1b[0m\n'}
[1.912974] (can_bridge) StdoutLine: {'line': b'[ 14%] \x1b[34m\x1b[1mGenerating C++ type support dispatch for ROS interfaces\x1b[0m\n'}
[1.916304] (global_traj_generate) StdoutLine: {'line': b'[ 32%] \x1b[34m\x1b[1mGenerating C introspection for ROS interfaces\x1b[0m\n'}
[1.916577] (can_bridge) StdoutLine: {'line': b'[ 17%] \x1b[34m\x1b[1mGenerating C++ introspection for ROS interfaces\x1b[0m\n'}
[1.960066] (remotecontrol_msgs) StdoutLine: {'line': b'[  5%] Built target ament_cmake_python_build_remotecontrol_msgs_egg\n'}
[1.963711] (can_bridge) StdoutLine: {'line': b'[ 19%] \x1b[32m\x1b[1mLinking C shared library libcan_bridge__rosidl_generator_c.so\x1b[0m\n'}
[2.003049] (-) TimerEvent: {}
[2.010855] (can_bridge) StdoutLine: {'line': b'[ 19%] Built target can_bridge__rosidl_generator_c\n'}
[2.025520] (can_bridge) StdoutLine: {'line': b'[ 21%] \x1b[34m\x1b[1mGenerating C type support for eProsima Fast-RTPS\x1b[0m\n'}
[2.026078] (can_bridge) StdoutLine: {'line': b'[ 23%] \x1b[34m\x1b[1mGenerating C type support dispatch for ROS interfaces\x1b[0m\n'}
[2.026701] (can_bridge) StdoutLine: {'line': b'[ 25%] \x1b[34m\x1b[1mGenerating C introspection for ROS interfaces\x1b[0m\n'}
[2.103519] (-) TimerEvent: {}
[2.116357] (remotecontrol_msgs) StdoutLine: {'line': b'[  7%] \x1b[32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_generator_c.dir/rosidl_generator_c/remotecontrol_msgs/msg/detail/key_state__functions.c.o\x1b[0m\n'}
[2.119419] (remotecontrol_msgs) StdoutLine: {'line': b'[ 10%] \x1b[32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_generator_c.dir/rosidl_generator_c/remotecontrol_msgs/msg/detail/joystick_state__functions.c.o\x1b[0m\n'}
[2.165988] (remotecontrol_msgs) StdoutLine: {'line': b'[ 10%] Built target remotecontrol_msgs__cpp\n'}
[2.183664] (remotecontrol_msgs) StdoutLine: {'line': b'[ 15%] \x1b[34m\x1b[1mGenerating C++ type support dispatch for ROS interfaces\x1b[0m\n'}
[2.183833] (remotecontrol_msgs) StdoutLine: {'line': b'[ 15%] \x1b[34m\x1b[1mGenerating C++ introspection for ROS interfaces\x1b[0m\n'}
[2.185069] (remotecontrol_msgs) StdoutLine: {'line': b'[ 17%] \x1b[34m\x1b[1mGenerating C++ type support for eProsima Fast-RTPS\x1b[0m\n'}
[2.201068] (global_traj_generate) StdoutLine: {'line': b'[ 35%] \x1b[32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/global_traj_generate/msg/detail/lateral_deviation__type_support_c.cpp.o\x1b[0m\n'}
[2.203608] (-) TimerEvent: {}
[2.206198] (remotecontrol_msgs) StdoutLine: {'line': b'[ 20%] \x1b[32m\x1b[1mLinking C shared library libremotecontrol_msgs__rosidl_generator_c.so\x1b[0m\n'}
[2.227326] (global_traj_generate) StdoutLine: {'line': b'[ 38%] \x1b[32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/global_traj_generate/msg/lateral_deviation__type_support.cpp.o\x1b[0m\n'}
[2.237739] (can_bridge) StdoutLine: {'line': b'[ 27%] \x1b[32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/can_bridge/msg/vehicle_control_flags__type_support.cpp.o\x1b[0m\n'}
[2.248013] (can_bridge) StdoutLine: {'line': b'[ 29%] \x1b[32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/can_bridge/msg/vehicle_motion__type_support.cpp.o\x1b[0m\n'}
[2.250037] (global_traj_generate) StdoutLine: {'line': b'[ 41%] \x1b[32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_c.dir/rosidl_typesupport_c/global_traj_generate/msg/lateral_deviation__type_support.cpp.o\x1b[0m\n'}
[2.250561] (can_bridge) StdoutLine: {'line': b'[ 31%] \x1b[32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/can_bridge/msg/vehicle_diagnostic__type_support.cpp.o\x1b[0m\n'}
[2.255596] (remotecontrol_msgs) StdoutLine: {'line': b'[ 20%] Built target remotecontrol_msgs__rosidl_generator_c\n'}
[2.269350] (remotecontrol_msgs) StdoutLine: {'line': b'[ 23%] \x1b[34m\x1b[1mGenerating C introspection for ROS interfaces\x1b[0m\n'}
[2.276363] (global_traj_generate) StdoutLine: {'line': b'[ 45%] \x1b[32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/global_traj_generate/msg/detail/lateral_deviation__type_support.c.o\x1b[0m\n'}
[2.277089] (remotecontrol_msgs) StdoutLine: {'line': b'[ 25%] \x1b[34m\x1b[1mGenerating C type support dispatch for ROS interfaces\x1b[0m\n'}
[2.277611] (remotecontrol_msgs) StdoutLine: {'line': b'[ 28%] \x1b[34m\x1b[1mGenerating C type support for eProsima Fast-RTPS\x1b[0m\n'}
[2.298785] (global_traj_generate) StdoutLine: {'line': b'[ 48%] \x1b[32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/global_traj_generate/msg/detail/lateral_deviation__type_support.cpp.o\x1b[0m\n'}
[2.303706] (-) TimerEvent: {}
[2.305108] (global_traj_generate) StdoutLine: {'line': b'[ 51%] \x1b[32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/global_traj_generate/msg/detail/dds_fastrtps/lateral_deviation__type_support.cpp.o\x1b[0m\n'}
[2.325120] (global_traj_generate) StdoutLine: {'line': b'[ 54%] \x1b[32m\x1b[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_c.so\x1b[0m\n'}
[2.337202] (can_bridge) StdoutLine: {'line': b'[ 34%] \x1b[32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/can_bridge/msg/detail/vehicle_control_flags__type_support.cpp.o\x1b[0m\n'}
[2.340704] (can_bridge) StdoutLine: {'line': b'[ 36%] \x1b[32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/can_bridge/msg/detail/vehicle_motion__type_support.cpp.o\x1b[0m\n'}
[2.342294] (can_bridge) StdoutLine: {'line': b'[ 38%] \x1b[32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/can_bridge/msg/detail/vehicle_diagnostic__type_support.cpp.o\x1b[0m\n'}
[2.352417] (can_bridge) StdoutLine: {'line': b'[ 40%] \x1b[32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/can_bridge/msg/detail/dds_fastrtps/vehicle_motion__type_support.cpp.o\x1b[0m\n'}
[2.355065] (global_traj_generate) StdoutLine: {'line': b'[ 58%] \x1b[32m\x1b[1mLinking C shared library libglobal_traj_generate__rosidl_typesupport_introspection_c.so\x1b[0m\n'}
[2.356072] (can_bridge) StdoutLine: {'line': b'[ 42%] \x1b[32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/can_bridge/msg/detail/dds_fastrtps/vehicle_diagnostic__type_support.cpp.o\x1b[0m\n'}
[2.358740] (can_bridge) StdoutLine: {'line': b'[ 44%] \x1b[32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/can_bridge/msg/detail/dds_fastrtps/vehicle_control_flags__type_support.cpp.o\x1b[0m\n'}
[2.403804] (-) TimerEvent: {}
[2.405269] (can_bridge) StdoutLine: {'line': b'[ 48%] \x1b[32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_c.dir/rosidl_typesupport_c/can_bridge/msg/vehicle_control_flags__type_support.cpp.o\x1b[0m\n'}
[2.405530] (can_bridge) StdoutLine: {'line': b'[ 48%] \x1b[32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_c.dir/rosidl_typesupport_c/can_bridge/msg/vehicle_diagnostic__type_support.cpp.o\x1b[0m\n'}
[2.408535] (can_bridge) StdoutLine: {'line': b'[ 51%] \x1b[32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_c.dir/rosidl_typesupport_c/can_bridge/msg/vehicle_motion__type_support.cpp.o\x1b[0m\n'}
[2.416608] (global_traj_generate) StdoutLine: {'line': b'[ 58%] Built target global_traj_generate__rosidl_typesupport_c\n'}
[2.444582] (global_traj_generate) StdoutLine: {'line': b'[ 58%] Built target global_traj_generate__rosidl_typesupport_introspection_c\n'}
[2.453489] (can_bridge) StdoutLine: {'line': b'[ 53%] \x1b[32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/can_bridge/msg/detail/vehicle_control_flags__type_support_c.cpp.o\x1b[0m\n'}
[2.460114] (can_bridge) StdoutLine: {'line': b'[ 55%] \x1b[32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/can_bridge/msg/detail/vehicle_diagnostic__type_support_c.cpp.o\x1b[0m\n'}
[2.478136] (can_bridge) StdoutLine: {'line': b'[ 57%] \x1b[32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/can_bridge/msg/detail/vehicle_motion__type_support_c.cpp.o\x1b[0m\n'}
[2.480729] (can_bridge) StdoutLine: {'line': b'[ 59%] \x1b[32m\x1b[1mLinking CXX shared library libcan_bridge__rosidl_typesupport_c.so\x1b[0m\n'}
[2.503919] (-) TimerEvent: {}
[2.521332] (can_bridge) StdoutLine: {'line': b'[ 61%] \x1b[32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/can_bridge/msg/detail/vehicle_control_flags__type_support.c.o\x1b[0m\n'}
[2.534297] (can_bridge) StdoutLine: {'line': b'[ 63%] \x1b[32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/can_bridge/msg/detail/vehicle_motion__type_support.c.o\x1b[0m\n'}
[2.534919] (can_bridge) StdoutLine: {'line': b'[ 65%] \x1b[32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/can_bridge/msg/detail/vehicle_diagnostic__type_support.c.o\x1b[0m\n'}
[2.580486] (can_bridge) StdoutLine: {'line': b'[ 65%] Built target can_bridge__rosidl_typesupport_c\n'}
[2.604028] (-) TimerEvent: {}
[2.608771] (can_bridge) StdoutLine: {'line': b'[ 68%] \x1b[32m\x1b[1mLinking C shared library libcan_bridge__rosidl_typesupport_introspection_c.so\x1b[0m\n'}
[2.686297] (can_bridge) StdoutLine: {'line': b'[ 68%] Built target can_bridge__rosidl_typesupport_introspection_c\n'}
[2.704197] (-) TimerEvent: {}
[2.777281] (global_traj_generate) StdoutLine: {'line': b'[ 61%] \x1b[32m\x1b[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_cpp.so\x1b[0m\n'}
[2.810060] (-) TimerEvent: {}
[2.865131] (can_bridge) StdoutLine: {'line': b'[ 70%] \x1b[32m\x1b[1mLinking CXX shared library libcan_bridge__rosidl_typesupport_cpp.so\x1b[0m\n'}
[2.897129] (global_traj_generate) StdoutLine: {'line': b'[ 61%] Built target global_traj_generate__rosidl_typesupport_cpp\n'}
[2.910181] (-) TimerEvent: {}
[2.954751] (global_traj_generate) StdoutLine: {'line': b'[ 64%] \x1b[32m\x1b[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_fastrtps_cpp.so\x1b[0m\n'}
[2.977159] (remotecontrol_msgs) StdoutLine: {'line': b'[ 30%] \x1b[32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/remotecontrol_msgs/msg/detail/key_state__type_support.cpp.o\x1b[0m\n'}
[2.977937] (remotecontrol_msgs) StdoutLine: {'line': b'[ 33%] \x1b[32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/remotecontrol_msgs/msg/detail/joystick_state__type_support.cpp.o\x1b[0m\n'}
[2.979226] (can_bridge) StdoutLine: {'line': b'[ 70%] Built target can_bridge__rosidl_typesupport_cpp\n'}
[2.995408] (global_traj_generate) StdoutLine: {'line': b'[ 67%] \x1b[32m\x1b[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_fastrtps_c.so\x1b[0m\n'}
[3.010297] (-) TimerEvent: {}
[3.041650] (keyframe_msgs) StdoutLine: {'line': b'[  2%] Built target keyframe_msgs__cpp\n'}
[3.068103] (keyframe_msgs) StdoutLine: {'line': b'[  3%] \x1b[34m\x1b[1mGenerating C++ introspection for ROS interfaces\x1b[0m\n'}
[3.069069] (keyframe_msgs) StdoutLine: {'line': b'[  4%] \x1b[34m\x1b[1mGenerating C++ type support for eProsima Fast-RTPS\x1b[0m\n'}
[3.072934] (keyframe_msgs) StdoutLine: {'line': b'[  5%] \x1b[34m\x1b[1mGenerating C++ type support dispatch for ROS interfaces\x1b[0m\n'}
[3.092098] (keyframe_msgs) StdoutLine: {'line': b'[  6%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/key_frame__functions.c.o\x1b[0m\n'}
[3.092987] (keyframe_msgs) StdoutLine: {'line': b'[  7%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/key_frame_pose_array__functions.c.o\x1b[0m\n'}
[3.093998] (keyframe_msgs) StdoutLine: {'line': b'[  8%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/key_frame_pose__functions.c.o\x1b[0m\n'}
[3.104150] (keyframe_msgs) StdoutLine: {'line': b'[  9%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/vehicle_control_flags__functions.c.o\x1b[0m\n'}
[3.105789] (keyframe_msgs) StdoutLine: {'line': b'[ 10%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/joystick_state__functions.c.o\x1b[0m\n'}
[3.110398] (-) TimerEvent: {}
[3.116170] (keyframe_msgs) StdoutLine: {'line': b'[ 12%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/vehicle_diagnostic__functions.c.o\x1b[0m\n'}
[3.117272] (keyframe_msgs) StdoutLine: {'line': b'[ 12%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/vehicle_motion__functions.c.o\x1b[0m\n'}
[3.118778] (keyframe_msgs) StdoutLine: {'line': b'[ 13%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/key_state__functions.c.o\x1b[0m\n'}
[3.119678] (keyframe_msgs) StdoutLine: {'line': b'[ 14%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/lateral_deviation__functions.c.o\x1b[0m\n'}
[3.134895] (can_bridge) StdoutLine: {'line': b'[ 72%] \x1b[32m\x1b[1mLinking CXX shared library libcan_bridge__rosidl_typesupport_introspection_cpp.so\x1b[0m\n'}
[3.172498] (remotecontrol_msgs) StdoutLine: {'line': b'[ 35%] \x1b[32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/remotecontrol_msgs/msg/joystick_state__type_support.cpp.o\x1b[0m\n'}
[3.178153] (remotecontrol_msgs) StdoutLine: {'line': b'[ 41%] \x1b[32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/remotecontrol_msgs/msg/detail/joystick_state__type_support_c.cpp.o\x1b[0m\n'}
[3.178694] (remotecontrol_msgs) StdoutLine: {'line': b'[ 41%] \x1b[32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/remotecontrol_msgs/msg/detail/key_state__type_support_c.cpp.o\x1b[0m\n'}
[3.180020] (remotecontrol_msgs) StdoutLine: {'line': b'[ 43%] \x1b[32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/remotecontrol_msgs/msg/key_state__type_support.cpp.o\x1b[0m\n'}
[3.184391] (global_traj_generate) StdoutLine: {'line': b'[ 70%] \x1b[32m\x1b[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_introspection_cpp.so\x1b[0m\n'}
[3.210081] (global_traj_generate) StdoutLine: {'line': b'[ 70%] Built target global_traj_generate__rosidl_typesupport_fastrtps_cpp\n'}
[3.210773] (-) TimerEvent: {}
[3.213768] (can_bridge) StdoutLine: {'line': b'[ 74%] \x1b[32m\x1b[1mLinking CXX shared library libcan_bridge__rosidl_typesupport_fastrtps_c.so\x1b[0m\n'}
[3.226003] (can_bridge) StdoutLine: {'line': b'[ 74%] Built target can_bridge__rosidl_typesupport_introspection_cpp\n'}
[3.236110] (remotecontrol_msgs) StdoutLine: {'line': b'[ 46%] \x1b[32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/remotecontrol_msgs/msg/joystick_state__type_support.cpp.o\x1b[0m\n'}
[3.238952] (remotecontrol_msgs) StdoutLine: {'line': b'[ 51%] \x1b[32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/remotecontrol_msgs/msg/key_state__type_support.cpp.o\x1b[0m\n'}
[3.239155] (remotecontrol_msgs) StdoutLine: {'line': b'[ 48%] \x1b[32m\x1b[1mLinking CXX shared library libremotecontrol_msgs__rosidl_typesupport_c.so\x1b[0m\n'}
[3.240723] (remotecontrol_msgs) StdoutLine: {'line': b'[ 53%] \x1b[32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/remotecontrol_msgs/msg/detail/dds_fastrtps/joystick_state__type_support.cpp.o\x1b[0m\n'}
[3.243911] (global_traj_generate) StdoutLine: {'line': b'[ 70%] Built target global_traj_generate__rosidl_typesupport_fastrtps_c\n'}
[3.246573] (keyframe_msgs) StdoutLine: {'line': b'[ 15%] \x1b[32m\x1b[1mLinking C shared library libkeyframe_msgs__rosidl_generator_c.so\x1b[0m\n'}
[3.249570] (remotecontrol_msgs) StdoutLine: {'line': b'[ 56%] \x1b[32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/remotecontrol_msgs/msg/detail/dds_fastrtps/key_state__type_support.cpp.o\x1b[0m\n'}
[3.254903] (can_bridge) StdoutLine: {'line': b'[ 76%] \x1b[32m\x1b[1mLinking CXX shared library libcan_bridge__rosidl_typesupport_fastrtps_cpp.so\x1b[0m\n'}
[3.294694] (global_traj_generate) StdoutLine: {'line': b'[ 70%] Built target global_traj_generate__rosidl_typesupport_introspection_cpp\n'}
[3.305411] (remotecontrol_msgs) StdoutLine: {'line': b'[ 56%] Built target remotecontrol_msgs__rosidl_typesupport_c\n'}
[3.311135] (-) TimerEvent: {}
[3.318546] (global_traj_generate) StdoutLine: {'line': b'[ 70%] Built target global_traj_generate\n'}
[3.325081] (keyframe_msgs) StdoutLine: {'line': b'[ 15%] Built target keyframe_msgs__rosidl_generator_c\n'}
[3.331194] (can_bridge) StdoutLine: {'line': b'[ 76%] Built target can_bridge__rosidl_typesupport_fastrtps_c\n'}
[3.341353] (keyframe_msgs) StdoutLine: {'line': b'[ 16%] \x1b[34m\x1b[1mGenerating C type support for eProsima Fast-RTPS\x1b[0m\n'}
[3.341594] (can_bridge) StdoutLine: {'line': b'[ 76%] Built target can_bridge__rosidl_typesupport_fastrtps_cpp\n'}
[3.341751] (keyframe_msgs) StdoutLine: {'line': b'[ 17%] \x1b[34m\x1b[1mGenerating C introspection for ROS interfaces\x1b[0m\n'}
[3.350114] (keyframe_msgs) StdoutLine: {'line': b'[ 18%] \x1b[34m\x1b[1mGenerating C type support dispatch for ROS interfaces\x1b[0m\n'}
[3.357107] (global_traj_generate) StdoutLine: {'line': b'[ 74%] \x1b[34m\x1b[1mGenerating Python code for ROS interfaces\x1b[0m\n'}
[3.371046] (can_bridge) StdoutLine: {'line': b'[ 76%] Built target can_bridge\n'}
[3.402282] (can_bridge) StdoutLine: {'line': b'[ 78%] \x1b[34m\x1b[1mGenerating Python code for ROS interfaces\x1b[0m\n'}
[3.411237] (-) TimerEvent: {}
[3.511574] (-) TimerEvent: {}
[3.514483] (remotecontrol_msgs) StdoutLine: {'line': b'[ 58%] \x1b[32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/remotecontrol_msgs/msg/detail/key_state__type_support.c.o\x1b[0m\n'}
[3.516871] (remotecontrol_msgs) StdoutLine: {'line': b'[ 61%] \x1b[32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/remotecontrol_msgs/msg/detail/joystick_state__type_support.c.o\x1b[0m\n'}
[3.549883] (remotecontrol_msgs) StdoutLine: {'line': b'[ 64%] \x1b[32m\x1b[1mLinking C shared library libremotecontrol_msgs__rosidl_typesupport_introspection_c.so\x1b[0m\n'}
[3.582101] (remotecontrol_msgs) StdoutLine: {'line': b'[ 64%] Built target remotecontrol_msgs__rosidl_typesupport_introspection_c\n'}
[3.602640] (remotecontrol_msgs) StdoutLine: {'line': b'[ 66%] \x1b[32m\x1b[1mLinking CXX shared library libremotecontrol_msgs__rosidl_typesupport_cpp.so\x1b[0m\n'}
[3.611683] (-) TimerEvent: {}
[3.632767] (remotecontrol_msgs) StdoutLine: {'line': b'[ 66%] Built target remotecontrol_msgs__rosidl_typesupport_cpp\n'}
[3.696560] (remotecontrol_msgs) StdoutLine: {'line': b'[ 69%] \x1b[32m\x1b[1mLinking CXX shared library libremotecontrol_msgs__rosidl_typesupport_fastrtps_cpp.so\x1b[0m\n'}
[3.705406] (remotecontrol_msgs) StdoutLine: {'line': b'[ 71%] \x1b[32m\x1b[1mLinking CXX shared library libremotecontrol_msgs__rosidl_typesupport_introspection_cpp.so\x1b[0m\n'}
[3.712374] (-) TimerEvent: {}
[3.731397] (remotecontrol_msgs) StdoutLine: {'line': b'[ 74%] \x1b[32m\x1b[1mLinking CXX shared library libremotecontrol_msgs__rosidl_typesupport_fastrtps_c.so\x1b[0m\n'}
[3.754883] (remotecontrol_msgs) StdoutLine: {'line': b'[ 74%] Built target remotecontrol_msgs__rosidl_typesupport_introspection_cpp\n'}
[3.762768] (global_traj_generate) StdoutLine: {'line': b'[ 74%] Built target global_traj_generate__py\n'}
[3.776910] (global_traj_generate) StdoutLine: {'line': b'[ 77%] \x1b[32mBuilding C object CMakeFiles/global_traj_generate__rosidl_generator_py.dir/rosidl_generator_py/global_traj_generate/msg/_lateral_deviation_s.c.o\x1b[0m\n'}
[3.778971] (remotecontrol_msgs) StdoutLine: {'line': b'[ 74%] Built target remotecontrol_msgs__rosidl_typesupport_fastrtps_cpp\n'}
[3.812470] (-) TimerEvent: {}
[3.817742] (remotecontrol_msgs) StdoutLine: {'line': b'[ 74%] Built target remotecontrol_msgs__rosidl_typesupport_fastrtps_c\n'}
[3.835035] (remotecontrol_msgs) StdoutLine: {'line': b'[ 74%] Built target remotecontrol_msgs\n'}
[3.850154] (remotecontrol_msgs) StdoutLine: {'line': b'[ 76%] \x1b[34m\x1b[1mGenerating Python code for ROS interfaces\x1b[0m\n'}
[3.896679] (global_traj_generate) StdoutLine: {'line': b'[ 80%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/global_traj_generate/libglobal_traj_generate__rosidl_generator_py.so\x1b[0m\n'}
[3.898114] (keyframe_msgs) StdoutLine: {'line': b'[ 20%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/vehicle_diagnostic__type_support.cpp.o\x1b[0m\n'}
[3.898293] (keyframe_msgs) StdoutLine: {'line': b'[ 23%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/vehicle_control_flags__type_support.cpp.o\x1b[0m\n'}
[3.899709] (keyframe_msgs) StdoutLine: {'line': b'[ 21%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/key_frame_pose_array__type_support.cpp.o\x1b[0m\n'}
[3.899887] (keyframe_msgs) StdoutLine: {'line': b'[ 22%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/key_frame__type_support.cpp.o\x1b[0m\n'}
[3.900678] (keyframe_msgs) StdoutLine: {'line': b'[ 24%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/key_frame_pose__type_support.cpp.o\x1b[0m\n'}
[3.900928] (keyframe_msgs) StdoutLine: {'line': b'[ 26%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/vehicle_motion__type_support.cpp.o\x1b[0m\n'}
[3.902270] (keyframe_msgs) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/joystick_state__type_support.cpp.o\x1b[0m\n'}
[3.902405] (keyframe_msgs) StdoutLine: {'line': b'[ 27%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/key_state__type_support.cpp.o\x1b[0m\n'}
[3.911490] (keyframe_msgs) StdoutLine: {'line': b'[ 28%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/lateral_deviation__type_support.cpp.o\x1b[0m\n'}
[3.912551] (-) TimerEvent: {}
[3.955628] (global_traj_generate) StdoutLine: {'line': b'[ 80%] Built target global_traj_generate__rosidl_generator_py\n'}
[4.010162] (global_traj_generate) StdoutLine: {'line': b'[ 83%] \x1b[32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_c__pyext.dir/rosidl_generator_py/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_fastrtps_c.c.o\x1b[0m\n'}
[4.010428] (global_traj_generate) StdoutLine: {'line': b'[ 87%] \x1b[32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_c__pyext.dir/rosidl_generator_py/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_introspection_c.c.o\x1b[0m\n'}
[4.020087] (-) TimerEvent: {}
[4.042357] (global_traj_generate) StdoutLine: {'line': b'[ 90%] \x1b[32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_c__pyext.dir/rosidl_generator_py/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_c.c.o\x1b[0m\n'}
[4.054195] (can_bridge) StdoutLine: {'line': b'[ 78%] Built target can_bridge__py\n'}
[4.093507] (keyframe_msgs) StdoutLine: {'line': b'[ 29%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/key_frame_pose_array__type_support.cpp.o\x1b[0m\n'}
[4.094448] (keyframe_msgs) StdoutLine: {'line': b'[ 30%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/key_frame_pose__type_support.cpp.o\x1b[0m\n'}
[4.094929] (keyframe_msgs) StdoutLine: {'line': b'[ 31%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/key_frame__type_support.cpp.o\x1b[0m\n'}
[4.110266] (can_bridge) StdoutLine: {'line': b'[ 80%] \x1b[32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_py.dir/rosidl_generator_py/can_bridge/msg/_vehicle_diagnostic_s.c.o\x1b[0m\n'}
[4.111109] (can_bridge) StdoutLine: {'line': b'[ 82%] \x1b[32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_py.dir/rosidl_generator_py/can_bridge/msg/_vehicle_motion_s.c.o\x1b[0m\n'}
[4.115226] (can_bridge) StdoutLine: {'line': b'[ 85%] \x1b[32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_py.dir/rosidl_generator_py/can_bridge/msg/_vehicle_control_flags_s.c.o\x1b[0m\n'}
[4.120184] (-) TimerEvent: {}
[4.220895] (-) TimerEvent: {}
[4.226136] (keyframe_msgs) StdoutLine: {'line': b'[ 32%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/vehicle_control_flags__type_support.cpp.o\x1b[0m\n'}
[4.236080] (global_traj_generate) StdoutLine: {'line': b'[ 93%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[4.241248] (global_traj_generate) StdoutLine: {'line': b'[ 96%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[4.247616] (global_traj_generate) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[4.280685] (keyframe_msgs) StdoutLine: {'line': b'[ 33%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/vehicle_diagnostic__type_support.cpp.o\x1b[0m\n'}
[4.309214] (keyframe_msgs) StdoutLine: {'line': b'[ 34%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/vehicle_motion__type_support.cpp.o\x1b[0m\n'}
[4.319091] (global_traj_generate) StdoutLine: {'line': b'[100%] Built target global_traj_generate__rosidl_typesupport_introspection_c__pyext\n'}
[4.319688] (global_traj_generate) StdoutLine: {'line': b'[100%] Built target global_traj_generate__rosidl_typesupport_fastrtps_c__pyext\n'}
[4.320982] (-) TimerEvent: {}
[4.321277] (global_traj_generate) StdoutLine: {'line': b'[100%] Built target global_traj_generate__rosidl_typesupport_c__pyext\n'}
[4.338872] (can_bridge) StdoutLine: {'line': b'[ 87%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/can_bridge/libcan_bridge__rosidl_generator_py.so\x1b[0m\n'}
[4.339386] (keyframe_msgs) StdoutLine: {'line': b'[ 35%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/joystick_state__type_support.cpp.o\x1b[0m\n'}
[4.358272] (global_traj_generate) CommandEnded: {'returncode': 0}
[4.358825] (global_traj_generate) JobProgress: {'identifier': 'global_traj_generate', 'progress': 'install'}
[4.358929] (keyframe_msgs) StdoutLine: {'line': b'[ 36%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/key_state__type_support.cpp.o\x1b[0m\n'}
[4.378870] (global_traj_generate) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/xugong_web_ws/build/global_traj_generate'], 'cwd': '/home/<USER>/xugong_web_ws/build/global_traj_generate', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/global_traj_generate'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[4.390542] (global_traj_generate) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[4.398867] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/ament_index/resource_index/rosidl_interfaces/global_traj_generate\n'}
[4.399087] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[4.399192] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[4.399281] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_generator_c__visibility_control.h\n'}
[4.399368] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/lateral_deviation.h\n'}
[4.399451] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[4.399532] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__functions.c\n'}
[4.399612] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__functions.h\n'}
[4.399692] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__type_support.h\n'}
[4.399769] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__struct.h\n'}
[4.399846] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/environment/library_path.sh\n'}
[4.399925] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/environment/library_path.dsv\n'}
[4.400001] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_c.so\n'}
[4.400093] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_c.so" to ""\n'}
[4.400189] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[4.400269] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[4.400351] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[4.400433] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[4.400515] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__rosidl_typesupport_fastrtps_c.h\n'}
[4.400597] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_c.so\n'}
[4.400676] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_c.so" to ""\n'}
[4.400757] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[4.400839] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[4.400920] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[4.412136] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/lateral_deviation.hpp\n'}
[4.412815] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[4.413351] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__traits.hpp\n'}
[4.413896] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__struct.hpp\n'}
[4.414435] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__type_support.hpp\n'}
[4.414991] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__builder.hpp\n'}
[4.415521] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[4.416033] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[4.416543] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[4.417322] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[4.419399] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[4.420045] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/dds_fastrtps\n'}
[4.420585] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_cpp.so\n'}
[4.421123] (-) TimerEvent: {}
[4.421387] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_cpp.so" to ""\n'}
[4.421992] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[4.422655] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[4.423245] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[4.425229] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[4.425374] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__rosidl_typesupport_introspection_c.h\n'}
[4.425481] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__type_support.c\n'}
[4.425578] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_c.so\n'}
[4.425667] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_c.so" to ""\n'}
[4.425754] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_c.so\n'}
[4.425838] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_c.so" to ""\n'}
[4.425918] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[4.425996] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[4.426093] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[4.426174] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__rosidl_typesupport_introspection_cpp.hpp\n'}
[4.426254] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__type_support.cpp\n'}
[4.426332] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_cpp.so\n'}
[4.426418] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_cpp.so" to ""\n'}
[4.426499] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_cpp.so\n'}
[4.426577] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_cpp.so" to ""\n'}
[4.426655] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/environment/pythonpath.sh\n'}
[4.428069] (keyframe_msgs) StdoutLine: {'line': b'[ 37%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/lateral_deviation__type_support.cpp.o\x1b[0m\n'}
[4.428253] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/environment/pythonpath.dsv\n'}
[4.428358] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info\n'}
[4.428444] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[4.428527] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[4.428606] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/top_level.txt\n'}
[4.428685] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[4.428762] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate\n'}
[4.428840] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_introspection_c.c\n'}
[4.431156] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[4.431299] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[4.431402] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/libglobal_traj_generate__rosidl_generator_py.so\n'}
[4.431493] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_c.c\n'}
[4.431577] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[4.431668] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[4.431751] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg\n'}
[4.431831] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_lateral_deviation.py\n'}
[4.431911] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_lateral_deviation_s.c\n'}
[4.431990] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/__init__.py\n'}
[4.432109] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/__init__.py\n'}
[4.432263] (can_bridge) StdoutLine: {'line': b'[ 87%] Built target can_bridge__rosidl_generator_py\n'}
[4.446569] (global_traj_generate) StdoutLine: {'line': b"Listing '/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate'...\n"}
[4.446867] (global_traj_generate) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/__init__.py'...\n"}
[4.446975] (global_traj_generate) StdoutLine: {'line': b"Listing '/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg'...\n"}
[4.447082] (global_traj_generate) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/__init__.py'...\n"}
[4.447174] (global_traj_generate) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_lateral_deviation.py'...\n"}
[4.453042] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[4.453338] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[4.453514] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[4.454303] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[4.458174] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[4.458314] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[4.458408] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_py.so\n'}
[4.458506] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_py.so" to ""\n'}
[4.458596] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/msg/LateralDeviation.idl\n'}
[4.458680] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/msg/LateralDeviation.msg\n'}
[4.458761] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/ament_index/resource_index/package_run_dependencies/global_traj_generate\n'}
[4.458844] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/ament_index/resource_index/parent_prefix_path/global_traj_generate\n'}
[4.458925] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/environment/ament_prefix_path.sh\n'}
[4.459004] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/environment/ament_prefix_path.dsv\n'}
[4.459099] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/environment/path.sh\n'}
[4.459178] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/environment/path.dsv\n'}
[4.459259] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/local_setup.bash\n'}
[4.459340] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/local_setup.sh\n'}
[4.459422] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/local_setup.zsh\n'}
[4.459504] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/local_setup.dsv\n'}
[4.459585] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.dsv\n'}
[4.459667] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/ament_index/resource_index/packages/global_traj_generate\n'}
[4.459752] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_cExport.cmake\n'}
[4.459838] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_cExport-noconfig.cmake\n'}
[4.459925] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[4.460457] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[4.460601] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_cppExport.cmake\n'}
[4.460688] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[4.460780] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[4.460874] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cExport.cmake\n'}
[4.460953] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[4.461045] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cExport.cmake\n'}
[4.461130] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cExport-noconfig.cmake\n'}
[4.461209] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cppExport.cmake\n'}
[4.461285] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[4.461500] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cppExport.cmake\n'}
[4.461595] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[4.461678] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_pyExport.cmake\n'}
[4.461758] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_pyExport-noconfig.cmake\n'}
[4.461840] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/rosidl_cmake-extras.cmake\n'}
[4.461926] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[4.462015] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[4.462113] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[4.462194] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_targets-extras.cmake\n'}
[4.462275] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[4.462354] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[4.462445] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generateConfig.cmake\n'}
[4.462522] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generateConfig-version.cmake\n'}
[4.462596] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.xml\n'}
[4.471888] (global_traj_generate) CommandEnded: {'returncode': 0}
[4.482164] (can_bridge) StdoutLine: {'line': b'[ 89%] \x1b[32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_c__pyext.dir/rosidl_generator_py/can_bridge/_can_bridge_s.ep.rosidl_typesupport_fastrtps_c.c.o\x1b[0m\n'}
[4.482861] (keyframe_msgs) StdoutLine: {'line': b'[ 38%] \x1b[32m\x1b[1mLinking CXX shared library libkeyframe_msgs__rosidl_typesupport_cpp.so\x1b[0m\n'}
[4.484160] (can_bridge) StdoutLine: {'line': b'[ 91%] \x1b[32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_introspection_c__pyext.dir/rosidl_generator_py/can_bridge/_can_bridge_s.ep.rosidl_typesupport_introspection_c.c.o\x1b[0m\n'}
[4.502089] (can_bridge) StdoutLine: {'line': b'[ 93%] \x1b[32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_c__pyext.dir/rosidl_generator_py/can_bridge/_can_bridge_s.ep.rosidl_typesupport_c.c.o\x1b[0m\n'}
[4.516665] (global_traj_generate) JobEnded: {'identifier': 'global_traj_generate', 'rc': 0}
[4.523461] (-) TimerEvent: {}
[4.594135] (keyframe_msgs) StdoutLine: {'line': b'[ 38%] Built target keyframe_msgs__rosidl_typesupport_cpp\n'}
[4.597657] (keyframe_msgs) StdoutLine: {'line': b'[ 40%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/key_frame__type_support.cpp.o\x1b[0m\n'}
[4.606239] (keyframe_msgs) StdoutLine: {'line': b'[ 41%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/key_frame_pose_array__type_support.cpp.o\x1b[0m\n'}
[4.615094] (can_bridge) StdoutLine: {'line': b'[ 95%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/can_bridge/can_bridge_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[4.616966] (keyframe_msgs) StdoutLine: {'line': b'[ 42%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/key_frame_pose__type_support.cpp.o\x1b[0m\n'}
[4.617211] (can_bridge) StdoutLine: {'line': b'[ 97%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/can_bridge/can_bridge_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[4.623576] (-) TimerEvent: {}
[4.631716] (keyframe_msgs) StdoutLine: {'line': b'[ 43%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/key_frame__type_support.c.o\x1b[0m\n'}
[4.642085] (keyframe_msgs) StdoutLine: {'line': b'[ 44%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/vehicle_control_flags__type_support.cpp.o\x1b[0m\n'}
[4.647204] (keyframe_msgs) StdoutLine: {'line': b'[ 45%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/key_frame_pose__type_support.c.o\x1b[0m\n'}
[4.654202] (keyframe_msgs) StdoutLine: {'line': b'[ 46%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/vehicle_diagnostic__type_support.cpp.o\x1b[0m\n'}
[4.657864] (keyframe_msgs) StdoutLine: {'line': b'[ 47%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/vehicle_control_flags__type_support.c.o\x1b[0m\n'}
[4.661092] (keyframe_msgs) StdoutLine: {'line': b'[ 48%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/key_frame_pose_array__type_support.c.o\x1b[0m\n'}
[4.665936] (keyframe_msgs) StdoutLine: {'line': b'[ 49%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/key_frame__type_support.cpp.o\x1b[0m\n'}
[4.668865] (keyframe_msgs) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/vehicle_diagnostic__type_support.c.o\x1b[0m\n'}
[4.669776] (keyframe_msgs) StdoutLine: {'line': b'[ 51%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/vehicle_motion__type_support.cpp.o\x1b[0m\n'}
[4.680147] (can_bridge) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/can_bridge/can_bridge_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[4.683058] (can_bridge) StdoutLine: {'line': b'[100%] Built target can_bridge__rosidl_typesupport_c__pyext\n'}
[4.691423] (can_bridge) StdoutLine: {'line': b'[100%] Built target can_bridge__rosidl_typesupport_fastrtps_c__pyext\n'}
[4.692076] (keyframe_msgs) StdoutLine: {'line': b'[ 52%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/key_frame_pose__type_support.cpp.o\x1b[0m\n'}
[4.701090] (keyframe_msgs) StdoutLine: {'line': b'[ 53%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/joystick_state__type_support.cpp.o\x1b[0m\n'}
[4.705820] (keyframe_msgs) StdoutLine: {'line': b'[ 54%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/key_state__type_support.cpp.o\x1b[0m\n'}
[4.712416] (keyframe_msgs) StdoutLine: {'line': b'[ 55%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/vehicle_motion__type_support.c.o\x1b[0m\n'}
[4.717266] (keyframe_msgs) StdoutLine: {'line': b'[ 56%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/key_state__type_support.c.o\x1b[0m\n'}
[4.720743] (keyframe_msgs) StdoutLine: {'line': b'[ 57%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/joystick_state__type_support.c.o\x1b[0m\n'}
[4.722004] (keyframe_msgs) StdoutLine: {'line': b'[ 58%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/lateral_deviation__type_support.cpp.o\x1b[0m\n'}
[4.722954] (keyframe_msgs) StdoutLine: {'line': b'[ 60%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/key_frame__type_support_c.cpp.o\x1b[0m\n'}
[4.723635] (-) TimerEvent: {}
[4.730237] (keyframe_msgs) StdoutLine: {'line': b'[ 61%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/key_frame_pose_array__type_support.cpp.o\x1b[0m\n'}
[4.749941] (keyframe_msgs) StdoutLine: {'line': b'[ 62%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/vehicle_control_flags__type_support.cpp.o\x1b[0m\n'}
[4.750882] (keyframe_msgs) StdoutLine: {'line': b'[ 63%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/lateral_deviation__type_support.c.o\x1b[0m\n'}
[4.757545] (can_bridge) StdoutLine: {'line': b'[100%] Built target can_bridge__rosidl_typesupport_introspection_c__pyext\n'}
[4.761138] (keyframe_msgs) StdoutLine: {'line': b'[ 64%] \x1b[32m\x1b[1mLinking CXX shared library libkeyframe_msgs__rosidl_typesupport_c.so\x1b[0m\n'}
[4.762088] (keyframe_msgs) StdoutLine: {'line': b'[ 65%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/key_frame_pose__type_support_c.cpp.o\x1b[0m\n'}
[4.775110] (keyframe_msgs) StdoutLine: {'line': b'[ 66%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/key_frame_pose_array__type_support_c.cpp.o\x1b[0m\n'}
[4.784129] (keyframe_msgs) StdoutLine: {'line': b'[ 67%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/vehicle_diagnostic__type_support.cpp.o\x1b[0m\n'}
[4.797228] (can_bridge) CommandEnded: {'returncode': 0}
[4.798491] (can_bridge) JobProgress: {'identifier': 'can_bridge', 'progress': 'install'}
[4.834249] (can_bridge) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/xugong_web_ws/build/can_bridge'], 'cwd': '/home/<USER>/xugong_web_ws/build/can_bridge', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/can_bridge'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[4.834831] (-) TimerEvent: {}
[4.834967] (keyframe_msgs) StdoutLine: {'line': b'[ 68%] \x1b[32m\x1b[1mLinking C shared library libkeyframe_msgs__rosidl_typesupport_introspection_c.so\x1b[0m\n'}
[4.835092] (keyframe_msgs) StdoutLine: {'line': b'[ 69%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/vehicle_motion__type_support.cpp.o\x1b[0m\n'}
[4.835153] (keyframe_msgs) StdoutLine: {'line': b'[ 70%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/joystick_state__type_support.cpp.o\x1b[0m\n'}
[4.836659] (can_bridge) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[4.837334] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/rosidl_interfaces/can_bridge\n'}
[4.837741] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge\n'}
[4.838131] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg\n'}
[4.838510] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_diagnostic.h\n'}
[4.838933] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_generator_c__visibility_control.h\n'}
[4.839361] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_motion.h\n'}
[4.839516] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_control_flags.h\n'}
[4.839579] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail\n'}
[4.839629] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.h\n'}
[4.839678] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.h\n'}
[4.839728] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__functions.h\n'}
[4.839778] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__struct.h\n'}
[4.839825] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__functions.c\n'}
[4.839873] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.h\n'}
[4.839922] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__functions.c\n'}
[4.839966] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__functions.c\n'}
[4.840028] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__struct.h\n'}
[4.840078] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__functions.h\n'}
[4.840125] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__functions.h\n'}
[4.840176] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__struct.h\n'}
[4.840228] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/library_path.sh\n'}
[4.840278] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/library_path.dsv\n'}
[4.840327] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_generator_c.so\n'}
[4.840376] (keyframe_msgs) StdoutLine: {'line': b'[ 71%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/vehicle_control_flags__type_support_c.cpp.o\x1b[0m\n'}
[4.842458] (keyframe_msgs) StdoutLine: {'line': b'[ 71%] Built target keyframe_msgs__rosidl_typesupport_c\n'}
[4.843215] (can_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_generator_c.so" to ""\n'}
[4.843358] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge\n'}
[4.843431] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg\n'}
[4.843510] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[4.843571] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail\n'}
[4.843648] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_fastrtps_c.h\n'}
[4.843707] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_fastrtps_c.h\n'}
[4.843779] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_fastrtps_c.h\n'}
[4.843858] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_fastrtps_c.so\n'}
[4.844122] (can_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_fastrtps_c.so" to ""\n'}
[4.844227] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge\n'}
[4.844288] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg\n'}
[4.844367] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_control_flags.hpp\n'}
[4.844425] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[4.844512] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_diagnostic.hpp\n'}
[4.844572] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail\n'}
[4.844625] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__traits.hpp\n'}
[4.844700] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.hpp\n'}
[4.844756] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__traits.hpp\n'}
[4.844830] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.hpp\n'}
[4.844889] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__traits.hpp\n'}
[4.844963] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__struct.hpp\n'}
[4.845023] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__builder.hpp\n'}
[4.845099] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__builder.hpp\n'}
[4.845154] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.hpp\n'}
[4.845220] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__builder.hpp\n'}
[4.845288] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__struct.hpp\n'}
[4.845359] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__struct.hpp\n'}
[4.845424] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_motion.hpp\n'}
[4.845530] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge\n'}
[4.845605] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg\n'}
[4.845655] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[4.845727] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail\n'}
[4.845778] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/dds_fastrtps\n'}
[4.845848] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[4.845898] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[4.845958] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[4.847095] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_fastrtps_cpp.so\n'}
[4.847197] (can_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_fastrtps_cpp.so" to ""\n'}
[4.847257] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge\n'}
[4.847305] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg\n'}
[4.847350] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[4.847394] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail\n'}
[4.847444] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_introspection_c.h\n'}
[4.847491] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_introspection_c.h\n'}
[4.847535] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_introspection_c.h\n'}
[4.847579] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.c\n'}
[4.847623] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.c\n'}
[4.847668] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.c\n'}
[4.847714] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_introspection_c.so\n'}
[4.847991] (can_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_introspection_c.so" to ""\n'}
[4.848081] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_c.so\n'}
[4.848130] (can_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_c.so" to ""\n'}
[4.848179] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge\n'}
[4.848226] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg\n'}
[4.850141] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail\n'}
[4.850236] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.cpp\n'}
[4.850294] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_introspection_cpp.hpp\n'}
[4.850349] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.cpp\n'}
[4.850398] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_introspection_cpp.hpp\n'}
[4.850456] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.cpp\n'}
[4.850504] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_introspection_cpp.hpp\n'}
[4.850554] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_introspection_cpp.so\n'}
[4.850728] (can_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_introspection_cpp.so" to ""\n'}
[4.850798] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_cpp.so\n'}
[4.850850] (can_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_cpp.so" to ""\n'}
[4.850904] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/pythonpath.sh\n'}
[4.850953] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/pythonpath.dsv\n'}
[4.851002] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info\n'}
[4.851056] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[4.851106] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[4.851153] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/top_level.txt\n'}
[4.851198] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[4.851244] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge\n'}
[4.851289] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/_can_bridge_s.ep.rosidl_typesupport_c.c\n'}
[4.851335] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[4.851382] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[4.851430] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[4.851478] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/_can_bridge_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[4.851527] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg\n'}
[4.851574] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_diagnostic_s.c\n'}
[4.851620] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_motion_s.c\n'}
[4.851666] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_motion.py\n'}
[4.851714] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_diagnostic.py\n'}
[4.851760] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_control_flags_s.c\n'}
[4.851809] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_control_flags.py\n'}
[4.851854] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/__init__.py\n'}
[4.851896] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/__init__.py\n'}
[4.851947] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/libcan_bridge__rosidl_generator_py.so\n'}
[4.851994] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/_can_bridge_s.ep.rosidl_typesupport_introspection_c.c\n'}
[4.869003] (keyframe_msgs) StdoutLine: {'line': b'[ 72%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/vehicle_diagnostic__type_support_c.cpp.o\x1b[0m\n'}
[4.893416] (keyframe_msgs) StdoutLine: {'line': b'[ 72%] Built target keyframe_msgs__rosidl_typesupport_introspection_c\n'}
[4.902669] (can_bridge) StdoutLine: {'line': b"Listing '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge'...\n"}
[4.902931] (can_bridge) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/__init__.py'...\n"}
[4.903026] (can_bridge) StdoutLine: {'line': b"Listing '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg'...\n"}
[4.903086] (can_bridge) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/__init__.py'...\n"}
[4.903139] (can_bridge) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_control_flags.py'...\n"}
[4.903189] (can_bridge) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_diagnostic.py'...\n"}
[4.903234] (can_bridge) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_motion.py'...\n"}
[4.910724] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[4.911076] (can_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[4.911218] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[4.911313] (can_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[4.911378] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[4.911457] (can_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[4.911536] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_generator_py.so\n'}
[4.911596] (can_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_generator_py.so" to ""\n'}
[4.911667] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleControlFlags.idl\n'}
[4.911734] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleDiagnostic.idl\n'}
[4.911787] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleMotion.idl\n'}
[4.911865] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleControlFlags.msg\n'}
[4.911920] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleDiagnostic.msg\n'}
[4.911971] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleMotion.msg\n'}
[4.912029] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/package_run_dependencies/can_bridge\n'}
[4.912081] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/parent_prefix_path/can_bridge\n'}
[4.912133] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/ament_prefix_path.sh\n'}
[4.912182] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/ament_prefix_path.dsv\n'}
[4.912232] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/path.sh\n'}
[4.912279] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/path.dsv\n'}
[4.912327] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.bash\n'}
[4.912376] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.sh\n'}
[4.912429] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.zsh\n'}
[4.912479] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.dsv\n'}
[4.912527] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.dsv\n'}
[4.912574] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/packages/can_bridge\n'}
[4.912621] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_cExport.cmake\n'}
[4.912669] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_cExport-noconfig.cmake\n'}
[4.912729] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[4.912780] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[4.912831] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_cppExport.cmake\n'}
[4.912952] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[4.913044] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[4.913119] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cExport.cmake\n'}
[4.913207] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[4.913281] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cExport.cmake\n'}
[4.913363] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cExport-noconfig.cmake\n'}
[4.913439] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cppExport.cmake\n'}
[4.913497] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[4.913569] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cppExport.cmake\n'}
[4.913644] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[4.913711] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_pyExport.cmake\n'}
[4.913762] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_pyExport-noconfig.cmake\n'}
[4.913840] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/rosidl_cmake-extras.cmake\n'}
[4.913906] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[4.913984] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[4.914044] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[4.914084] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_targets-extras.cmake\n'}
[4.914128] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[4.914171] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[4.914212] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridgeConfig.cmake\n'}
[4.914251] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridgeConfig-version.cmake\n'}
[4.914288] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.xml\n'}
[4.919353] (can_bridge) CommandEnded: {'returncode': 0}
[4.925982] (keyframe_msgs) StdoutLine: {'line': b'[ 73%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/vehicle_motion__type_support_c.cpp.o\x1b[0m\n'}
[4.934473] (can_bridge) JobEnded: {'identifier': 'can_bridge', 'rc': 0}
[4.935182] (-) TimerEvent: {}
[4.943514] (keyframe_msgs) StdoutLine: {'line': b'[ 74%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/key_state__type_support.cpp.o\x1b[0m\n'}
[4.981071] (keyframe_msgs) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/joystick_state__type_support_c.cpp.o\x1b[0m\n'}
[5.037082] (-) TimerEvent: {}
[5.137516] (-) TimerEvent: {}
[5.160386] (keyframe_msgs) StdoutLine: {'line': b'[ 76%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/key_state__type_support_c.cpp.o\x1b[0m\n'}
[5.164394] (keyframe_msgs) StdoutLine: {'line': b'[ 77%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/lateral_deviation__type_support_c.cpp.o\x1b[0m\n'}
[5.182799] (keyframe_msgs) StdoutLine: {'line': b'[ 78%] \x1b[32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/lateral_deviation__type_support.cpp.o\x1b[0m\n'}
[5.207134] (keyframe_msgs) StdoutLine: {'line': b'[ 80%] \x1b[32m\x1b[1mLinking CXX shared library libkeyframe_msgs__rosidl_typesupport_introspection_cpp.so\x1b[0m\n'}
[5.237633] (-) TimerEvent: {}
[5.338457] (-) TimerEvent: {}
[5.344019] (keyframe_msgs) StdoutLine: {'line': b'[ 80%] Built target keyframe_msgs__rosidl_typesupport_introspection_cpp\n'}
[5.344934] (remotecontrol_msgs) StdoutLine: {'line': b'[ 76%] Built target remotecontrol_msgs__py\n'}
[5.361943] (remotecontrol_msgs) StdoutLine: {'line': b'[ 79%] \x1b[32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_generator_py.dir/rosidl_generator_py/remotecontrol_msgs/msg/_key_state_s.c.o\x1b[0m\n'}
[5.363201] (remotecontrol_msgs) StdoutLine: {'line': b'[ 82%] \x1b[32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_generator_py.dir/rosidl_generator_py/remotecontrol_msgs/msg/_joystick_state_s.c.o\x1b[0m\n'}
[5.438582] (-) TimerEvent: {}
[5.441952] (remotecontrol_msgs) StdoutLine: {'line': b'[ 84%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/remotecontrol_msgs/libremotecontrol_msgs__rosidl_generator_py.so\x1b[0m\n'}
[5.468820] (keyframe_msgs) StdoutLine: {'line': b'[ 81%] \x1b[32m\x1b[1mLinking CXX shared library libkeyframe_msgs__rosidl_typesupport_fastrtps_c.so\x1b[0m\n'}
[5.476608] (remotecontrol_msgs) StdoutLine: {'line': b'[ 84%] Built target remotecontrol_msgs__rosidl_generator_py\n'}
[5.492694] (remotecontrol_msgs) StdoutLine: {'line': b'[ 87%] \x1b[32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_fastrtps_c__pyext.dir/rosidl_generator_py/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_fastrtps_c.c.o\x1b[0m\n'}
[5.495080] (remotecontrol_msgs) StdoutLine: {'line': b'[ 89%] \x1b[32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_c__pyext.dir/rosidl_generator_py/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_c.c.o\x1b[0m\n'}
[5.495595] (remotecontrol_msgs) StdoutLine: {'line': b'[ 92%] \x1b[32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_introspection_c__pyext.dir/rosidl_generator_py/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_introspection_c.c.o\x1b[0m\n'}
[5.521561] (keyframe_msgs) StdoutLine: {'line': b'[ 82%] \x1b[32m\x1b[1mLinking CXX shared library libkeyframe_msgs__rosidl_typesupport_fastrtps_cpp.so\x1b[0m\n'}
[5.526191] (keyframe_msgs) StdoutLine: {'line': b'[ 82%] Built target keyframe_msgs__rosidl_typesupport_fastrtps_c\n'}
[5.538699] (-) TimerEvent: {}
[5.560833] (remotecontrol_msgs) StdoutLine: {'line': b'[ 94%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[5.563944] (remotecontrol_msgs) StdoutLine: {'line': b'[ 97%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[5.567388] (remotecontrol_msgs) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[5.584371] (keyframe_msgs) StdoutLine: {'line': b'[ 82%] Built target keyframe_msgs__rosidl_typesupport_fastrtps_cpp\n'}
[5.592128] (remotecontrol_msgs) StdoutLine: {'line': b'[100%] Built target remotecontrol_msgs__rosidl_typesupport_c__pyext\n'}
[5.595853] (remotecontrol_msgs) StdoutLine: {'line': b'[100%] Built target remotecontrol_msgs__rosidl_typesupport_fastrtps_c__pyext\n'}
[5.599112] (remotecontrol_msgs) StdoutLine: {'line': b'[100%] Built target remotecontrol_msgs__rosidl_typesupport_introspection_c__pyext\n'}
[5.600804] (keyframe_msgs) StdoutLine: {'line': b'[ 82%] Built target keyframe_msgs\n'}
[5.608827] (remotecontrol_msgs) CommandEnded: {'returncode': 0}
[5.609668] (remotecontrol_msgs) JobProgress: {'identifier': 'remotecontrol_msgs', 'progress': 'install'}
[5.609939] (remotecontrol_msgs) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs'], 'cwd': '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[5.615672] (keyframe_msgs) StdoutLine: {'line': b'[ 83%] \x1b[34m\x1b[1mGenerating Python code for ROS interfaces\x1b[0m\n'}
[5.616362] (remotecontrol_msgs) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[5.616495] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/ament_index/resource_index/rosidl_interfaces/remotecontrol_msgs\n'}
[5.616565] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs\n'}
[5.616627] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg\n'}
[5.616674] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/joystick_state.h\n'}
[5.616727] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/key_state.h\n'}
[5.616783] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_generator_c__visibility_control.h\n'}
[5.616839] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail\n'}
[5.616893] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__struct.h\n'}
[5.616951] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__type_support.h\n'}
[5.616983] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__functions.h\n'}
[5.617042] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__functions.c\n'}
[5.617115] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__struct.h\n'}
[5.617163] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__functions.h\n'}
[5.617225] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__type_support.h\n'}
[5.617268] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__functions.c\n'}
[5.617307] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/library_path.sh\n'}
[5.617364] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/library_path.dsv\n'}
[5.617417] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_generator_c.so\n'}
[5.617473] (remotecontrol_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_generator_c.so" to ""\n'}
[5.617523] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs\n'}
[5.617575] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg\n'}
[5.617629] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[5.617670] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail\n'}
[5.617708] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__rosidl_typesupport_fastrtps_c.h\n'}
[5.617755] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__rosidl_typesupport_fastrtps_c.h\n'}
[5.617785] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_fastrtps_c.so\n'}
[5.617862] (remotecontrol_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_fastrtps_c.so" to ""\n'}
[5.617913] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs\n'}
[5.617965] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg\n'}
[5.618102] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/joystick_state.hpp\n'}
[5.618274] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/key_state.hpp\n'}
[5.618417] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[5.618531] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail\n'}
[5.618654] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__struct.hpp\n'}
[5.618754] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__type_support.hpp\n'}
[5.618836] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__builder.hpp\n'}
[5.618956] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__traits.hpp\n'}
[5.619073] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__struct.hpp\n'}
[5.619176] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__traits.hpp\n'}
[5.619271] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__builder.hpp\n'}
[5.619378] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__type_support.hpp\n'}
[5.619460] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs\n'}
[5.619585] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg\n'}
[5.619703] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[5.619808] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail\n'}
[5.619917] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/dds_fastrtps\n'}
[5.620022] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[5.620127] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[5.620235] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_fastrtps_cpp.so\n'}
[5.620354] (remotecontrol_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_fastrtps_cpp.so" to ""\n'}
[5.620484] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs\n'}
[5.620591] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg\n'}
[5.620700] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[5.620821] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail\n'}
[5.620933] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__type_support.c\n'}
[5.621058] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__type_support.c\n'}
[5.621168] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__rosidl_typesupport_introspection_c.h\n'}
[5.621260] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__rosidl_typesupport_introspection_c.h\n'}
[5.621347] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_introspection_c.so\n'}
[5.621434] (remotecontrol_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_introspection_c.so" to ""\n'}
[5.621524] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_c.so\n'}
[5.621652] (remotecontrol_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_c.so" to ""\n'}
[5.621753] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs\n'}
[5.621794] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg\n'}
[5.621825] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail\n'}
[5.621855] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__type_support.cpp\n'}
[5.621888] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__type_support.cpp\n'}
[5.621916] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[5.621944] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[5.621972] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_introspection_cpp.so\n'}
[5.621999] (remotecontrol_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_introspection_cpp.so" to ""\n'}
[5.622039] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_cpp.so\n'}
[5.622067] (remotecontrol_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_cpp.so" to ""\n'}
[5.622094] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/pythonpath.sh\n'}
[5.622121] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/pythonpath.dsv\n'}
[5.622147] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info\n'}
[5.622174] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[5.622202] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[5.622228] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info/top_level.txt\n'}
[5.622258] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[5.622284] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs\n'}
[5.622310] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_c.c\n'}
[5.622337] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_introspection_c.c\n'}
[5.622364] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/libremotecontrol_msgs__rosidl_generator_py.so\n'}
[5.622389] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[5.622419] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[5.622447] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[5.622475] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[5.622501] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg\n'}
[5.622526] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_joystick_state.py\n'}
[5.622553] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_key_state.py\n'}
[5.622582] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_key_state_s.c\n'}
[5.622610] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_joystick_state_s.c\n'}
[5.622638] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/__init__.py\n'}
[5.622665] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/__init__.py\n'}
[5.638786] (-) TimerEvent: {}
[5.651757] (remotecontrol_msgs) StdoutLine: {'line': b"Listing '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs'...\n"}
[5.651902] (remotecontrol_msgs) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/__init__.py'...\n"}
[5.651943] (remotecontrol_msgs) StdoutLine: {'line': b"Listing '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg'...\n"}
[5.651975] (remotecontrol_msgs) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/__init__.py'...\n"}
[5.652014] (remotecontrol_msgs) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_joystick_state.py'...\n"}
[5.652046] (remotecontrol_msgs) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_key_state.py'...\n"}
[5.655921] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[5.656244] (remotecontrol_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[5.656421] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[5.656711] (remotecontrol_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[5.656914] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[5.657254] (remotecontrol_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[5.657329] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_generator_py.so\n'}
[5.657737] (remotecontrol_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_generator_py.so" to ""\n'}
[5.657852] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/msg/JoystickState.idl\n'}
[5.657976] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/msg/KeyState.idl\n'}
[5.658057] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/msg/JoystickState.msg\n'}
[5.658112] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/msg/KeyState.msg\n'}
[5.658161] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/ament_index/resource_index/package_run_dependencies/remotecontrol_msgs\n'}
[5.658238] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/ament_index/resource_index/parent_prefix_path/remotecontrol_msgs\n'}
[5.658292] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/ament_prefix_path.sh\n'}
[5.658344] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/ament_prefix_path.dsv\n'}
[5.658394] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/path.sh\n'}
[5.658445] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/path.dsv\n'}
[5.658495] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/local_setup.bash\n'}
[5.658547] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/local_setup.sh\n'}
[5.658600] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/local_setup.zsh\n'}
[5.658648] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/local_setup.dsv\n'}
[5.658695] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.dsv\n'}
[5.658764] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/ament_index/resource_index/packages/remotecontrol_msgs\n'}
[5.658840] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_cExport.cmake\n'}
[5.658899] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_cExport-noconfig.cmake\n'}
[5.658965] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[5.659029] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[5.659093] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_cppExport.cmake\n'}
[5.659154] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[5.659206] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[5.659266] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_introspection_cExport.cmake\n'}
[5.659321] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[5.659383] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_cExport.cmake\n'}
[5.659461] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_cExport-noconfig.cmake\n'}
[5.659560] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_introspection_cppExport.cmake\n'}
[5.659642] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[5.659708] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_cppExport.cmake\n'}
[5.659778] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[5.659857] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_pyExport.cmake\n'}
[5.659946] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_pyExport-noconfig.cmake\n'}
[5.660083] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/rosidl_cmake-extras.cmake\n'}
[5.660179] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[5.660252] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[5.660326] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[5.660408] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/ament_cmake_export_targets-extras.cmake\n'}
[5.660461] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[5.660513] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[5.660584] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgsConfig.cmake\n'}
[5.660663] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgsConfig-version.cmake\n'}
[5.660713] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.xml\n'}
[5.662574] (remotecontrol_msgs) CommandEnded: {'returncode': 0}
[5.672646] (remotecontrol_msgs) JobEnded: {'identifier': 'remotecontrol_msgs', 'rc': 0}
[5.738978] (-) TimerEvent: {}
[5.839198] (-) TimerEvent: {}
[5.939441] (-) TimerEvent: {}
[6.039685] (-) TimerEvent: {}
[6.139953] (-) TimerEvent: {}
[6.240181] (-) TimerEvent: {}
[6.340405] (-) TimerEvent: {}
[6.440754] (-) TimerEvent: {}
[6.541020] (-) TimerEvent: {}
[6.641248] (-) TimerEvent: {}
[6.686584] (keyframe_msgs) StdoutLine: {'line': b'[ 83%] Built target keyframe_msgs__py\n'}
[6.708666] (keyframe_msgs) StdoutLine: {'line': b'[ 86%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_py.dir/rosidl_generator_py/keyframe_msgs/msg/_key_frame_pose_s.c.o\x1b[0m\n'}
[6.708857] (keyframe_msgs) StdoutLine: {'line': b'[ 86%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_py.dir/rosidl_generator_py/keyframe_msgs/msg/_vehicle_diagnostic_s.c.o\x1b[0m\n'}
[6.708932] (keyframe_msgs) StdoutLine: {'line': b'[ 86%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_py.dir/rosidl_generator_py/keyframe_msgs/msg/_vehicle_control_flags_s.c.o\x1b[0m\n'}
[6.708988] (keyframe_msgs) StdoutLine: {'line': b'[ 87%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_py.dir/rosidl_generator_py/keyframe_msgs/msg/_joystick_state_s.c.o\x1b[0m\n'}
[6.709757] (keyframe_msgs) StdoutLine: {'line': b'[ 88%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_py.dir/rosidl_generator_py/keyframe_msgs/msg/_lateral_deviation_s.c.o\x1b[0m\n'}
[6.710136] (keyframe_msgs) StdoutLine: {'line': b'[ 89%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_py.dir/rosidl_generator_py/keyframe_msgs/msg/_key_frame_pose_array_s.c.o\x1b[0m\n'}
[6.711339] (keyframe_msgs) StdoutLine: {'line': b'[ 90%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_py.dir/rosidl_generator_py/keyframe_msgs/msg/_key_frame_s.c.o\x1b[0m\n'}
[6.712261] (keyframe_msgs) StdoutLine: {'line': b'[ 91%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_py.dir/rosidl_generator_py/keyframe_msgs/msg/_vehicle_motion_s.c.o\x1b[0m\n'}
[6.712873] (keyframe_msgs) StdoutLine: {'line': b'[ 92%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_py.dir/rosidl_generator_py/keyframe_msgs/msg/_key_state_s.c.o\x1b[0m\n'}
[6.741360] (-) TimerEvent: {}
[6.812608] (keyframe_msgs) StdoutLine: {'line': b'[ 93%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/keyframe_msgs/libkeyframe_msgs__rosidl_generator_py.so\x1b[0m\n'}
[6.841569] (-) TimerEvent: {}
[6.848830] (keyframe_msgs) StdoutLine: {'line': b'[ 93%] Built target keyframe_msgs__rosidl_generator_py\n'}
[6.864189] (keyframe_msgs) StdoutLine: {'line': b'[ 94%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c__pyext.dir/rosidl_generator_py/keyframe_msgs/_keyframe_msgs_s.ep.rosidl_typesupport_fastrtps_c.c.o\x1b[0m\n'}
[6.864378] (keyframe_msgs) StdoutLine: {'line': b'[ 95%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c__pyext.dir/rosidl_generator_py/keyframe_msgs/_keyframe_msgs_s.ep.rosidl_typesupport_introspection_c.c.o\x1b[0m\n'}
[6.867408] (keyframe_msgs) StdoutLine: {'line': b'[ 96%] \x1b[32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_c__pyext.dir/rosidl_generator_py/keyframe_msgs/_keyframe_msgs_s.ep.rosidl_typesupport_c.c.o\x1b[0m\n'}
[6.941651] (-) TimerEvent: {}
[6.944362] (keyframe_msgs) StdoutLine: {'line': b'[ 97%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[6.944599] (keyframe_msgs) StdoutLine: {'line': b'[ 98%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[6.960874] (keyframe_msgs) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[6.974478] (keyframe_msgs) StdoutLine: {'line': b'[100%] Built target keyframe_msgs__rosidl_typesupport_introspection_c__pyext\n'}
[6.975201] (keyframe_msgs) StdoutLine: {'line': b'[100%] Built target keyframe_msgs__rosidl_typesupport_fastrtps_c__pyext\n'}
[6.996444] (keyframe_msgs) StdoutLine: {'line': b'[100%] Built target keyframe_msgs__rosidl_typesupport_c__pyext\n'}
[7.006307] (keyframe_msgs) CommandEnded: {'returncode': 0}
[7.007045] (keyframe_msgs) JobProgress: {'identifier': 'keyframe_msgs', 'progress': 'install'}
[7.007362] (keyframe_msgs) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/xugong_web_ws/build/keyframe_msgs'], 'cwd': '/home/<USER>/xugong_web_ws/build/keyframe_msgs', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/keyframe_msgs'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[7.017628] (keyframe_msgs) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[7.017840] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/ament_index/resource_index/rosidl_interfaces/keyframe_msgs\n'}
[7.017919] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs\n'}
[7.018020] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg\n'}
[7.018098] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/joystick_state.h\n'}
[7.018153] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/key_state.h\n'}
[7.018204] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/vehicle_diagnostic.h\n'}
[7.018256] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/rosidl_generator_c__visibility_control.h\n'}
[7.018308] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/key_frame_pose_array.h\n'}
[7.018394] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/vehicle_motion.h\n'}
[7.018463] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/key_frame_pose.h\n'}
[7.018515] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/vehicle_control_flags.h\n'}
[7.018563] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/key_frame.h\n'}
[7.018639] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/lateral_deviation.h\n'}
[7.018688] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail\n'}
[7.018737] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__type_support.h\n'}
[7.018786] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__functions.c\n'}
[7.018833] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__functions.h\n'}
[7.018881] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__type_support.h\n'}
[7.018928] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__functions.h\n'}
[7.019031] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__type_support.h\n'}
[7.019085] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__struct.h\n'}
[7.019134] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__struct.h\n'}
[7.019205] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__struct.h\n'}
[7.019258] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__functions.c\n'}
[7.019308] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__type_support.h\n'}
[7.019358] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__type_support.h\n'}
[7.019406] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__type_support.h\n'}
[7.019457] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__functions.h\n'}
[7.019506] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__functions.h\n'}
[7.019554] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__functions.c\n'}
[7.019602] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__type_support.h\n'}
[7.019650] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__struct.h\n'}
[7.019716] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__type_support.h\n'}
[7.019804] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__functions.c\n'}
[7.019854] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__functions.c\n'}
[7.019900] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__functions.h\n'}
[7.019951] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__functions.c\n'}
[7.019998] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__functions.c\n'}
[7.020099] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__functions.c\n'}
[7.020151] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__functions.h\n'}
[7.020199] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__type_support.h\n'}
[7.020322] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__struct.h\n'}
[7.020393] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__functions.h\n'}
[7.020444] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__struct.h\n'}
[7.020492] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__struct.h\n'}
[7.020540] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__functions.h\n'}
[7.020588] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__functions.h\n'}
[7.020637] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__struct.h\n'}
[7.020686] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__functions.c\n'}
[7.020736] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__struct.h\n'}
[7.020808] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/environment/library_path.sh\n'}
[7.020860] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/environment/library_path.dsv\n'}
[7.020910] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_generator_c.so\n'}
[7.021004] (keyframe_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_generator_c.so" to ""\n'}
[7.021063] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs\n'}
[7.021106] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg\n'}
[7.021148] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[7.021190] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail\n'}
[7.021233] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__rosidl_typesupport_fastrtps_c.h\n'}
[7.021278] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__rosidl_typesupport_fastrtps_c.h\n'}
[7.021360] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__rosidl_typesupport_fastrtps_c.h\n'}
[7.021412] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__rosidl_typesupport_fastrtps_c.h\n'}
[7.021456] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__rosidl_typesupport_fastrtps_c.h\n'}
[7.021534] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__rosidl_typesupport_fastrtps_c.h\n'}
[7.021578] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__rosidl_typesupport_fastrtps_c.h\n'}
[7.021623] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__rosidl_typesupport_fastrtps_c.h\n'}
[7.021670] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__rosidl_typesupport_fastrtps_c.h\n'}
[7.021719] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_fastrtps_c.so\n'}
[7.021798] (keyframe_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_fastrtps_c.so" to ""\n'}
[7.021850] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs\n'}
[7.021960] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg\n'}
[7.022019] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/key_frame_pose.hpp\n'}
[7.022077] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/joystick_state.hpp\n'}
[7.022127] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/key_state.hpp\n'}
[7.022206] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/key_frame_pose_array.hpp\n'}
[7.022254] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/vehicle_control_flags.hpp\n'}
[7.022300] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[7.022351] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/vehicle_diagnostic.hpp\n'}
[7.022401] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/lateral_deviation.hpp\n'}
[7.022448] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/key_frame.hpp\n'}
[7.022527] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail\n'}
[7.022578] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__traits.hpp\n'}
[7.022627] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__struct.hpp\n'}
[7.022674] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__type_support.hpp\n'}
[7.022722] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__builder.hpp\n'}
[7.022813] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__traits.hpp\n'}
[7.022863] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__type_support.hpp\n'}
[7.022912] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__builder.hpp\n'}
[7.022961] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__type_support.hpp\n'}
[7.023010] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__type_support.hpp\n'}
[7.023061] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__traits.hpp\n'}
[7.023108] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__type_support.hpp\n'}
[7.023155] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__builder.hpp\n'}
[7.023201] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__struct.hpp\n'}
[7.023287] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__traits.hpp\n'}
[7.023342] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__builder.hpp\n'}
[7.023391] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__traits.hpp\n'}
[7.023438] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__traits.hpp\n'}
[7.023484] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__struct.hpp\n'}
[7.023581] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__struct.hpp\n'}
[7.023638] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__struct.hpp\n'}
[7.023687] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__builder.hpp\n'}
[7.023736] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__builder.hpp\n'}
[7.023785] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__traits.hpp\n'}
[7.023907] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__traits.hpp\n'}
[7.023956] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__type_support.hpp\n'}
[7.024051] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__traits.hpp\n'}
[7.024104] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__builder.hpp\n'}
[7.024156] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__type_support.hpp\n'}
[7.024206] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__builder.hpp\n'}
[7.024255] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__struct.hpp\n'}
[7.024302] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__struct.hpp\n'}
[7.024349] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__builder.hpp\n'}
[7.024395] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__struct.hpp\n'}
[7.024440] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__type_support.hpp\n'}
[7.024486] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__type_support.hpp\n'}
[7.024532] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__struct.hpp\n'}
[7.024577] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/vehicle_motion.hpp\n'}
[7.024624] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs\n'}
[7.024672] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg\n'}
[7.024719] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[7.024766] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail\n'}
[7.024811] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.024856] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/dds_fastrtps\n'}
[7.024902] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.024970] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.025098] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.025155] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.025234] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.025282] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.025332] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.025376] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[7.025422] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_fastrtps_cpp.so\n'}
[7.025466] (keyframe_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_fastrtps_cpp.so" to ""\n'}
[7.025535] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs\n'}
[7.025583] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg\n'}
[7.025630] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[7.025727] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail\n'}
[7.025780] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__rosidl_typesupport_introspection_c.h\n'}
[7.025828] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__rosidl_typesupport_introspection_c.h\n'}
[7.025874] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__type_support.c\n'}
[7.025919] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__type_support.c\n'}
[7.025962] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__rosidl_typesupport_introspection_c.h\n'}
[7.026014] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__rosidl_typesupport_introspection_c.h\n'}
[7.026067] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__type_support.c\n'}
[7.026109] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__rosidl_typesupport_introspection_c.h\n'}
[7.026139] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__rosidl_typesupport_introspection_c.h\n'}
[7.026167] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__rosidl_typesupport_introspection_c.h\n'}
[7.026196] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__type_support.c\n'}
[7.026249] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__rosidl_typesupport_introspection_c.h\n'}
[7.026278] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__rosidl_typesupport_introspection_c.h\n'}
[7.026306] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__type_support.c\n'}
[7.026334] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__type_support.c\n'}
[7.026404] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__type_support.c\n'}
[7.026442] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__type_support.c\n'}
[7.026472] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__type_support.c\n'}
[7.026501] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_introspection_c.so\n'}
[7.026528] (keyframe_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_introspection_c.so" to ""\n'}
[7.026556] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_c.so\n'}
[7.026584] (keyframe_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_c.so" to ""\n'}
[7.026611] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs\n'}
[7.026639] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg\n'}
[7.026667] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail\n'}
[7.026695] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__type_support.cpp\n'}
[7.026774] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__type_support.cpp\n'}
[7.026803] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.026832] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.026861] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.026889] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__type_support.cpp\n'}
[7.026917] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__type_support.cpp\n'}
[7.026946] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__type_support.cpp\n'}
[7.026987] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__type_support.cpp\n'}
[7.027026] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.027056] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.027085] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.027157] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__type_support.cpp\n'}
[7.027206] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.027250] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.027291] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__type_support.cpp\n'}
[7.027346] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__type_support.cpp\n'}
[7.027376] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__rosidl_typesupport_introspection_cpp.hpp\n'}
[7.027404] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_introspection_cpp.so\n'}
[7.027449] (keyframe_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_introspection_cpp.so" to ""\n'}
[7.027496] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_cpp.so\n'}
[7.027643] (keyframe_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_cpp.so" to ""\n'}
[7.027694] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/environment/pythonpath.sh\n'}
[7.027739] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/environment/pythonpath.dsv\n'}
[7.027798] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs-0.0.0-py3.10.egg-info\n'}
[7.027879] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[7.027927] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[7.027968] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs-0.0.0-py3.10.egg-info/top_level.txt\n'}
[7.028022] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[7.028083] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs\n'}
[7.028128] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/_keyframe_msgs_s.ep.rosidl_typesupport_c.c\n'}
[7.028226] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[7.028281] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[7.028381] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[7.028453] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/_keyframe_msgs_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[7.028539] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/libkeyframe_msgs__rosidl_generator_py.so\n'}
[7.028613] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/_keyframe_msgs_s.ep.rosidl_typesupport_introspection_c.c\n'}
[7.028677] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg\n'}
[7.028762] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_vehicle_diagnostic_s.c\n'}
[7.028792] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_frame_pose_array.py\n'}
[7.028852] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_vehicle_motion_s.c\n'}
[7.028907] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_vehicle_motion.py\n'}
[7.028939] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_frame_pose.py\n'}
[7.028986] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_lateral_deviation.py\n'}
[7.029039] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_frame_pose_array_s.c\n'}
[7.029097] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_vehicle_diagnostic.py\n'}
[7.029139] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_vehicle_control_flags_s.c\n'}
[7.029194] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_frame.py\n'}
[7.029232] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_joystick_state.py\n'}
[7.029284] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_state.py\n'}
[7.029346] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_lateral_deviation_s.c\n'}
[7.029393] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_frame_pose_s.c\n'}
[7.029461] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_vehicle_control_flags.py\n'}
[7.029503] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_state_s.c\n'}
[7.029545] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_frame_s.c\n'}
[7.029604] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_joystick_state_s.c\n'}
[7.029642] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/__init__.py\n'}
[7.029677] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/__init__.py\n'}
[7.041750] (-) TimerEvent: {}
[7.071650] (keyframe_msgs) StdoutLine: {'line': b"Listing '/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs'...\n"}
[7.071846] (keyframe_msgs) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/__init__.py'...\n"}
[7.071925] (keyframe_msgs) StdoutLine: {'line': b"Listing '/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg'...\n"}
[7.071980] (keyframe_msgs) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/__init__.py'...\n"}
[7.072045] (keyframe_msgs) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_joystick_state.py'...\n"}
[7.072099] (keyframe_msgs) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_frame.py'...\n"}
[7.072149] (keyframe_msgs) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_frame_pose.py'...\n"}
[7.072199] (keyframe_msgs) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_frame_pose_array.py'...\n"}
[7.072246] (keyframe_msgs) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_state.py'...\n"}
[7.072288] (keyframe_msgs) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_lateral_deviation.py'...\n"}
[7.072334] (keyframe_msgs) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_vehicle_control_flags.py'...\n"}
[7.072379] (keyframe_msgs) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_vehicle_diagnostic.py'...\n"}
[7.072423] (keyframe_msgs) StdoutLine: {'line': b"Compiling '/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_vehicle_motion.py'...\n"}
[7.075152] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[7.075321] (keyframe_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[7.075422] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[7.075574] (keyframe_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[7.075653] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[7.075837] (keyframe_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[7.075921] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_generator_py.so\n'}
[7.076151] (keyframe_msgs) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_generator_py.so" to ""\n'}
[7.076229] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/KeyFrame.idl\n'}
[7.076299] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/KeyFramePose.idl\n'}
[7.076374] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/KeyFramePoseArray.idl\n'}
[7.076440] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/VehicleControlFlags.idl\n'}
[7.076498] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/VehicleDiagnostic.idl\n'}
[7.076570] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/VehicleMotion.idl\n'}
[7.076628] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/JoystickState.idl\n'}
[7.076700] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/KeyState.idl\n'}
[7.076753] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/LateralDeviation.idl\n'}
[7.076806] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/KeyFrame.msg\n'}
[7.076889] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/KeyFramePose.msg\n'}
[7.076961] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/KeyFramePoseArray.msg\n'}
[7.077016] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/VehicleControlFlags.msg\n'}
[7.077107] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/VehicleDiagnostic.msg\n'}
[7.077188] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/VehicleMotion.msg\n'}
[7.077237] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/JoystickState.msg\n'}
[7.077314] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/KeyState.msg\n'}
[7.077389] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/LateralDeviation.msg\n'}
[7.077463] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/ament_index/resource_index/package_run_dependencies/keyframe_msgs\n'}
[7.077537] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/ament_index/resource_index/parent_prefix_path/keyframe_msgs\n'}
[7.077624] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/environment/ament_prefix_path.sh\n'}
[7.077769] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/environment/ament_prefix_path.dsv\n'}
[7.077854] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/environment/path.sh\n'}
[7.078025] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/environment/path.dsv\n'}
[7.078220] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/local_setup.bash\n'}
[7.078455] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/local_setup.sh\n'}
[7.078550] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/local_setup.zsh\n'}
[7.078607] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/local_setup.dsv\n'}
[7.078663] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.dsv\n'}
[7.078713] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/ament_index/resource_index/packages/keyframe_msgs\n'}
[7.078763] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_generator_cExport.cmake\n'}
[7.078873] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_generator_cExport-noconfig.cmake\n'}
[7.078968] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[7.079075] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[7.079217] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_generator_cppExport.cmake\n'}
[7.079344] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[7.079443] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[7.079583] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgs__rosidl_typesupport_introspection_cExport.cmake\n'}
[7.079826] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgs__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[7.080124] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgs__rosidl_typesupport_cExport.cmake\n'}
[7.080333] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgs__rosidl_typesupport_cExport-noconfig.cmake\n'}
[7.080499] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgs__rosidl_typesupport_introspection_cppExport.cmake\n'}
[7.080721] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgs__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[7.080894] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgs__rosidl_typesupport_cppExport.cmake\n'}
[7.080986] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgs__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[7.081119] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_generator_pyExport.cmake\n'}
[7.081245] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_generator_pyExport-noconfig.cmake\n'}
[7.081328] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/rosidl_cmake-extras.cmake\n'}
[7.081418] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[7.081517] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[7.081607] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[7.081700] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/ament_cmake_export_targets-extras.cmake\n'}
[7.081809] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[7.081895] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[7.081987] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgsConfig.cmake\n'}
[7.082075] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgsConfig-version.cmake\n'}
[7.082166] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.xml\n'}
[7.084140] (keyframe_msgs) CommandEnded: {'returncode': 0}
[7.093573] (keyframe_msgs) JobEnded: {'identifier': 'keyframe_msgs', 'rc': 0}
[7.094266] (web) JobStarted: {'identifier': 'web'}
[7.106506] (web) JobProgress: {'identifier': 'web', 'progress': 'cmake'}
[7.106790] (web) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/xugong_web_ws/src/web', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/web'], 'cwd': '/home/<USER>/xugong_web_ws/build/web', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/web'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble')]), 'shell': False}
[7.141859] (-) TimerEvent: {}
[7.156996] (web) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[7.203055] (web) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[7.210784] (web) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[7.241968] (-) TimerEvent: {}
[7.270453] (web) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[7.275417] (web) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[7.275604] (web) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[7.275859] (web) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[7.278278] (web) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[7.342137] (-) TimerEvent: {}
[7.345632] (web) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[7.351286] (web) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[7.351486] (web) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[7.351776] (web) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[7.353873] (web) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[7.442232] (-) TimerEvent: {}
[7.476512] (web) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[7.542354] (-) TimerEvent: {}
[7.560347] (web) StdoutLine: {'line': b'-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[7.595143] (web) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[7.599068] (web) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[7.605171] (web) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[7.614019] (web) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[7.625522] (web) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[7.642498] (-) TimerEvent: {}
[7.664161] (web) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[7.667028] (web) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[7.742612] (-) TimerEvent: {}
[7.746072] (web) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[7.771146] (web) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[7.808291] (web) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[7.818783] (web) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[7.842762] (-) TimerEvent: {}
[7.879294] (web) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[7.879580] (web) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[7.942204] (web) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[7.942820] (-) TimerEvent: {}
[7.943120] (web) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[7.987633] (web) StdoutLine: {'line': b'-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[8.011201] (web) StdoutLine: {'line': b'-- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)\n'}
[8.026919] (web) StdoutLine: {'line': b'-- Found hv: /usr/local/include  \n'}
[8.029532] (web) StdoutLine: {'line': b'-- Found nlohmann_json: /usr/lib/cmake/nlohmann_json/nlohmann_jsonConfig.cmake (found version "3.10.5") \n'}
[8.032524] (web) StdoutLine: {'line': b'-- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)\n'}
[8.042358] (web) StdoutLine: {'line': b'-- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)\n'}
[8.043109] (-) TimerEvent: {}
[8.057516] (web) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)\n'}
[8.058503] (web) StdoutLine: {'line': b'-- Found Eigen3: TRUE (found version "3.4.0") \n'}
[8.058617] (web) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[8.143226] (-) TimerEvent: {}
[8.148235] (web) StdoutLine: {'line': b"-- Checking for module 'eigen3'\n"}
[8.159036] (web) StdoutLine: {'line': b'--   Found eigen3, version 3.4.0\n'}
[8.188542] (web) StdoutLine: {'line': b'-- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") \n'}
[8.188695] (web) StdoutLine: {'line': b'-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)\n'}
[8.209384] (web) StdoutLine: {'line': b'-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found suitable version "1.74.0", minimum required is "1.65.0") found components: system filesystem date_time iostreams serialization \n'}
[8.243352] (-) TimerEvent: {}
[8.260328] (web) StdoutLine: {'line': b"-- Checking for module 'libusb-1.0'\n"}
[8.274873] (web) StdoutLine: {'line': b'--   Found libusb-1.0, version 1.0.25\n'}
[8.304321] (web) StdoutLine: {'line': b'-- Found libusb: /usr/lib/x86_64-linux-gnu/libusb-1.0.so  \n'}
[8.306055] (web) StdoutLine: {'line': b'-- Found OpenNI: /usr/lib/libOpenNI.so;libusb::libusb (found version "1.5.4.0") \n'}
[8.306168] (web) StdoutLine: {'line': b'-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)\n'}
[8.343449] (-) TimerEvent: {}
[8.353937] (web) StdoutLine: {'line': b'-- Found OpenNI2: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb (found version "2.2.0.33") \n'}
[8.354247] (web) StdoutLine: {'line': b'-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)\n'}
[8.354365] (web) StderrLine: {'line': b'\x1b[0m** WARNING ** io features related to pcap will be disabled\x1b[0m\n'}
[8.354476] (web) StderrLine: {'line': b'\x1b[0m** WARNING ** io features related to png will be disabled\x1b[0m\n'}
[8.383264] (web) StdoutLine: {'line': b'-- Found GLEW: /usr/lib/x86_64-linux-gnu/libGLEW.so  \n'}
[8.388558] (web) StdoutLine: {'line': b'-- Found OpenGL: /usr/lib/x86_64-linux-gnu/libOpenGL.so  found components: OpenGL GLX \n'}
[8.443556] (-) TimerEvent: {}
[8.518050] (web) StdoutLine: {'line': b'-- Found MPI_C: /usr/lib/x86_64-linux-gnu/libmpi.so (found version "3.1") \n'}
[8.518658] (web) StdoutLine: {'line': b'-- Found MPI: TRUE (found version "3.1") found components: C \n'}
[8.524504] (web) StdoutLine: {'line': b'-- Found JsonCpp: /usr/lib/x86_64-linux-gnu/libjsoncpp.so (found suitable version "1.9.5", minimum required is "0.7.0") \n'}
[8.541524] (web) StdoutLine: {'line': b'-- Found ZLIB: /usr/lib/x86_64-linux-gnu/libz.so (found version "1.2.11") \n'}
[8.543766] (-) TimerEvent: {}
[8.561189] (web) StdoutLine: {'line': b'-- Found PNG: /usr/lib/x86_64-linux-gnu/libpng.so (found version "1.6.37") \n'}
[8.578551] (web) StdoutLine: {'line': b'-- Found Eigen3: /usr/include/eigen3 (found version "3.4.0") \n'}
[8.643888] (-) TimerEvent: {}
[8.654455] (web) StdoutLine: {'line': b'-- Found X11: /usr/include   \n'}
[8.655826] (web) StdoutLine: {'line': b'-- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so\n'}
[8.720390] (web) StdoutLine: {'line': b'-- Looking for XOpenDisplay in /usr/lib/x86_64-linux-gnu/libX11.so;/usr/lib/x86_64-linux-gnu/libXext.so - found\n'}
[8.720539] (web) StdoutLine: {'line': b'-- Looking for gethostbyname\n'}
[8.744021] (-) TimerEvent: {}
[8.817438] (web) StdoutLine: {'line': b'-- Looking for gethostbyname - found\n'}
[8.817681] (web) StdoutLine: {'line': b'-- Looking for connect\n'}
[8.844128] (-) TimerEvent: {}
[8.918253] (web) StdoutLine: {'line': b'-- Looking for connect - found\n'}
[8.918577] (web) StdoutLine: {'line': b'-- Looking for remove\n'}
[8.944258] (-) TimerEvent: {}
[9.018940] (web) StdoutLine: {'line': b'-- Looking for remove - found\n'}
[9.019104] (web) StdoutLine: {'line': b'-- Looking for shmat\n'}
[9.044362] (-) TimerEvent: {}
[9.112010] (web) StdoutLine: {'line': b'-- Looking for shmat - found\n'}
[9.112287] (web) StdoutLine: {'line': b'-- Looking for IceConnectionNumber in ICE\n'}
[9.144523] (-) TimerEvent: {}
[9.219772] (web) StdoutLine: {'line': b'-- Looking for IceConnectionNumber in ICE - found\n'}
[9.244596] (-) TimerEvent: {}
[9.298468] (web) StdoutLine: {'line': b'-- Found EXPAT: /usr/lib/x86_64-linux-gnu/libexpat.so (found version "2.4.7") \n'}
[9.300266] (web) StdoutLine: {'line': b'-- Found double-conversion: /usr/lib/x86_64-linux-gnu/libdouble-conversion.so  \n'}
[9.302515] (web) StdoutLine: {'line': b'-- Found LZ4: /usr/lib/x86_64-linux-gnu/liblz4.so (found version "1.9.3") \n'}
[9.304105] (web) StdoutLine: {'line': b'-- Found LZMA: /usr/lib/x86_64-linux-gnu/liblzma.so (found version "5.2.5") \n'}
[9.316145] (web) StdoutLine: {'line': b'-- Found JPEG: /usr/lib/x86_64-linux-gnu/libjpeg.so (found version "80") \n'}
[9.321438] (web) StdoutLine: {'line': b'-- Found TIFF: /usr/lib/x86_64-linux-gnu/libtiff.so (found version "4.3.0")  \n'}
[9.325582] (web) StdoutLine: {'line': b'-- Found Freetype: /usr/lib/x86_64-linux-gnu/libfreetype.so (found version "2.11.1") \n'}
[9.331126] (web) StdoutLine: {'line': b'-- Found utf8cpp: /usr/include/utf8cpp  \n'}
[9.344695] (-) TimerEvent: {}
[9.359716] (web) StdoutLine: {'line': b'-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)\n'}
[9.377264] (web) StdoutLine: {'line': b"-- Checking for module 'flann'\n"}
[9.398590] (web) StdoutLine: {'line': b'--   Found flann, version 1.9.1\n'}
[9.444868] (-) TimerEvent: {}
[9.459755] (web) StdoutLine: {'line': b'-- Found FLANN: /usr/lib/x86_64-linux-gnu/libflann_cpp.so  \n'}
[9.459986] (web) StdoutLine: {'line': b'-- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)\n'}
[9.461721] (web) StdoutLine: {'line': b'-- looking for PCL_COMMON\n'}
[9.463076] (web) StdoutLine: {'line': b'-- Found PCL_COMMON: /usr/lib/x86_64-linux-gnu/libpcl_common.so  \n'}
[9.463390] (web) StdoutLine: {'line': b'-- looking for PCL_OCTREE\n'}
[9.464417] (web) StdoutLine: {'line': b'-- Found PCL_OCTREE: /usr/lib/x86_64-linux-gnu/libpcl_octree.so  \n'}
[9.464575] (web) StdoutLine: {'line': b'-- looking for PCL_IO\n'}
[9.465731] (web) StdoutLine: {'line': b'-- Found PCL_IO: /usr/lib/x86_64-linux-gnu/libpcl_io.so  \n'}
[9.466051] (web) StdoutLine: {'line': b'-- looking for PCL_KDTREE\n'}
[9.466792] (web) StdoutLine: {'line': b'-- Found PCL_KDTREE: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so  \n'}
[9.466930] (web) StdoutLine: {'line': b'-- looking for PCL_SEARCH\n'}
[9.467799] (web) StdoutLine: {'line': b'-- Found PCL_SEARCH: /usr/lib/x86_64-linux-gnu/libpcl_search.so  \n'}
[9.467900] (web) StdoutLine: {'line': b'-- looking for PCL_SAMPLE_CONSENSUS\n'}
[9.468617] (web) StdoutLine: {'line': b'-- Found PCL_SAMPLE_CONSENSUS: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so  \n'}
[9.468720] (web) StdoutLine: {'line': b'-- looking for PCL_FILTERS\n'}
[9.469334] (web) StdoutLine: {'line': b'-- Found PCL_FILTERS: /usr/lib/x86_64-linux-gnu/libpcl_filters.so  \n'}
[9.469721] (web) StdoutLine: {'line': b'-- Found PCL: pcl_common;pcl_octree;pcl_io;pcl_kdtree;pcl_search;pcl_sample_consensus;pcl_filters;Boost::system;Boost::filesystem;Boost::date_time;Boost::iostreams;Boost::serialization;/usr/lib/libOpenNI.so;libusb::libusb;/usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb;VTK::ChartsCore;VTK::CommonColor;VTK::CommonComputationalGeometry;VTK::CommonCore;VTK::CommonDataModel;VTK::CommonExecutionModel;VTK::CommonMath;VTK::CommonMisc;VTK::CommonTransforms;VTK::FiltersCore;VTK::FiltersExtraction;VTK::FiltersGeneral;VTK::FiltersGeometry;VTK::FiltersModeling;VTK::FiltersSources;VTK::ImagingCore;VTK::ImagingSources;VTK::InteractionImage;VTK::InteractionStyle;VTK::InteractionWidgets;VTK::IOCore;VTK::IOGeometry;VTK::IOImage;VTK::IOLegacy;VTK::IOPLY;VTK::RenderingAnnotation;VTK::RenderingCore;VTK::RenderingContext2D;VTK::RenderingLOD;VTK::RenderingFreeType;VTK::ViewsCore;VTK::ViewsContext2D;VTK::RenderingOpenGL2;VTK::GUISupportQt;FLANN::FLANN  \n'}
[9.472596] (web) StdoutLine: {'line': b'-- Found pcl_conversions: 2.4.5 (/opt/ros/humble/share/pcl_conversions/cmake)\n'}
[9.492274] (web) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)\n'}
[9.499371] (web) StdoutLine: {'line': b'-- Found keyframe_msgs: 0.0.0 (/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake)\n'}
[9.512640] (web) StdoutLine: {'line': b'-- Found can_bridge: 0.0.0 (/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake)\n'}
[9.526744] (web) StdoutLine: {'line': b'-- Found remotecontrol_msgs: 0.0.0 (/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake)\n'}
[9.539429] (web) StdoutLine: {'line': b'-- Found global_traj_generate: 0.0.0 (/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake)\n'}
[9.544951] (-) TimerEvent: {}
[9.561512] (web) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[9.628466] (web) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[9.629037] (web) StdoutLine: {'line': b'-- Configured cppcheck include dirs: \n'}
[9.629215] (web) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[9.630050] (web) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[9.631134] (web) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[9.631887] (web) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[9.633674] (web) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[9.633793] (web) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[9.634182] (web) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[9.645080] (-) TimerEvent: {}
[9.648588] (web) StdoutLine: {'line': b'-- Configuring done\n'}
[9.697103] (web) StdoutLine: {'line': b'-- Generating done\n'}
[9.703036] (web) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/xugong_web_ws/build/web\n'}
[9.716921] (web) CommandEnded: {'returncode': 0}
[9.718541] (web) JobProgress: {'identifier': 'web', 'progress': 'build'}
[9.718592] (web) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/xugong_web_ws/build/web', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/xugong_web_ws/build/web', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/web'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble')]), 'shell': False}
[9.745234] (-) TimerEvent: {}
[9.773772] (web) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/web_node.dir/src/web_node.cpp.o\x1b[0m\n'}
[9.845352] (-) TimerEvent: {}
[9.945654] (-) TimerEvent: {}
[10.045922] (-) TimerEvent: {}
[10.146194] (-) TimerEvent: {}
[10.246546] (-) TimerEvent: {}
[10.346812] (-) TimerEvent: {}
[10.447082] (-) TimerEvent: {}
[10.547308] (-) TimerEvent: {}
[10.647619] (-) TimerEvent: {}
[10.747933] (-) TimerEvent: {}
[10.848175] (-) TimerEvent: {}
[10.948521] (-) TimerEvent: {}
[11.048764] (-) TimerEvent: {}
[11.149035] (-) TimerEvent: {}
[11.249314] (-) TimerEvent: {}
[11.349590] (-) TimerEvent: {}
[11.449891] (-) TimerEvent: {}
[11.550204] (-) TimerEvent: {}
[11.650501] (-) TimerEvent: {}
[11.750825] (-) TimerEvent: {}
[11.851065] (-) TimerEvent: {}
[11.951339] (-) TimerEvent: {}
[12.051661] (-) TimerEvent: {}
[12.151954] (-) TimerEvent: {}
[12.252204] (-) TimerEvent: {}
[12.352501] (-) TimerEvent: {}
[12.452767] (-) TimerEvent: {}
[12.553066] (-) TimerEvent: {}
[12.653377] (-) TimerEvent: {}
[12.753676] (-) TimerEvent: {}
[12.853950] (-) TimerEvent: {}
[12.954206] (-) TimerEvent: {}
[13.054464] (-) TimerEvent: {}
[13.154749] (-) TimerEvent: {}
[13.255050] (-) TimerEvent: {}
[13.355300] (-) TimerEvent: {}
[13.455569] (-) TimerEvent: {}
[13.555864] (-) TimerEvent: {}
[13.656101] (-) TimerEvent: {}
[13.756340] (-) TimerEvent: {}
[13.856643] (-) TimerEvent: {}
[13.956961] (-) TimerEvent: {}
[14.057220] (-) TimerEvent: {}
[14.157532] (-) TimerEvent: {}
[14.257881] (-) TimerEvent: {}
[14.358195] (-) TimerEvent: {}
[14.458451] (-) TimerEvent: {}
[14.558749] (-) TimerEvent: {}
[14.659004] (-) TimerEvent: {}
[14.759268] (-) TimerEvent: {}
[14.859585] (-) TimerEvent: {}
[14.959929] (-) TimerEvent: {}
[15.060184] (-) TimerEvent: {}
[15.160442] (-) TimerEvent: {}
[15.260830] (-) TimerEvent: {}
[15.361091] (-) TimerEvent: {}
[15.461398] (-) TimerEvent: {}
[15.561778] (-) TimerEvent: {}
[15.662032] (-) TimerEvent: {}
[15.762305] (-) TimerEvent: {}
[15.821255] (web) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/xugong_web_ws/src/web/src/web_node.cpp:3\x1b[m\x1b[K:\n'}
[15.821413] (web) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/xugong_web_ws/src/web/src/websocket_server.h:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid WebSocketServerSingleton::avoidance_remind_callback(std_msgs::msg::Int8_<std::allocator<void> >::SharedPtr)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[15.821457] (web) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/xugong_web_ws/src/web/src/websocket_server.h:1270:73:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kmsg\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[15.821493] (web) StderrLine: {'line': b' 1270 |     void avoidance_remind_callback(\x1b[01;35m\x1b[Kconst std_msgs::msg::Int8::SharedPtr msg\x1b[m\x1b[K)\n'}
[15.821525] (web) StderrLine: {'line': b'      |                                    \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~\x1b[m\x1b[K\n'}
[15.862503] (-) TimerEvent: {}
[15.962763] (-) TimerEvent: {}
[16.063011] (-) TimerEvent: {}
[16.163286] (-) TimerEvent: {}
[16.263564] (-) TimerEvent: {}
[16.363848] (-) TimerEvent: {}
[16.464196] (-) TimerEvent: {}
[16.564437] (-) TimerEvent: {}
[16.664699] (-) TimerEvent: {}
[16.764968] (-) TimerEvent: {}
[16.865267] (-) TimerEvent: {}
[16.965651] (-) TimerEvent: {}
[17.066065] (-) TimerEvent: {}
[17.166380] (-) TimerEvent: {}
[17.266692] (-) TimerEvent: {}
[17.366989] (-) TimerEvent: {}
[17.467337] (-) TimerEvent: {}
[17.567723] (-) TimerEvent: {}
[17.668105] (-) TimerEvent: {}
[17.768464] (-) TimerEvent: {}
[17.868864] (-) TimerEvent: {}
[17.969242] (-) TimerEvent: {}
[18.069686] (-) TimerEvent: {}
[18.169973] (-) TimerEvent: {}
[18.270255] (-) TimerEvent: {}
[18.370575] (-) TimerEvent: {}
[18.470965] (-) TimerEvent: {}
[18.571261] (-) TimerEvent: {}
[18.671591] (-) TimerEvent: {}
[18.771899] (-) TimerEvent: {}
[18.872210] (-) TimerEvent: {}
[18.972573] (-) TimerEvent: {}
[19.072919] (-) TimerEvent: {}
[19.173288] (-) TimerEvent: {}
[19.273668] (-) TimerEvent: {}
[19.374102] (-) TimerEvent: {}
[19.474512] (-) TimerEvent: {}
[19.574942] (-) TimerEvent: {}
[19.675332] (-) TimerEvent: {}
[19.775675] (-) TimerEvent: {}
[19.876053] (-) TimerEvent: {}
[19.976497] (-) TimerEvent: {}
[20.076747] (-) TimerEvent: {}
[20.177075] (-) TimerEvent: {}
[20.277406] (-) TimerEvent: {}
[20.377905] (-) TimerEvent: {}
[20.478191] (-) TimerEvent: {}
[20.578545] (-) TimerEvent: {}
[20.678892] (-) TimerEvent: {}
[20.779192] (-) TimerEvent: {}
[20.879440] (-) TimerEvent: {}
[20.979718] (-) TimerEvent: {}
[21.079993] (-) TimerEvent: {}
[21.180265] (-) TimerEvent: {}
[21.280698] (-) TimerEvent: {}
[21.381129] (-) TimerEvent: {}
[21.481387] (-) TimerEvent: {}
[21.581717] (-) TimerEvent: {}
[21.682062] (-) TimerEvent: {}
[21.782321] (-) TimerEvent: {}
[21.882560] (-) TimerEvent: {}
[21.982930] (-) TimerEvent: {}
[22.083302] (-) TimerEvent: {}
[22.183715] (-) TimerEvent: {}
[22.284047] (-) TimerEvent: {}
[22.384312] (-) TimerEvent: {}
[22.484613] (-) TimerEvent: {}
[22.584936] (-) TimerEvent: {}
[22.685207] (-) TimerEvent: {}
[22.785501] (-) TimerEvent: {}
[22.885788] (-) TimerEvent: {}
[22.986123] (-) TimerEvent: {}
[23.086433] (-) TimerEvent: {}
[23.186754] (-) TimerEvent: {}
[23.287237] (-) TimerEvent: {}
[23.387678] (-) TimerEvent: {}
[23.488055] (-) TimerEvent: {}
[23.588365] (-) TimerEvent: {}
[23.688692] (-) TimerEvent: {}
[23.788999] (-) TimerEvent: {}
[23.889359] (-) TimerEvent: {}
[23.989707] (-) TimerEvent: {}
[24.090087] (-) TimerEvent: {}
[24.190363] (-) TimerEvent: {}
[24.290745] (-) TimerEvent: {}
[24.391088] (-) TimerEvent: {}
[24.491347] (-) TimerEvent: {}
[24.591671] (-) TimerEvent: {}
[24.691909] (-) TimerEvent: {}
[24.792165] (-) TimerEvent: {}
[24.892438] (-) TimerEvent: {}
[24.992746] (-) TimerEvent: {}
[25.093011] (-) TimerEvent: {}
[25.193293] (-) TimerEvent: {}
[25.293595] (-) TimerEvent: {}
[25.393858] (-) TimerEvent: {}
[25.494197] (-) TimerEvent: {}
[25.594592] (-) TimerEvent: {}
[25.695047] (-) TimerEvent: {}
[25.795352] (-) TimerEvent: {}
[25.895754] (-) TimerEvent: {}
[25.996158] (-) TimerEvent: {}
[26.096490] (-) TimerEvent: {}
[26.196888] (-) TimerEvent: {}
[26.297211] (-) TimerEvent: {}
[26.397581] (-) TimerEvent: {}
[26.497959] (-) TimerEvent: {}
[26.598301] (-) TimerEvent: {}
[26.698670] (-) TimerEvent: {}
[26.799055] (-) TimerEvent: {}
[26.899497] (-) TimerEvent: {}
[26.999846] (-) TimerEvent: {}
[27.100220] (-) TimerEvent: {}
[27.200536] (-) TimerEvent: {}
[27.300909] (-) TimerEvent: {}
[27.401177] (-) TimerEvent: {}
[27.501403] (-) TimerEvent: {}
[27.601698] (-) TimerEvent: {}
[27.702074] (-) TimerEvent: {}
[27.802502] (-) TimerEvent: {}
[27.902867] (-) TimerEvent: {}
[28.003243] (-) TimerEvent: {}
[28.103574] (-) TimerEvent: {}
[28.203956] (-) TimerEvent: {}
[28.304243] (-) TimerEvent: {}
[28.404584] (-) TimerEvent: {}
[28.504925] (-) TimerEvent: {}
[28.605201] (-) TimerEvent: {}
[28.705551] (-) TimerEvent: {}
[28.805973] (-) TimerEvent: {}
[28.906328] (-) TimerEvent: {}
[29.006630] (-) TimerEvent: {}
[29.106973] (-) TimerEvent: {}
[29.207233] (-) TimerEvent: {}
[29.307609] (-) TimerEvent: {}
[29.407936] (-) TimerEvent: {}
[29.508303] (-) TimerEvent: {}
[29.608549] (-) TimerEvent: {}
[29.708795] (-) TimerEvent: {}
[29.809051] (-) TimerEvent: {}
[29.909366] (-) TimerEvent: {}
[30.009715] (-) TimerEvent: {}
[30.110069] (-) TimerEvent: {}
[30.210428] (-) TimerEvent: {}
[30.310693] (-) TimerEvent: {}
[30.410974] (-) TimerEvent: {}
[30.511210] (-) TimerEvent: {}
[30.611458] (-) TimerEvent: {}
[30.711731] (-) TimerEvent: {}
[30.811981] (-) TimerEvent: {}
[30.912223] (-) TimerEvent: {}
[31.012471] (-) TimerEvent: {}
[31.112720] (-) TimerEvent: {}
[31.213046] (-) TimerEvent: {}
[31.313336] (-) TimerEvent: {}
[31.413661] (-) TimerEvent: {}
[31.513968] (-) TimerEvent: {}
[31.614294] (-) TimerEvent: {}
[31.714610] (-) TimerEvent: {}
[31.814930] (-) TimerEvent: {}
[31.915285] (-) TimerEvent: {}
[32.015652] (-) TimerEvent: {}
[32.115980] (-) TimerEvent: {}
[32.216293] (-) TimerEvent: {}
[32.316565] (-) TimerEvent: {}
[32.416826] (-) TimerEvent: {}
[32.517079] (-) TimerEvent: {}
[32.617388] (-) TimerEvent: {}
[32.717712] (-) TimerEvent: {}
[32.818049] (-) TimerEvent: {}
[32.918395] (-) TimerEvent: {}
[33.018691] (-) TimerEvent: {}
[33.118963] (-) TimerEvent: {}
[33.219267] (-) TimerEvent: {}
[33.319545] (-) TimerEvent: {}
[33.419820] (-) TimerEvent: {}
[33.520179] (-) TimerEvent: {}
[33.620459] (-) TimerEvent: {}
[33.720834] (-) TimerEvent: {}
[33.821133] (-) TimerEvent: {}
[33.921444] (-) TimerEvent: {}
[34.021717] (-) TimerEvent: {}
[34.121977] (-) TimerEvent: {}
[34.222333] (-) TimerEvent: {}
[34.322602] (-) TimerEvent: {}
[34.422990] (-) TimerEvent: {}
[34.523277] (-) TimerEvent: {}
[34.623593] (-) TimerEvent: {}
[34.723899] (-) TimerEvent: {}
[34.824285] (-) TimerEvent: {}
[34.924636] (-) TimerEvent: {}
[35.024873] (-) TimerEvent: {}
[35.125169] (-) TimerEvent: {}
[35.225410] (-) TimerEvent: {}
[35.325676] (-) TimerEvent: {}
[35.426016] (-) TimerEvent: {}
[35.526272] (-) TimerEvent: {}
[35.626522] (-) TimerEvent: {}
[35.726780] (-) TimerEvent: {}
[35.827050] (-) TimerEvent: {}
[35.927377] (-) TimerEvent: {}
[36.027661] (-) TimerEvent: {}
[36.128200] (-) TimerEvent: {}
[36.228564] (-) TimerEvent: {}
[36.328940] (-) TimerEvent: {}
[36.429230] (-) TimerEvent: {}
[36.529547] (-) TimerEvent: {}
[36.629857] (-) TimerEvent: {}
[36.730126] (-) TimerEvent: {}
[36.830381] (-) TimerEvent: {}
[36.930710] (-) TimerEvent: {}
[37.031066] (-) TimerEvent: {}
[37.131338] (-) TimerEvent: {}
[37.231622] (-) TimerEvent: {}
[37.331990] (-) TimerEvent: {}
[37.432343] (-) TimerEvent: {}
[37.532624] (-) TimerEvent: {}
[37.632900] (-) TimerEvent: {}
[37.733183] (-) TimerEvent: {}
[37.833450] (-) TimerEvent: {}
[37.933723] (-) TimerEvent: {}
[38.033991] (-) TimerEvent: {}
[38.134276] (-) TimerEvent: {}
[38.234558] (-) TimerEvent: {}
[38.334846] (-) TimerEvent: {}
[38.435134] (-) TimerEvent: {}
[38.535384] (-) TimerEvent: {}
[38.635651] (-) TimerEvent: {}
[38.735884] (-) TimerEvent: {}
[38.836193] (-) TimerEvent: {}
[38.936421] (-) TimerEvent: {}
[39.036769] (-) TimerEvent: {}
[39.137035] (-) TimerEvent: {}
[39.237259] (-) TimerEvent: {}
[39.337615] (-) TimerEvent: {}
[39.437955] (-) TimerEvent: {}
[39.538229] (-) TimerEvent: {}
[39.638502] (-) TimerEvent: {}
[39.738835] (-) TimerEvent: {}
[39.839099] (-) TimerEvent: {}
[39.939330] (-) TimerEvent: {}
[40.039585] (-) TimerEvent: {}
[40.139929] (-) TimerEvent: {}
[40.240182] (-) TimerEvent: {}
[40.340449] (-) TimerEvent: {}
[40.440730] (-) TimerEvent: {}
[40.541012] (-) TimerEvent: {}
[40.641276] (-) TimerEvent: {}
[40.741571] (-) TimerEvent: {}
[40.841871] (-) TimerEvent: {}
[40.942137] (-) TimerEvent: {}
[41.042371] (-) TimerEvent: {}
[41.142596] (-) TimerEvent: {}
[41.242849] (-) TimerEvent: {}
[41.343178] (-) TimerEvent: {}
[41.443418] (-) TimerEvent: {}
[41.543689] (-) TimerEvent: {}
[41.643948] (-) TimerEvent: {}
[41.744193] (-) TimerEvent: {}
[41.844493] (-) TimerEvent: {}
[41.944722] (-) TimerEvent: {}
[42.044944] (-) TimerEvent: {}
[42.145179] (-) TimerEvent: {}
[42.245432] (-) TimerEvent: {}
[42.345720] (-) TimerEvent: {}
[42.446052] (-) TimerEvent: {}
[42.546272] (-) TimerEvent: {}
[42.646502] (-) TimerEvent: {}
[42.746724] (-) TimerEvent: {}
[42.846979] (-) TimerEvent: {}
[42.947236] (-) TimerEvent: {}
[43.047454] (-) TimerEvent: {}
[43.147678] (-) TimerEvent: {}
[43.247948] (-) TimerEvent: {}
[43.348212] (-) TimerEvent: {}
[43.448449] (-) TimerEvent: {}
[43.548707] (-) TimerEvent: {}
[43.649049] (-) TimerEvent: {}
[43.749283] (-) TimerEvent: {}
[43.849527] (-) TimerEvent: {}
[43.949854] (-) TimerEvent: {}
[44.050261] (-) TimerEvent: {}
[44.150511] (-) TimerEvent: {}
[44.250791] (-) TimerEvent: {}
[44.351239] (-) TimerEvent: {}
[44.451496] (-) TimerEvent: {}
[44.551757] (-) TimerEvent: {}
[44.652017] (-) TimerEvent: {}
[44.752266] (-) TimerEvent: {}
[44.852522] (-) TimerEvent: {}
[44.952751] (-) TimerEvent: {}
[45.053001] (-) TimerEvent: {}
[45.153239] (-) TimerEvent: {}
[45.253526] (-) TimerEvent: {}
[45.353894] (-) TimerEvent: {}
[45.454275] (-) TimerEvent: {}
[45.554614] (-) TimerEvent: {}
[45.654909] (-) TimerEvent: {}
[45.755263] (-) TimerEvent: {}
[45.855729] (-) TimerEvent: {}
[45.956035] (-) TimerEvent: {}
[46.056342] (-) TimerEvent: {}
[46.156654] (-) TimerEvent: {}
[46.257090] (-) TimerEvent: {}
[46.357411] (-) TimerEvent: {}
[46.457798] (-) TimerEvent: {}
[46.558098] (-) TimerEvent: {}
[46.658342] (-) TimerEvent: {}
[46.758617] (-) TimerEvent: {}
[46.858974] (-) TimerEvent: {}
[46.959220] (-) TimerEvent: {}
[47.059443] (-) TimerEvent: {}
[47.159675] (-) TimerEvent: {}
[47.260056] (-) TimerEvent: {}
[47.360305] (-) TimerEvent: {}
[47.460635] (-) TimerEvent: {}
[47.560966] (-) TimerEvent: {}
[47.661205] (-) TimerEvent: {}
[47.761482] (-) TimerEvent: {}
[47.861885] (-) TimerEvent: {}
[47.962211] (-) TimerEvent: {}
[48.062456] (-) TimerEvent: {}
[48.162714] (-) TimerEvent: {}
[48.263086] (-) TimerEvent: {}
[48.363341] (-) TimerEvent: {}
[48.463620] (-) TimerEvent: {}
[48.563955] (-) TimerEvent: {}
[48.664190] (-) TimerEvent: {}
[48.764431] (-) TimerEvent: {}
[48.864746] (-) TimerEvent: {}
[48.965115] (-) TimerEvent: {}
[49.065377] (-) TimerEvent: {}
[49.165743] (-) TimerEvent: {}
[49.266138] (-) TimerEvent: {}
[49.366512] (-) TimerEvent: {}
[49.466841] (-) TimerEvent: {}
[49.567062] (-) TimerEvent: {}
[49.667298] (-) TimerEvent: {}
[49.767597] (-) TimerEvent: {}
[49.867896] (-) TimerEvent: {}
[49.968126] (-) TimerEvent: {}
[50.068499] (-) TimerEvent: {}
[50.168773] (-) TimerEvent: {}
[50.269035] (-) TimerEvent: {}
[50.369297] (-) TimerEvent: {}
[50.469570] (-) TimerEvent: {}
[50.569787] (-) TimerEvent: {}
[50.670114] (-) TimerEvent: {}
[50.770397] (-) TimerEvent: {}
[50.870762] (-) TimerEvent: {}
[50.971113] (-) TimerEvent: {}
[51.071339] (-) TimerEvent: {}
[51.171561] (-) TimerEvent: {}
[51.271896] (-) TimerEvent: {}
[51.372413] (-) TimerEvent: {}
[51.472758] (-) TimerEvent: {}
[51.573101] (-) TimerEvent: {}
[51.673340] (-) TimerEvent: {}
[51.773723] (-) TimerEvent: {}
[51.874030] (-) TimerEvent: {}
[51.974273] (-) TimerEvent: {}
[52.074584] (-) TimerEvent: {}
[52.174835] (-) TimerEvent: {}
[52.275088] (-) TimerEvent: {}
[52.375449] (-) TimerEvent: {}
[52.475754] (-) TimerEvent: {}
[52.576132] (-) TimerEvent: {}
[52.676493] (-) TimerEvent: {}
[52.776845] (-) TimerEvent: {}
[52.877358] (-) TimerEvent: {}
[52.977659] (-) TimerEvent: {}
[53.078021] (-) TimerEvent: {}
[53.178286] (-) TimerEvent: {}
[53.278555] (-) TimerEvent: {}
[53.378853] (-) TimerEvent: {}
[53.479184] (-) TimerEvent: {}
[53.579489] (-) TimerEvent: {}
[53.679765] (-) TimerEvent: {}
[53.780091] (-) TimerEvent: {}
[53.880476] (-) TimerEvent: {}
[53.980773] (-) TimerEvent: {}
[54.081119] (-) TimerEvent: {}
[54.181417] (-) TimerEvent: {}
[54.281783] (-) TimerEvent: {}
[54.382156] (-) TimerEvent: {}
[54.482444] (-) TimerEvent: {}
[54.582773] (-) TimerEvent: {}
[54.683168] (-) TimerEvent: {}
[54.783420] (-) TimerEvent: {}
[54.883752] (-) TimerEvent: {}
[54.984019] (-) TimerEvent: {}
[55.084263] (-) TimerEvent: {}
[55.184506] (-) TimerEvent: {}
[55.284806] (-) TimerEvent: {}
[55.385233] (-) TimerEvent: {}
[55.485503] (-) TimerEvent: {}
[55.585802] (-) TimerEvent: {}
[55.686155] (-) TimerEvent: {}
[55.786380] (-) TimerEvent: {}
[55.886645] (-) TimerEvent: {}
[55.986954] (-) TimerEvent: {}
[56.087205] (-) TimerEvent: {}
[56.187460] (-) TimerEvent: {}
[56.287721] (-) TimerEvent: {}
[56.387975] (-) TimerEvent: {}
[56.488166] (-) TimerEvent: {}
[56.588380] (-) TimerEvent: {}
[56.688602] (-) TimerEvent: {}
[56.788838] (-) TimerEvent: {}
[56.889164] (-) TimerEvent: {}
[56.989408] (-) TimerEvent: {}
[57.089658] (-) TimerEvent: {}
[57.189916] (-) TimerEvent: {}
[57.290192] (-) TimerEvent: {}
[57.390475] (-) TimerEvent: {}
[57.490768] (-) TimerEvent: {}
[57.591025] (-) TimerEvent: {}
[57.691291] (-) TimerEvent: {}
[57.791553] (-) TimerEvent: {}
[57.891849] (-) TimerEvent: {}
[57.992162] (-) TimerEvent: {}
[58.092575] (-) TimerEvent: {}
[58.192871] (-) TimerEvent: {}
[58.293138] (-) TimerEvent: {}
[58.393370] (-) TimerEvent: {}
[58.493609] (-) TimerEvent: {}
[58.593945] (-) TimerEvent: {}
[58.694287] (-) TimerEvent: {}
[58.794523] (-) TimerEvent: {}
[58.894755] (-) TimerEvent: {}
[58.995016] (-) TimerEvent: {}
[59.095244] (-) TimerEvent: {}
[59.195485] (-) TimerEvent: {}
[59.295764] (-) TimerEvent: {}
[59.396043] (-) TimerEvent: {}
[59.496307] (-) TimerEvent: {}
[59.596592] (-) TimerEvent: {}
[59.696894] (-) TimerEvent: {}
[59.797168] (-) TimerEvent: {}
[59.897428] (-) TimerEvent: {}
[59.997703] (-) TimerEvent: {}
[60.097982] (-) TimerEvent: {}
[60.198261] (-) TimerEvent: {}
[60.298519] (-) TimerEvent: {}
[60.398974] (-) TimerEvent: {}
[60.499350] (-) TimerEvent: {}
[60.599597] (-) TimerEvent: {}
[60.699947] (-) TimerEvent: {}
[60.800306] (-) TimerEvent: {}
[60.900580] (-) TimerEvent: {}
[61.000847] (-) TimerEvent: {}
[61.101100] (-) TimerEvent: {}
[61.201466] (-) TimerEvent: {}
[61.301745] (-) TimerEvent: {}
[61.402012] (-) TimerEvent: {}
[61.502300] (-) TimerEvent: {}
[61.602671] (-) TimerEvent: {}
[61.703079] (-) TimerEvent: {}
[61.803487] (-) TimerEvent: {}
[61.903839] (-) TimerEvent: {}
[62.004209] (-) TimerEvent: {}
[62.104565] (-) TimerEvent: {}
[62.204876] (-) TimerEvent: {}
[62.305185] (-) TimerEvent: {}
[62.317914] (web) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable web_node\x1b[0m\n'}
[62.405391] (-) TimerEvent: {}
[62.505687] (-) TimerEvent: {}
[62.606082] (-) TimerEvent: {}
[62.706352] (-) TimerEvent: {}
[62.806660] (-) TimerEvent: {}
[62.907041] (-) TimerEvent: {}
[63.007446] (-) TimerEvent: {}
[63.107878] (-) TimerEvent: {}
[63.208194] (-) TimerEvent: {}
[63.308480] (-) TimerEvent: {}
[63.408762] (-) TimerEvent: {}
[63.509054] (-) TimerEvent: {}
[63.609346] (-) TimerEvent: {}
[63.709666] (-) TimerEvent: {}
[63.810094] (-) TimerEvent: {}
[63.910377] (-) TimerEvent: {}
[64.010750] (-) TimerEvent: {}
[64.111129] (-) TimerEvent: {}
[64.211392] (-) TimerEvent: {}
[64.311715] (-) TimerEvent: {}
[64.412129] (-) TimerEvent: {}
[64.512434] (-) TimerEvent: {}
[64.612798] (-) TimerEvent: {}
[64.713195] (-) TimerEvent: {}
[64.813526] (-) TimerEvent: {}
[64.913928] (-) TimerEvent: {}
[65.014324] (-) TimerEvent: {}
[65.114644] (-) TimerEvent: {}
[65.215169] (-) TimerEvent: {}
[65.315599] (-) TimerEvent: {}
[65.416053] (-) TimerEvent: {}
[65.516423] (-) TimerEvent: {}
[65.616730] (-) TimerEvent: {}
[65.717162] (-) TimerEvent: {}
[65.817555] (-) TimerEvent: {}
[65.917924] (-) TimerEvent: {}
[66.018203] (-) TimerEvent: {}
[66.118584] (-) TimerEvent: {}
[66.218877] (-) TimerEvent: {}
[66.319193] (-) TimerEvent: {}
[66.419515] (-) TimerEvent: {}
[66.519820] (-) TimerEvent: {}
[66.620089] (-) TimerEvent: {}
[66.720483] (-) TimerEvent: {}
[66.820754] (-) TimerEvent: {}
[66.921018] (-) TimerEvent: {}
[67.021266] (-) TimerEvent: {}
[67.121625] (-) TimerEvent: {}
[67.222004] (-) TimerEvent: {}
[67.322308] (-) TimerEvent: {}
[67.422577] (-) TimerEvent: {}
[67.522944] (-) TimerEvent: {}
[67.623248] (-) TimerEvent: {}
[67.723548] (-) TimerEvent: {}
[67.823939] (-) TimerEvent: {}
[67.887856] (web) StdoutLine: {'line': b'[100%] Built target web_node\n'}
[67.905384] (web) CommandEnded: {'returncode': 0}
[67.906993] (web) JobProgress: {'identifier': 'web', 'progress': 'install'}
[67.907660] (web) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/xugong_web_ws/build/web'], 'cwd': '/home/<USER>/xugong_web_ws/build/web', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/web'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble')]), 'shell': False}
[67.923346] (web) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[67.923546] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/lib/web/web_node\n'}
[67.924043] (-) TimerEvent: {}
[67.949725] (web) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/xugong_web_ws/install/web/lib/web/web_node" to ""\n'}
[67.949896] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/launch\n'}
[67.949940] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/launch/web_launch.py\n'}
[67.950881] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/package_run_dependencies/web\n'}
[67.950940] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/parent_prefix_path/web\n'}
[67.951044] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/environment/ament_prefix_path.sh\n'}
[67.951101] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/environment/ament_prefix_path.dsv\n'}
[67.951194] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/environment/path.sh\n'}
[67.951244] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/environment/path.dsv\n'}
[67.951313] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.bash\n'}
[67.951425] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.sh\n'}
[67.951510] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.zsh\n'}
[67.951584] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.dsv\n'}
[67.951702] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/package.dsv\n'}
[67.951783] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/packages/web\n'}
[67.951844] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/cmake/webConfig.cmake\n'}
[67.951905] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/cmake/webConfig-version.cmake\n'}
[67.951975] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/package.xml\n'}
[67.953629] (web) CommandEnded: {'returncode': 0}
[67.962844] (web) JobEnded: {'identifier': 'web', 'rc': 0}
[67.963419] (-) EventReactorShutdown: {}
