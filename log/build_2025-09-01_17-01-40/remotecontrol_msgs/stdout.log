-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") 
-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
-- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") 
-- Using PYTHON_EXECUTABLE: /usr/bin/python3
-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
-- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so
-- Found PythonExtra: .so  
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/xugong_web_ws/build/remotecontrol_msgs
[  2%] [34m[1mGenerating C code for ROS interfaces[0m
[  5%] [34m[1mGenerating C++ code for ROS interfaces[0m
[  5%] Built target ament_cmake_python_copy_remotecontrol_msgs
running egg_info
creating remotecontrol_msgs.egg-info
writing remotecontrol_msgs.egg-info/PKG-INFO
writing dependency_links to remotecontrol_msgs.egg-info/dependency_links.txt
writing top-level names to remotecontrol_msgs.egg-info/top_level.txt
writing manifest file 'remotecontrol_msgs.egg-info/SOURCES.txt'
reading manifest file 'remotecontrol_msgs.egg-info/SOURCES.txt'
writing manifest file 'remotecontrol_msgs.egg-info/SOURCES.txt'
[  5%] Built target ament_cmake_python_build_remotecontrol_msgs_egg
[  7%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_generator_c.dir/rosidl_generator_c/remotecontrol_msgs/msg/detail/key_state__functions.c.o[0m
[ 10%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_generator_c.dir/rosidl_generator_c/remotecontrol_msgs/msg/detail/joystick_state__functions.c.o[0m
[ 10%] Built target remotecontrol_msgs__cpp
[ 15%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[ 15%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[ 17%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[ 20%] [32m[1mLinking C shared library libremotecontrol_msgs__rosidl_generator_c.so[0m
[ 20%] Built target remotecontrol_msgs__rosidl_generator_c
[ 23%] [34m[1mGenerating C introspection for ROS interfaces[0m
[ 25%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
[ 28%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[ 30%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/remotecontrol_msgs/msg/detail/key_state__type_support.cpp.o[0m
[ 33%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/remotecontrol_msgs/msg/detail/joystick_state__type_support.cpp.o[0m
[ 35%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/remotecontrol_msgs/msg/joystick_state__type_support.cpp.o[0m
[ 41%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/remotecontrol_msgs/msg/detail/joystick_state__type_support_c.cpp.o[0m
[ 41%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/remotecontrol_msgs/msg/detail/key_state__type_support_c.cpp.o[0m
[ 43%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/remotecontrol_msgs/msg/key_state__type_support.cpp.o[0m
[ 46%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/remotecontrol_msgs/msg/joystick_state__type_support.cpp.o[0m
[ 51%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/remotecontrol_msgs/msg/key_state__type_support.cpp.o[0m
[ 48%] [32m[1mLinking CXX shared library libremotecontrol_msgs__rosidl_typesupport_c.so[0m
[ 53%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/remotecontrol_msgs/msg/detail/dds_fastrtps/joystick_state__type_support.cpp.o[0m
[ 56%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/remotecontrol_msgs/msg/detail/dds_fastrtps/key_state__type_support.cpp.o[0m
[ 56%] Built target remotecontrol_msgs__rosidl_typesupport_c
[ 58%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/remotecontrol_msgs/msg/detail/key_state__type_support.c.o[0m
[ 61%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/remotecontrol_msgs/msg/detail/joystick_state__type_support.c.o[0m
[ 64%] [32m[1mLinking C shared library libremotecontrol_msgs__rosidl_typesupport_introspection_c.so[0m
[ 64%] Built target remotecontrol_msgs__rosidl_typesupport_introspection_c
[ 66%] [32m[1mLinking CXX shared library libremotecontrol_msgs__rosidl_typesupport_cpp.so[0m
[ 66%] Built target remotecontrol_msgs__rosidl_typesupport_cpp
[ 69%] [32m[1mLinking CXX shared library libremotecontrol_msgs__rosidl_typesupport_fastrtps_cpp.so[0m
[ 71%] [32m[1mLinking CXX shared library libremotecontrol_msgs__rosidl_typesupport_introspection_cpp.so[0m
[ 74%] [32m[1mLinking CXX shared library libremotecontrol_msgs__rosidl_typesupport_fastrtps_c.so[0m
[ 74%] Built target remotecontrol_msgs__rosidl_typesupport_introspection_cpp
[ 74%] Built target remotecontrol_msgs__rosidl_typesupport_fastrtps_cpp
[ 74%] Built target remotecontrol_msgs__rosidl_typesupport_fastrtps_c
[ 74%] Built target remotecontrol_msgs
[ 76%] [34m[1mGenerating Python code for ROS interfaces[0m
[ 76%] Built target remotecontrol_msgs__py
[ 79%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_generator_py.dir/rosidl_generator_py/remotecontrol_msgs/msg/_key_state_s.c.o[0m
[ 82%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_generator_py.dir/rosidl_generator_py/remotecontrol_msgs/msg/_joystick_state_s.c.o[0m
[ 84%] [32m[1mLinking C shared library rosidl_generator_py/remotecontrol_msgs/libremotecontrol_msgs__rosidl_generator_py.so[0m
[ 84%] Built target remotecontrol_msgs__rosidl_generator_py
[ 87%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_fastrtps_c__pyext.dir/rosidl_generator_py/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_fastrtps_c.c.o[0m
[ 89%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_c__pyext.dir/rosidl_generator_py/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_c.c.o[0m
[ 92%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_introspection_c__pyext.dir/rosidl_generator_py/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_introspection_c.c.o[0m
[ 94%] [32m[1mLinking C shared library rosidl_generator_py/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so[0m
[ 97%] [32m[1mLinking C shared library rosidl_generator_py/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so[0m
[100%] [32m[1mLinking C shared library rosidl_generator_py/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so[0m
[100%] Built target remotecontrol_msgs__rosidl_typesupport_c__pyext
[100%] Built target remotecontrol_msgs__rosidl_typesupport_fastrtps_c__pyext
[100%] Built target remotecontrol_msgs__rosidl_typesupport_introspection_c__pyext
-- Install configuration: ""
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/ament_index/resource_index/rosidl_interfaces/remotecontrol_msgs
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/joystick_state.h
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/key_state.h
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_generator_c__visibility_control.h
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__struct.h
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__type_support.h
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__functions.h
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__functions.c
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__struct.h
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__functions.h
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__type_support.h
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__functions.c
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/library_path.sh
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/library_path.dsv
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_generator_c.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_generator_c.so" to ""
-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs
-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__rosidl_typesupport_fastrtps_c.h
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__rosidl_typesupport_fastrtps_c.h
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_fastrtps_c.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_fastrtps_c.so" to ""
-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs
-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/joystick_state.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/key_state.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_generator_cpp__visibility_control.hpp
-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__struct.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__type_support.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__builder.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__traits.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__struct.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__traits.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__builder.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__type_support.hpp
-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs
-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/dds_fastrtps
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__rosidl_typesupport_fastrtps_cpp.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__rosidl_typesupport_fastrtps_cpp.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_fastrtps_cpp.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_fastrtps_cpp.so" to ""
-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs
-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h
-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__type_support.c
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__type_support.c
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__rosidl_typesupport_introspection_c.h
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__rosidl_typesupport_introspection_c.h
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_introspection_c.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_introspection_c.so" to ""
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_c.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_c.so" to ""
-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs
-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg
-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__type_support.cpp
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__type_support.cpp
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__rosidl_typesupport_introspection_cpp.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__rosidl_typesupport_introspection_cpp.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_introspection_cpp.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_introspection_cpp.so" to ""
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_cpp.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_cpp.so" to ""
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/pythonpath.sh
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/pythonpath.dsv
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info/dependency_links.txt
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info/SOURCES.txt
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info/top_level.txt
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info/PKG-INFO
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_c.c
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_introspection_c.c
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/libremotecontrol_msgs__rosidl_generator_py.so
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_fastrtps_c.c
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_joystick_state.py
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_key_state.py
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_key_state_s.c
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_joystick_state_s.c
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/__init__.py
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/__init__.py
Listing '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs'...
Compiling '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/__init__.py'...
Listing '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg'...
Compiling '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/__init__.py'...
Compiling '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_joystick_state.py'...
Compiling '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_key_state.py'...
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so" to ""
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so" to ""
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so" to ""
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_generator_py.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_generator_py.so" to ""
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/msg/JoystickState.idl
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/msg/KeyState.idl
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/msg/JoystickState.msg
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/msg/KeyState.msg
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/ament_index/resource_index/package_run_dependencies/remotecontrol_msgs
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/ament_index/resource_index/parent_prefix_path/remotecontrol_msgs
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/path.sh
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/path.dsv
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/local_setup.bash
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/local_setup.sh
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/local_setup.zsh
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/local_setup.dsv
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.dsv
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/ament_index/resource_index/packages/remotecontrol_msgs
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_cExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_cExport-noconfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_typesupport_fastrtps_cExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_typesupport_fastrtps_cExport-noconfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_cppExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_typesupport_fastrtps_cppExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_introspection_cExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_introspection_cExport-noconfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_cExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_cExport-noconfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_introspection_cppExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_introspection_cppExport-noconfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_cppExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_cppExport-noconfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_pyExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_pyExport-noconfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/rosidl_cmake-extras.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/ament_cmake_export_dependencies-extras.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/ament_cmake_export_include_directories-extras.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/ament_cmake_export_libraries-extras.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/ament_cmake_export_targets-extras.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgsConfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgsConfig-version.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.xml
