Invoking command in '/home/<USER>/xugong_web_ws/build/web': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/web -- -j16 -l16
Invoked command in '/home/<USER>/xugong_web_ws/build/web' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/web -- -j16 -l16
Invoking command in '/home/<USER>/xugong_web_ws/build/web': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/web
Invoked command in '/home/<USER>/xugong_web_ws/build/web' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/web
