[0.078s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.078s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7c3821345db0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7c3821345960>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7c3821345960>>)
[0.256s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.256s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.256s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.256s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.256s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.256s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.256s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/xugong_web_ws'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extensions ['ignore', 'ignore_ament_install']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extension 'ignore'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extension 'ignore_ament_install'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extensions ['colcon_pkg']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extension 'colcon_pkg'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extensions ['colcon_meta']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extension 'colcon_meta'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extensions ['ros']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extension 'ros'
[0.268s] DEBUG:colcon.colcon_core.package_identification:Package 'src/can_bridge' with type 'ros.ament_cmake' and name 'can_bridge'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extensions ['ignore', 'ignore_ament_install']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extension 'ignore'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extension 'ignore_ament_install'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extensions ['colcon_pkg']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extension 'colcon_pkg'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extensions ['colcon_meta']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extension 'colcon_meta'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extensions ['ros']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extension 'ros'
[0.268s] DEBUG:colcon.colcon_core.package_identification:Package 'src/global_traj_generate' with type 'ros.ament_cmake' and name 'global_traj_generate'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extension 'ignore'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extension 'ignore_ament_install'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extensions ['colcon_pkg']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extension 'colcon_pkg'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extensions ['colcon_meta']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extension 'colcon_meta'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extensions ['ros']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extension 'ros'
[0.269s] DEBUG:colcon.colcon_core.package_identification:Package 'src/keyframe_msgs' with type 'ros.ament_cmake' and name 'keyframe_msgs'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extension 'ignore'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extension 'ignore_ament_install'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extensions ['colcon_pkg']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extension 'colcon_pkg'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extensions ['colcon_meta']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extension 'colcon_meta'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extensions ['ros']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extension 'ros'
[0.270s] DEBUG:colcon.colcon_core.package_identification:Package 'src/remotecontrol_msgs' with type 'ros.ament_cmake' and name 'remotecontrol_msgs'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extensions ['ignore', 'ignore_ament_install']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extension 'ignore'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extension 'ignore_ament_install'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extensions ['colcon_pkg']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extension 'colcon_pkg'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extensions ['colcon_meta']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extension 'colcon_meta'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extensions ['ros']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extension 'ros'
[0.271s] DEBUG:colcon.colcon_core.package_identification:Package 'src/web' with type 'ros.ament_cmake' and name 'web'
[0.271s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.271s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.271s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.271s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.271s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.292s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.292s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.294s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 5 installed packages in /home/<USER>/xugong_web_ws/install
[0.295s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 473 installed packages in /opt/ros/humble
[0.296s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.330s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'cmake_args' from command line to 'None'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'cmake_target' from command line to 'None'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'cmake_clean_cache' from command line to 'False'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'cmake_clean_first' from command line to 'False'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'cmake_force_configure' from command line to 'False'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'ament_cmake_args' from command line to 'None'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'catkin_cmake_args' from command line to 'None'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.330s] DEBUG:colcon.colcon_core.verb:Building package 'can_bridge' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/xugong_web_ws/build/can_bridge', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/xugong_web_ws/install/can_bridge', 'merge_install': False, 'path': '/home/<USER>/xugong_web_ws/src/can_bridge', 'symlink_install': False, 'test_result_base': None}
[0.330s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_args' from command line to 'None'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_target' from command line to 'None'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_clean_cache' from command line to 'False'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_clean_first' from command line to 'False'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_force_configure' from command line to 'False'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'ament_cmake_args' from command line to 'None'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'catkin_cmake_args' from command line to 'None'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.330s] DEBUG:colcon.colcon_core.verb:Building package 'global_traj_generate' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/xugong_web_ws/build/global_traj_generate', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/xugong_web_ws/install/global_traj_generate', 'merge_install': False, 'path': '/home/<USER>/xugong_web_ws/src/global_traj_generate', 'symlink_install': False, 'test_result_base': None}
[0.331s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'cmake_target' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.331s] DEBUG:colcon.colcon_core.verb:Building package 'keyframe_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/xugong_web_ws/build/keyframe_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/xugong_web_ws/install/keyframe_msgs', 'merge_install': False, 'path': '/home/<USER>/xugong_web_ws/src/keyframe_msgs', 'symlink_install': False, 'test_result_base': None}
[0.331s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'cmake_target' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.331s] DEBUG:colcon.colcon_core.verb:Building package 'remotecontrol_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs', 'merge_install': False, 'path': '/home/<USER>/xugong_web_ws/src/remotecontrol_msgs', 'symlink_install': False, 'test_result_base': None}
[0.331s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'cmake_target' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'cmake_clean_cache' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'cmake_clean_first' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'cmake_force_configure' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'ament_cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'catkin_cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.331s] DEBUG:colcon.colcon_core.verb:Building package 'web' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/xugong_web_ws/build/web', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/xugong_web_ws/install/web', 'merge_install': False, 'path': '/home/<USER>/xugong_web_ws/src/web', 'symlink_install': False, 'test_result_base': None}
[0.331s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.332s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.332s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/xugong_web_ws/src/can_bridge' with build type 'ament_cmake'
[0.333s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/xugong_web_ws/src/can_bridge'
[0.336s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.336s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.336s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.339s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/xugong_web_ws/src/global_traj_generate' with build type 'ament_cmake'
[0.339s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/xugong_web_ws/src/global_traj_generate'
[0.339s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.339s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.341s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/xugong_web_ws/src/keyframe_msgs' with build type 'ament_cmake'
[0.341s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/xugong_web_ws/src/keyframe_msgs'
[0.341s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.341s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.344s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/xugong_web_ws/src/remotecontrol_msgs' with build type 'ament_cmake'
[0.344s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/xugong_web_ws/src/remotecontrol_msgs'
[0.344s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.344s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.351s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/can_bridge': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/can_bridge -- -j16 -l16
[0.353s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/global_traj_generate': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/global_traj_generate -- -j16 -l16
[0.357s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/keyframe_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/keyframe_msgs -- -j16 -l16
[0.361s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/remotecontrol_msgs -- -j16 -l16
[0.651s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/remotecontrol_msgs -- -j16 -l16
[0.661s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/remotecontrol_msgs
[0.662s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/can_bridge' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/can_bridge -- -j16 -l16
[0.663s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/can_bridge': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/can_bridge
[0.676s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/global_traj_generate' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/global_traj_generate -- -j16 -l16
[0.685s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/global_traj_generate': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/global_traj_generate
[0.687s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/keyframe_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/keyframe_msgs -- -j16 -l16
[0.687s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/keyframe_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/keyframe_msgs
[0.704s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(remotecontrol_msgs)
[0.709s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/remotecontrol_msgs
[0.713s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs' for CMake module files
[0.713s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs' for CMake config files
[0.713s] Level 1:colcon.colcon_core.shell:create_environment_hook('remotecontrol_msgs', 'cmake_prefix_path')
[0.713s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/cmake_prefix_path.ps1'
[0.714s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/cmake_prefix_path.dsv'
[0.714s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/cmake_prefix_path.sh'
[0.715s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib'
[0.715s] Level 1:colcon.colcon_core.shell:create_environment_hook('remotecontrol_msgs', 'ld_library_path_lib')
[0.716s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/ld_library_path_lib.ps1'
[0.716s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/ld_library_path_lib.dsv'
[0.717s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/ld_library_path_lib.sh'
[0.718s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/bin'
[0.718s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/pkgconfig/remotecontrol_msgs.pc'
[0.718s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/python3.10/site-packages'
[0.718s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/bin'
[0.718s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.ps1'
[0.719s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.dsv'
[0.720s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.sh'
[0.722s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.bash'
[0.723s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.zsh'
[0.724s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/colcon-core/packages/remotecontrol_msgs)
[0.725s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(remotecontrol_msgs)
[0.725s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs' for CMake module files
[0.726s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs' for CMake config files
[0.729s] Level 1:colcon.colcon_core.shell:create_environment_hook('remotecontrol_msgs', 'cmake_prefix_path')
[0.729s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/cmake_prefix_path.ps1'
[0.729s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/cmake_prefix_path.dsv'
[0.730s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/cmake_prefix_path.sh'
[0.730s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib'
[0.731s] Level 1:colcon.colcon_core.shell:create_environment_hook('remotecontrol_msgs', 'ld_library_path_lib')
[0.731s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/ld_library_path_lib.ps1'
[0.731s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/ld_library_path_lib.dsv'
[0.732s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/ld_library_path_lib.sh'
[0.732s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/bin'
[0.732s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/pkgconfig/remotecontrol_msgs.pc'
[0.732s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/python3.10/site-packages'
[0.732s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/bin'
[0.733s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.ps1'
[0.733s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.dsv'
[0.734s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.sh'
[0.734s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.bash'
[0.735s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.zsh'
[0.735s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/colcon-core/packages/remotecontrol_msgs)
[0.737s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(can_bridge)
[0.738s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge' for CMake module files
[0.738s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/can_bridge' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/can_bridge
[0.739s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge' for CMake config files
[0.739s] Level 1:colcon.colcon_core.shell:create_environment_hook('can_bridge', 'cmake_prefix_path')
[0.740s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/cmake_prefix_path.ps1'
[0.740s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/cmake_prefix_path.dsv'
[0.741s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/cmake_prefix_path.sh'
[0.741s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/lib'
[0.741s] Level 1:colcon.colcon_core.shell:create_environment_hook('can_bridge', 'ld_library_path_lib')
[0.742s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/ld_library_path_lib.ps1'
[0.742s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/ld_library_path_lib.dsv'
[0.743s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/ld_library_path_lib.sh'
[0.743s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/bin'
[0.743s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/lib/pkgconfig/can_bridge.pc'
[0.744s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/lib/python3.10/site-packages'
[0.744s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/bin'
[0.744s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.ps1'
[0.745s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.dsv'
[0.746s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.sh'
[0.746s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.bash'
[0.747s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.zsh'
[0.747s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/can_bridge/share/colcon-core/packages/can_bridge)
[0.748s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(can_bridge)
[0.748s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge' for CMake module files
[0.748s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge' for CMake config files
[0.748s] Level 1:colcon.colcon_core.shell:create_environment_hook('can_bridge', 'cmake_prefix_path')
[0.748s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/cmake_prefix_path.ps1'
[0.749s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/cmake_prefix_path.dsv'
[0.749s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/cmake_prefix_path.sh'
[0.749s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/lib'
[0.749s] Level 1:colcon.colcon_core.shell:create_environment_hook('can_bridge', 'ld_library_path_lib')
[0.750s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/ld_library_path_lib.ps1'
[0.750s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/ld_library_path_lib.dsv'
[0.750s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/ld_library_path_lib.sh'
[0.750s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/bin'
[0.750s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/lib/pkgconfig/can_bridge.pc'
[0.751s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/lib/python3.10/site-packages'
[0.751s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/bin'
[0.751s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.ps1'
[0.751s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.dsv'
[0.752s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.sh'
[0.752s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.bash'
[0.752s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.zsh'
[0.752s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/can_bridge/share/colcon-core/packages/can_bridge)
[0.753s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(global_traj_generate)
[0.753s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate' for CMake module files
[0.754s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/global_traj_generate' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/global_traj_generate
[0.754s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate' for CMake config files
[0.754s] Level 1:colcon.colcon_core.shell:create_environment_hook('global_traj_generate', 'cmake_prefix_path')
[0.754s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.ps1'
[0.754s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.dsv'
[0.755s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.sh'
[0.755s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/lib'
[0.755s] Level 1:colcon.colcon_core.shell:create_environment_hook('global_traj_generate', 'ld_library_path_lib')
[0.755s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.ps1'
[0.755s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.dsv'
[0.755s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.sh'
[0.756s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/bin'
[0.756s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/lib/pkgconfig/global_traj_generate.pc'
[0.756s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/lib/python3.10/site-packages'
[0.756s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/bin'
[0.756s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.ps1'
[0.757s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.dsv'
[0.757s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.sh'
[0.757s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.bash'
[0.757s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.zsh'
[0.758s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/global_traj_generate/share/colcon-core/packages/global_traj_generate)
[0.758s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(global_traj_generate)
[0.758s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate' for CMake module files
[0.759s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate' for CMake config files
[0.759s] Level 1:colcon.colcon_core.shell:create_environment_hook('global_traj_generate', 'cmake_prefix_path')
[0.759s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.ps1'
[0.759s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.dsv'
[0.759s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.sh'
[0.760s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/lib'
[0.760s] Level 1:colcon.colcon_core.shell:create_environment_hook('global_traj_generate', 'ld_library_path_lib')
[0.760s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.ps1'
[0.760s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.dsv'
[0.760s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.sh'
[0.761s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/bin'
[0.761s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/lib/pkgconfig/global_traj_generate.pc'
[0.761s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/lib/python3.10/site-packages'
[0.761s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/bin'
[0.761s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.ps1'
[0.762s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.dsv'
[0.762s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.sh'
[0.762s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.bash'
[0.762s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.zsh'
[0.763s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/global_traj_generate/share/colcon-core/packages/global_traj_generate)
[0.763s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(keyframe_msgs)
[0.763s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs' for CMake module files
[0.764s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/keyframe_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/keyframe_msgs
[0.764s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs' for CMake config files
[0.764s] Level 1:colcon.colcon_core.shell:create_environment_hook('keyframe_msgs', 'cmake_prefix_path')
[0.764s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/cmake_prefix_path.ps1'
[0.765s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/cmake_prefix_path.dsv'
[0.765s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/cmake_prefix_path.sh'
[0.765s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib'
[0.765s] Level 1:colcon.colcon_core.shell:create_environment_hook('keyframe_msgs', 'ld_library_path_lib')
[0.765s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/ld_library_path_lib.ps1'
[0.766s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/ld_library_path_lib.dsv'
[0.766s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/ld_library_path_lib.sh'
[0.766s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/bin'
[0.766s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/pkgconfig/keyframe_msgs.pc'
[0.766s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/python3.10/site-packages'
[0.766s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/bin'
[0.766s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.ps1'
[0.767s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.dsv'
[0.767s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.sh'
[0.767s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.bash'
[0.767s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.zsh'
[0.768s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/colcon-core/packages/keyframe_msgs)
[0.768s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(keyframe_msgs)
[0.768s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs' for CMake module files
[0.768s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs' for CMake config files
[0.769s] Level 1:colcon.colcon_core.shell:create_environment_hook('keyframe_msgs', 'cmake_prefix_path')
[0.769s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/cmake_prefix_path.ps1'
[0.769s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/cmake_prefix_path.dsv'
[0.769s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/cmake_prefix_path.sh'
[0.769s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib'
[0.769s] Level 1:colcon.colcon_core.shell:create_environment_hook('keyframe_msgs', 'ld_library_path_lib')
[0.770s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/ld_library_path_lib.ps1'
[0.770s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/ld_library_path_lib.dsv'
[0.770s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/ld_library_path_lib.sh'
[0.770s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/bin'
[0.770s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/pkgconfig/keyframe_msgs.pc'
[0.770s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/python3.10/site-packages'
[0.770s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/bin'
[0.771s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.ps1'
[0.771s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.dsv'
[0.771s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.sh'
[0.771s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.bash'
[0.772s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.zsh'
[0.772s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/colcon-core/packages/keyframe_msgs)
[0.772s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/xugong_web_ws/src/web' with build type 'ament_cmake'
[0.772s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/xugong_web_ws/src/web'
[0.772s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.772s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.792s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/web': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/web -- -j16 -l16
[0.901s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/web' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/web -- -j16 -l16
[0.903s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/web': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/web
[0.913s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(web)
[0.913s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/web' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/web
[0.914s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web' for CMake module files
[0.914s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web' for CMake config files
[0.915s] Level 1:colcon.colcon_core.shell:create_environment_hook('web', 'cmake_prefix_path')
[0.915s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/web/share/web/hook/cmake_prefix_path.ps1'
[0.915s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/web/share/web/hook/cmake_prefix_path.dsv'
[0.915s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/web/share/web/hook/cmake_prefix_path.sh'
[0.916s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/lib'
[0.916s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/bin'
[0.916s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/lib/pkgconfig/web.pc'
[0.916s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/lib/python3.10/site-packages'
[0.916s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/bin'
[0.916s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/web/share/web/package.ps1'
[0.916s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/web/share/web/package.dsv'
[0.917s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/web/share/web/package.sh'
[0.917s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/web/share/web/package.bash'
[0.917s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/web/share/web/package.zsh'
[0.917s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/web/share/colcon-core/packages/web)
[0.918s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(web)
[0.918s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web' for CMake module files
[0.918s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web' for CMake config files
[0.918s] Level 1:colcon.colcon_core.shell:create_environment_hook('web', 'cmake_prefix_path')
[0.918s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/web/share/web/hook/cmake_prefix_path.ps1'
[0.918s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/web/share/web/hook/cmake_prefix_path.dsv'
[0.919s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/web/share/web/hook/cmake_prefix_path.sh'
[0.919s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/lib'
[0.919s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/bin'
[0.919s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/lib/pkgconfig/web.pc'
[0.919s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/lib/python3.10/site-packages'
[0.919s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/bin'
[0.919s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/web/share/web/package.ps1'
[0.920s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/web/share/web/package.dsv'
[0.920s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/web/share/web/package.sh'
[0.920s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/web/share/web/package.bash'
[0.921s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/web/share/web/package.zsh'
[0.921s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/web/share/colcon-core/packages/web)
[0.921s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.921s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.921s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.921s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.925s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.925s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.925s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.935s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.935s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/xugong_web_ws/install/local_setup.ps1'
[0.936s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/xugong_web_ws/install/_local_setup_util_ps1.py'
[0.937s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/xugong_web_ws/install/setup.ps1'
[0.938s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/xugong_web_ws/install/local_setup.sh'
[0.938s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/xugong_web_ws/install/_local_setup_util_sh.py'
[0.939s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/xugong_web_ws/install/setup.sh'
[0.940s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/xugong_web_ws/install/local_setup.bash'
[0.940s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/xugong_web_ws/install/setup.bash'
[0.941s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/xugong_web_ws/install/local_setup.zsh'
[0.941s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/xugong_web_ws/install/setup.zsh'
