[0.012s] Invoking command in '/home/<USER>/xugong_web_ws/build/global_traj_generate': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/global_traj_generate -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/global_traj_generate
[0.110s] -- The C compiler identification is GNU 11.4.0
[0.176s] -- The CXX compiler identification is GNU 11.4.0
[0.183s] -- Detecting C compiler ABI info
[0.234s] -- Detecting C compiler ABI info - done
[0.239s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.240s] -- Detecting C compile features
[0.241s] -- Detecting C compile features - done
[0.243s] -- Detecting CXX compiler ABI info
[0.305s] -- Detecting CXX compiler AB<PERSON> info - done
[0.313s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.313s] -- Detecting CXX compile features
[0.313s] -- Detecting CXX compile features - done
[0.315s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.434s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.496s] -- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)
[0.526s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.530s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.538s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.547s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.561s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.580s] -- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[0.590s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.604s] -- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
[0.837s] -- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
[1.204s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.330s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.348s] -- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") 
[1.351s] -- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
[1.374s] -- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") 
[1.374s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[1.374s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
[1.374s] -- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so
[1.390s] -- Found PythonExtra: .so  
[1.442s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.460s] -- Added test 'lint_cmake' to check CMake code style
[1.461s] -- Added test 'xmllint' to check XML markup files
[1.463s] -- Configuring done
[1.485s] -- Generating done
[1.493s] -- Build files have been written to: /home/<USER>/xugong_web_ws/build/global_traj_generate
[1.500s] Invoked command in '/home/<USER>/xugong_web_ws/build/global_traj_generate' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/global_traj_generate -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/global_traj_generate
[1.503s] Invoking command in '/home/<USER>/xugong_web_ws/build/global_traj_generate': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/global_traj_generate -- -j16 -l16
[1.530s] [  3%] [34m[1mGenerating C code for ROS interfaces[0m
[1.537s] [  6%] [34m[1mGenerating C++ code for ROS interfaces[0m
[1.548s] [  6%] Built target ament_cmake_python_copy_global_traj_generate
[1.796s] [  9%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_generator_c.dir/rosidl_generator_c/global_traj_generate/msg/detail/lateral_deviation__functions.c.o[0m
[1.847s] running egg_info
[1.847s] creating global_traj_generate.egg-info
[1.850s] writing global_traj_generate.egg-info/PKG-INFO
[1.850s] writing dependency_links to global_traj_generate.egg-info/dependency_links.txt
[1.850s] writing top-level names to global_traj_generate.egg-info/top_level.txt
[1.851s] writing manifest file 'global_traj_generate.egg-info/SOURCES.txt'
[1.851s] reading manifest file 'global_traj_generate.egg-info/SOURCES.txt'
[1.851s] writing manifest file 'global_traj_generate.egg-info/SOURCES.txt'
[1.853s] [ 12%] [32m[1mLinking C shared library libglobal_traj_generate__rosidl_generator_c.so[0m
[1.872s] [ 12%] Built target global_traj_generate__cpp
[1.887s] [ 16%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[1.887s] [ 16%] Built target ament_cmake_python_build_global_traj_generate_egg
[1.890s] [ 19%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[1.891s] [ 22%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[1.896s] [ 22%] Built target global_traj_generate__rosidl_generator_c
[1.904s] [ 25%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[1.907s] [ 29%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
[1.912s] [ 32%] [34m[1mGenerating C introspection for ROS interfaces[0m
[2.196s] [ 35%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/global_traj_generate/msg/detail/lateral_deviation__type_support_c.cpp.o[0m
[2.223s] [ 38%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/global_traj_generate/msg/lateral_deviation__type_support.cpp.o[0m
[2.245s] [ 41%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_c.dir/rosidl_typesupport_c/global_traj_generate/msg/lateral_deviation__type_support.cpp.o[0m
[2.272s] [ 45%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/global_traj_generate/msg/detail/lateral_deviation__type_support.c.o[0m
[2.294s] [ 48%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/global_traj_generate/msg/detail/lateral_deviation__type_support.cpp.o[0m
[2.300s] [ 51%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/global_traj_generate/msg/detail/dds_fastrtps/lateral_deviation__type_support.cpp.o[0m
[2.320s] [ 54%] [32m[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_c.so[0m
[2.350s] [ 58%] [32m[1mLinking C shared library libglobal_traj_generate__rosidl_typesupport_introspection_c.so[0m
[2.412s] [ 58%] Built target global_traj_generate__rosidl_typesupport_c
[2.440s] [ 58%] Built target global_traj_generate__rosidl_typesupport_introspection_c
