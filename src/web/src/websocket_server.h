#ifndef WEBSOCKET_SERVER_H
#define WEBSOCKET_SERVER_H

#include "hv/WebSocketServer.h"
#include "hv/htime.h"
#include "hv/HttpClient.h"
#include "hv/Event.h"
#include "hv/EventLoop.h"
#include <iostream>
#include <unordered_set>
#include <mutex>
#include <nlohmann/json.hpp>
#include <functional>
#include <string>
#include <unordered_map>
#include "common.h"
#include "yaml-cpp/yaml.h"
#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <nav_msgs/msg/path.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#include "sensor_msgs/msg/point_cloud2.hpp"
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/io/pcd_io.h>
#include <filesystem>
#include <thread>
#include <mutex>
#include "file_update.h"
#include "std_msgs/msg/string.hpp"
#include "std_msgs/msg/float32.hpp"
#include "nav_msgs/msg/odometry.hpp"
#include "nav_msgs/msg/odometry.hpp"
#include "geometry_msgs/msg/quaternion.hpp"
#include "std_msgs/msg/bool.hpp"
#include <tf2/LinearMath/Quaternion.h>
#include <tf2/LinearMath/Matrix3x3.h>
#include "std_msgs/msg/int8.hpp"
#include "file_download.h"
#include "file_update.h"
#include "can_bridge/msg/vehicle_control_flags.hpp"
#include "can_bridge/msg/vehicle_diagnostic.hpp"
#include "can_bridge/msg/vehicle_motion.hpp"
#include "map_server.h"
#include "keyframe_msgs/msg/key_frame_pose_array.hpp"
#include "keyframe_msgs/msg/key_frame_pose.hpp"   
#include "keyframe_msgs/msg/key_frame.hpp"
#include "remotecontrol_msgs/msg/joystick_state.hpp"
#include "remotecontrol_msgs/msg/key_state.hpp"
#include "geometry_msgs/msg/vector3.hpp" 
#include "global_traj_generate/msg/lateral_deviation.hpp"
// 在websocket_server.h顶部添加
#include "sensor_msgs/msg/nav_sat_fix.hpp"  // ROS 2的消息头文件（注意.hpp后缀）

namespace fs = std::filesystem;

using json = nlohmann::json;
using namespace hv;

class WebSocketServerSingleton {
public:
    using MsgHandler = std::function<void(const std::string&)>;

    // 获取单例实例
    static WebSocketServerSingleton& getInstance(int port = 2052) {
        static WebSocketServerSingleton instance(port);
        return instance;
    }

    // 禁止拷贝和赋值
    WebSocketServerSingleton(const WebSocketServerSingleton&) = delete;
    WebSocketServerSingleton& operator=(const WebSocketServerSingleton&) = delete;

    void start() {
		loadConfigFromYAML(PKG_YAML_PATH, pkg_configs);

        cameraConfig = loadConfig(config_yaml_path);

        std::cout << "front: " << cameraConfig.qzsxt << std::endl;
        std::cout << "rear: " << cameraConfig.hzsxt << std::endl;

        initMessageHandlers();  //消息处理

        ws_.onopen = [this](const WebSocketChannelPtr& channel, const HttpRequestPtr& ) {
            std::lock_guard<std::mutex> lock(mutex_);
            clients_.insert(channel);
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "Client Connected successfully");

            if (!timer_running_) {
            }
        };

        ws_.onmessage = [this](const WebSocketChannelPtr& , const std::string& msg) {
            // std::cout << "Received message: " << msg << std::endl;
            try {
                json j = json::parse(msg);
                std::string cmd = j["type"];
                clear_heart_cnt();
                auto it = msgHandlers.find(cmd);
                if (it != msgHandlers.end()) {
                    it->second(msg);
                } else {
                    RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "Unknown message type: %s", cmd.c_str());
                }
            } catch (const std::exception& e) {
                std::cerr << "Failed to parse message: " << e.what() << std::endl;
                RCLCPP_ERROR(rclcpp::get_logger(NODE_NAME), "Failed to parse message: %s", e.what());
            }
        };

        ws_.onclose = [this](const WebSocketChannelPtr& channel) {
            std::lock_guard<std::mutex> lock(mutex_);
            clients_.erase(channel);
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "[Websocket] Client disconnected");

            if (clients_.empty() && timer_running_) {
                hv::killTimer(timer_id_);
                timer_running_ = false;
            }
        };

        server_.port = port_;
        
        server_.ws = &ws_;
       
        RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "WebSocketTimeServer running on port %d", port_);
        websocket_server_run(&server_, 0);
    }

    void broadcast(const std::string& message) {
        // static int count = 0;
        // count++;
        // if (count %10 == 0)
        // {
        //     count = 0;
        //     RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "websocket broadcast->%s", message.c_str());
        // }
        
        std::lock_guard<std::mutex> lock(mutex_);
        for (auto& client : clients_) {
            if (client->isConnected()) {
                client->send(message);
            }
        }
    }

    std::string get_sn(void){
        return sn_;
    }

    void set_sn(std::string sn){
        sn_ = sn;
    }

    std::string get_area_id(void){
        return area_id_;
    }

    void set_area_id(std::string area_id){
        area_id_ = area_id;
    }

    void set_map_id(std::string map_id){
        map_id_ = map_id;
    }

    std::string get_map_id(void){
        return map_id_;
    }

    //key_frame
    void key_frame_pose_array_callback(const keyframe_msgs::msg::KeyFramePoseArray::SharedPtr msg)
    {
        std::vector<int> frame_ids;
        std::vector<std::array<double, 3>> positions;
        std::vector<std::array<double, 4>> orientations;

        frame_ids.reserve(msg->poses.size());
        positions.reserve(msg->poses.size());
        orientations.reserve(msg->poses.size());

        for (const auto& pose_item : msg->poses)
        {
            // keyframe_id
            frame_ids.push_back(static_cast<int>(pose_item.keyframe_id));

            // position (x, y, z)
            positions.push_back({ 
                pose_item.pose.position.x, 
                pose_item.pose.position.y, 
                pose_item.pose.position.z 
            });

            // orientation (x, y, z, w)
            orientations.push_back({ 
                pose_item.pose.orientation.x, 
                pose_item.pose.orientation.y, 
                pose_item.pose.orientation.z, 
                pose_item.pose.orientation.w 
            });
        }

        // 这里你就可以直接用 frame_ids, positions, orientations 做其他处理
        createUploadResolvePoses(frame_ids, positions, orientations);
    }

    void key_frame_callback(const keyframe_msgs::msg::KeyFrame::SharedPtr msg)
    {
        pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZ>);
            pcl::fromROSMsg(msg->pointcloud, *cloud);
        if (cloud->empty())
        {
            std::cout << "Received empty point cloud!" << std::endl;
            return;
        }
         // 体素滤波降采样
        pcl::PointCloud<pcl::PointXYZ>::Ptr filtered(new pcl::PointCloud<pcl::PointXYZ>);
        pcl::VoxelGrid<pcl::PointXYZ> voxel;
        voxel.setInputCloud(cloud);
        float key_voxel_leaf_size_ = 0.1; 
        voxel.setLeafSize(key_voxel_leaf_size_, key_voxel_leaf_size_, key_voxel_leaf_size_);
        voxel.filter(*filtered);

        if (!fs::exists(LOCAL_MAPS_DIR))
        {
            fs::create_directories(LOCAL_MAPS_DIR);
        }

        std::string filename = std::string(LOCAL_MAPS_DIR) + "/cloud_map_KEY.pcd";
        if (pcl::io::savePCDFileBinary(filename, *cloud) == -1)
        {
            std::cerr << "Error saving PCD file: " << filename << std::endl;
            return;
        }
        std::string url = upload_file_and_get_url(filename);

        RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "key_frame_callback");
        buildUploadKeyframeMessage(msg->keyframe_id, url, msg->pose.position.x,
            msg->pose.position.y, msg->pose.position.z, msg->pose.orientation.x,
            msg->pose.orientation.y, msg->pose.orientation.z, msg->pose.orientation.w);
    }

    //遥控器信息
    void joystick_state_callback(const remotecontrol_msgs::msg::JoystickState::SharedPtr msg)
    {
        json j;
        j["type"] = "upload_remotecontrol_joystick_state";
        j["forward"] = false;
        j["sn"] = get_sn();
        j["area_id"] = get_area_id();
        j["timestamp"] = msg->timestamp;
        j["left_x"] = msg->left_x;
        j["left_y"] = msg->left_y;
        j["left_z"] = msg->left_z;
        j["right_x"] = msg->right_x;
        j["right_y"] = msg->right_y;
        j["right_z"] = msg->right_z;

        j["angular_vel"] = msg->angular_vel;
        j["linear_vel"] = msg->linear_vel;
        j["big_arm"] = msg->big_arm;
        j["small_arm"] = msg->small_arm;

        // std::cout << j.dump() << std::endl;
       
        broadcast(j.dump());
    }

    void key_state_callback(const remotecontrol_msgs::msg::KeyState::SharedPtr msg)
    {
        json j;
        j["type"] = "upload_remotecontrol_key_state";
        j["forward"] = false;
        j["sn"] = get_sn();
        j["area_id"] = get_area_id();
        j["timestamp"] = msg->timestamp;
        j["snowgear_switch"] = msg->snowgear_switch;
        j["mode_switch"] = msg->mode_switch;
        j["parking_brake"] = msg->parking_brake;
        j["work_light"] = msg->work_light;
        j["warning_light"] = msg->warning_light;
        j["gear_shift"] = msg->gear_shift;
        j["horn"] = msg->horn;
        j["turn_left_in_place"] = msg->turn_left_in_place;
        j["turn_right_in_place"] = msg->turn_right_in_place;
        j["reserved1"] = msg->reserved1;
        j["rpm_up"] = msg->rpm_up;
        j["reserved2"] = msg->reserved2;
        j["tool_left"] = msg->tool_left;
        j["tool_start_stop"] = msg->tool_start_stop;
        j["tool_right"] = msg->tool_right;
        j["reserved3"] = msg->reserved3;
        j["rpm_down"] = msg->rpm_down;
        j["reserved4"] = msg->reserved4;
        j["ignition"] = msg->ignition;
        j["emergency_stop"] = msg->emergency_stop;

        // std::cout << j.dump() << std::endl;
       
        broadcast(j.dump());
    }

    void point_record_callback(const geometry_msgs::msg::Vector3::SharedPtr msg) 
    {
        RCLCPP_INFO(
            rclcpp::get_logger(NODE_NAME), 
            "收到路径点: x=%.2f, y=%.2f, z=%.2f",
            msg->x, msg->y, msg->z
        );
        
        // 此处可添加路径点的进一步处理逻辑：
        // 1. 存储到文件（如CSV、TXT）
        // 2. 用于地图构建
        // 3. 可视化显示等
        json j;
        j["type"] = "upload_point_record";
        point_record_seq++;
        j["seq"] = point_record_seq;
        std::cout << "point_record_seq = " << point_record_seq << std::endl;
        j["forward"] = false;
        j["sn"] = get_sn();
        j["area_id"] = get_area_id();
        j["map_id"] = get_map_id();
        j["x"] = msg->x;
        j["y"] = msg->y;
        j["z"] = msg->z;
       
        broadcast(j.dump());
    }

    //底盘
    // 回调：VehicleMotion
    void motion_callback(const can_bridge::msg::VehicleMotion::SharedPtr msg)
    {
        // RCLCPP_INFO(rclcpp::get_logger(NODE_NAME),
        //     "[VehicleMotion] engine_rpm=%d, coolant_temp=%d, fuel_level=%d, "
        //     "tilt_x=%d, tilt_y=%d, left_track_speed=%d, "
        //     "right_track_speed=%d",
        //     msg->engine_rpm, msg->coolant_temp, msg->fuel_level,
        //     msg->tilt_x, msg->tilt_y, msg->left_track_speed,
        //     msg->right_track_speed);

        vehicleMotionState.engine_rpm = msg->engine_rpm;
        vehicleMotionState.coolant_temp = msg->coolant_temp;
        vehicleMotionState.fuel_level = msg->fuel_level;
        vehicleMotionState.tilt_x = msg->tilt_x;
        vehicleMotionState.tilt_y = msg->tilt_y;
        vehicleMotionState.left_track_speed = msg->left_track_speed;
        vehicleMotionState.right_track_speed = msg->right_track_speed;
    }

    // 回调：VehicleControlFlags
    void flags_callback(const can_bridge::msg::VehicleControlFlags::SharedPtr msg)
    {
        // RCLCPP_INFO(rclcpp::get_logger(NODE_NAME),
        //             "[VehicleControlFlags] remote_mode=%d, emergency_stop=%d",
        //             msg->remote_mode, msg->emergency_stop);
        vehicleControlFlags.emergency_stop = msg->emergency_stop;
        vehicleControlFlags.remote_mode = msg->remote_mode;
    }

    // 回调：VehicleDiagnostic
    void diag_callback(const can_bridge::msg::VehicleDiagnostic::SharedPtr msg)
    {
        // RCLCPP_INFO(rclcpp::get_logger(NODE_NAME),
        //     "[VehicleDiagnostic] mode_raw=%d, implement_rpm=%d, implement_pressure=%d, "
        //     "engine_dtc_low=%d, engine_dtc_mid=%d, engine_dtc_high_fmi=%d, "
        //     "engine_dtc_extra=%d, battery_voltage=%d",
        //     msg->mode_raw, msg->implement_rpm, msg->implement_pressure,
        //     msg->engine_dtc_low, msg->engine_dtc_mid, msg->engine_dtc_high_fmi,
        //     msg->engine_dtc_extra, msg->battery_voltage);

        vehicleDiagnosticStatus.mode_raw = msg->mode_raw;
        vehicleDiagnosticStatus.implement_rpm = msg->implement_rpm;
        vehicleDiagnosticStatus.implement_pressure = msg->implement_pressure;
        vehicleDiagnosticStatus.engine_dtc_low = msg->engine_dtc_low;
        vehicleDiagnosticStatus.engine_dtc_mid = msg->engine_dtc_mid;
        vehicleDiagnosticStatus.engine_dtc_high_fmi = msg->engine_dtc_high_fmi;
        vehicleDiagnosticStatus.engine_dtc_extra = msg->engine_dtc_extra;
        vehicleDiagnosticStatus.battery_voltage_raw = msg->battery_voltage;
    }

    //节点状态
    void pcd_mapping_ready_Callback(const std_msgs::msg::Bool::SharedPtr msg)
    {
        RCLCPP_INFO(node_->get_logger(), "pcd_mapping_ready_Callback: %d", msg->data);
        createUploadMappingPointcloudStatus(true);
    }

    void localization_ready_Callback(const std_msgs::msg::Bool::SharedPtr msg)
    {
        RCLCPP_INFO(node_->get_logger(), "localization_ready_Callback: %d", msg->data);
        createLocationNodeStatus(true);
    }

    void topogrid_ready_Callback(const std_msgs::msg::Bool::SharedPtr msg)
    {
        RCLCPP_INFO(node_->get_logger(), "topogrid_ready_Callback: %d", msg->data);
        createUploadMappingGridmapStatus(true);
    }

    void navigation_ready_Callback(const std_msgs::msg::Bool::SharedPtr msg)
    {
        RCLCPP_INFO(node_->get_logger(), "navigation_ready_Callback: %d", msg->data);
        createNavigationNodeStatus(true);
    }

    void set_node(rclcpp::Node::SharedPtr n) {
        node_ = n;
        goal_loc_pub = node_->create_publisher<geometry_msgs::msg::PoseStamped>("/web_loc_goal_pose", 10);
        goal_pub = node_->create_publisher<geometry_msgs::msg::PoseStamped>("/web_goal_pose", 10);
        pointcloud_sub = node_->create_subscription<sensor_msgs::msg::PointCloud2>(
            // "/registered_scan",
            "/cloud_registered",
            10,
            std::bind(&WebSocketServerSingleton::pointCloudCallback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1)
        );
        pointcloud_scan_sub = node_->create_subscription<sensor_msgs::msg::PointCloud2>(
             "/registered_scan",
            10,
            std::bind(&WebSocketServerSingleton::pointCloudCallback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1)
        );
        // cmd_vel_pub = node_->create_publisher<geometry_msgs::msg::Twist>("/cmd_vel", 10);
		mode_pub = node_->create_publisher<std_msgs::msg::String>("/robot_mode", 10);
        max_speed_pub = node_->create_publisher<std_msgs::msg::Float32>("/set_max_speed", 10);
        // 初始化重规划话题发布器
        replan_pub = node_->create_publisher<std_msgs::msg::Int8>("/replan", 10);
        
        // 初始化启动导航话题发布器
        start_nav_pub = node_->create_publisher<std_msgs::msg::Int8>("/start_navigation", 10);
        
        // 初始化终止导航话题发布器
        stop_nav_pub = node_->create_publisher<std_msgs::msg::Int8>("/stop_navigation", 10);
            
        odom_sub = node_->create_subscription<nav_msgs::msg::Odometry>(
            "/state_estimation",
            1000,
            std::bind(&WebSocketServerSingleton::odomCallback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1)
        );
        sub_relocal_ = node_->create_subscription<std_msgs::msg::Bool>(
            "/Relocal_flag", 10,
            std::bind(&WebSocketServerSingleton::RelocalCallback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1));
        sub_local_ = node_->create_subscription<std_msgs::msg::Bool>(
            "/Local_flag", 10,
            std::bind(&WebSocketServerSingleton::LocalCallback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1));
        sub_gps_add = node_->create_subscription<std_msgs::msg::Bool>(
            "/GPS_add_flag", 10,
            std::bind(&WebSocketServerSingleton::GpsAddCallback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1));
        //到达目标点
        sub_arrive_goal = node_->create_subscription<std_msgs::msg::Int8>(
            "/navigation_success", 1,
            std::bind(&WebSocketServerSingleton::ArriveCallback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1));
        
        //节点启动状态
        sub_pcd_mapping_node_ready_ = node_->create_subscription<std_msgs::msg::Bool>(
            "/pcd_mapping_node_ready",
            10,
            std::bind(&WebSocketServerSingleton::pcd_mapping_ready_Callback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1)
        );

        sub_localization_node_ready_ = node_->create_subscription<std_msgs::msg::Bool>(
            "/localization_node_ready",
            10,
            std::bind(&WebSocketServerSingleton::localization_ready_Callback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1)
        );

        sub_topogrid_node_ready_ = node_->create_subscription<std_msgs::msg::Bool>(
            "/topogrid_node_ready",
            10,
            std::bind(&WebSocketServerSingleton::topogrid_ready_Callback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1)
        );

        sub_navigation_node_ready_ = node_->create_subscription<std_msgs::msg::Bool>(
            "/navigation_node_ready",
            10,
            std::bind(&WebSocketServerSingleton::navigation_ready_Callback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1)
        );

        //局部规划路径
        path_sub = node_->create_subscription<nav_msgs::msg::Path>(
            "/path",
            10,
            std::bind(&WebSocketServerSingleton::pathCallback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1));

        // 底盘创建订阅者
        motion_sub = node_->create_subscription<can_bridge::msg::VehicleMotion>(
            "/vehicle_motion", 10, std::bind(&WebSocketServerSingleton::motion_callback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1));

        flags_sub = node_->create_subscription<can_bridge::msg::VehicleControlFlags>(
            "/vehicle_control_flags", 10, std::bind(&WebSocketServerSingleton::flags_callback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1));

        diag_sub = node_->create_subscription<can_bridge::msg::VehicleDiagnostic>(
            "/vehicle_diagnostic", 10, std::bind(&WebSocketServerSingleton::diag_callback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1));

       //key_frames
        key_frame_pose_array_sub = node_->create_subscription<keyframe_msgs::msg::KeyFramePoseArray>(
            "/key_frame_pose_array", 10, std::bind(&WebSocketServerSingleton::key_frame_pose_array_callback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1));

        key_frame_sub = node_->create_subscription<keyframe_msgs::msg::KeyFrame>(
        "/key_frame", 10, std::bind(&WebSocketServerSingleton::key_frame_callback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1));

         //所有导航任务完成
        task_completed_sub = node_->create_subscription<std_msgs::msg::Int8>(
            "/stopAtTarget", 10, std::bind(&WebSocketServerSingleton::task_completed_callback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1));   

        // gps_subscriber_ = node_->create_subscription<sensor_msgs::msg::NavSatFix>(
        //     "/chattergps",  // 话题名称，需与发布器保持一致
        //     10,          // 消息队列长度
        //     std::bind(&WebSocketServerSingleton::gps_callback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1));
        gps_subscriber_ = node_->create_subscription<sensor_msgs::msg::NavSatFix>(
            "/chattergps",  // 话题名称（绝对路径，确保与发布端一致）
            10,             // 消息队列长度
            std::bind(&WebSocketServerSingleton::chattergps_callback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1)  // 绑定回调函数
        );
#if XG_CHANXUECHE
        //偏移提醒
        offset_remind_sub = node_->create_subscription<global_traj_generate::msg::LateralDeviation>(
            "/vehicle/lateral_deviation", 10, std::bind(&WebSocketServerSingleton::offset_remind_callback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1));
        //避障提醒
        avoidance_remind_sub = node_->create_subscription<std_msgs::msg::Int8>(
            "/stop", 10, std::bind(&WebSocketServerSingleton::avoidance_remind_callback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1));   
       
        //遥控器消息订阅
        joystick_state_sub = node_->create_subscription<remotecontrol_msgs::msg::JoystickState>(
        "/remotecontrol/joystick_state", 10, std::bind(&WebSocketServerSingleton::joystick_state_callback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1));

        key_state_sub = node_->create_subscription<remotecontrol_msgs::msg::KeyState>(
        "/remotecontrol/key_state", 10, std::bind(&WebSocketServerSingleton::key_state_callback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1));

        point_record_sub = node_->create_subscription<geometry_msgs::msg::Vector3>(
        "/point_record", 1000, std::bind(&WebSocketServerSingleton::point_record_callback, &WebSocketServerSingleton::getInstance(), std::placeholders::_1));

#endif
    }

    rclcpp::Node::SharedPtr get_node() const {
        return node_;
    }

//#pragma region  //建图
    //实时点云上传
    void upload_realtime_point_cloud(std::string url){
        json j;
        j["type"] = "upload_realtime_point_cloud";
        j["forward"] = false;
        j["url"] = url;
        j["sn"] = get_sn();
        j["area_id"] = get_area_id();
        broadcast(j.dump());
    }

    // 关键帧点云上传
    void buildUploadKeyframeMessage(
        int frame_id,
        const std::string& url,
        double pos_x, double pos_y, double pos_z,
        double ori_x, double ori_y, double ori_z, double ori_w
    ) {
        json j;
        j["type"] = "upload_keyframe";
        j["forward"] = true;
        j["frame_id"] = frame_id;
        j["url"] = url;
        j["sn"] = get_sn();
        j["area_id"] = get_area_id();

        j["pose"] = {
            {"position", {
                {"x", pos_x},
                {"y", pos_y},
                {"z", pos_z}
            }},
            {"orientation", {
                {"x", ori_x},
                {"y", ori_y},
                {"z", ori_z},
                {"w", ori_w}
            }}
        };

        broadcast(j.dump());
    }

    //关键帧位姿更新
    void createUploadResolvePoses(
        const std::vector<int>& frame_ids,
        const std::vector<std::array<double, 3>>& positions,
        const std::vector<std::array<double, 4>>& orientations
    ) {
        json payload;
        payload["type"] = "upload_resolve_poses";
        payload["forward"] = true;
        payload["sn"] = get_sn();
        payload["area_id"] = get_area_id();

        json key_poses = json::array();

        for (size_t i = 0; i < frame_ids.size(); ++i) {
            json pose;
            pose["frame_id"] = frame_ids[i];
            pose["position"] = {
                {"x", positions[i][0]},
                {"y", positions[i][1]},
                {"z", positions[i][2]}
            };
            pose["orientation"] = {
                {"x", orientations[i][0]},
                {"y", orientations[i][1]},
                {"z", orientations[i][2]},
                {"w", orientations[i][3]}
            };
            key_poses.push_back(pose);
        }

        payload["key_poses"] = key_poses;
        broadcast(payload.dump());
    }

    //设备端建图完成地图上传
    void createUploadMaps(
        const std::string& url_pcd,
        const std::string& url_txt,
        const std::string& url_pgm,
        const std::string& url_yaml,
        const std::string& url_path
    ) {
        json msg;
        msg["type"] = "upload_maps_up";
        msg["forward"] = true;
        msg["url_pcd"] = url_pcd;
        msg["url_txt"] = url_txt;
        msg["url_pgm"] = url_pgm;
        msg["url_yaml"] = url_yaml;
        if(!url_path.empty()){
            msg["url_path"] = url_path;
        }
        msg["sn"] = get_sn();
        msg["map_id"] = get_map_id();
        msg["area_id"] = get_area_id();
        broadcast(msg.dump());
    }

    //设备上报点云构图启动状态
    void createUploadMappingPointcloudStatus(
        bool status
    ) {
        json msg;
        msg["type"] = "upload_mapping_pointcloud_status";
        msg["forward"] = false;
        msg["status"] = status;
        msg["sn"] = get_sn();
        msg["area_id"] = get_area_id();
        broadcast(msg.dump());
    }

    //设备上报栅格构图启动状态
    void createUploadMappingGridmapStatus(
        bool status
    ) {
        json msg;
        msg["type"] = "upload_mapping_gridmap_status";
        msg["forward"] = false;
        msg["status"] = status;
        msg["sn"] = get_sn();
        msg["area_id"] = get_area_id();
        broadcast(msg.dump());
    }

//#pragma endregion

//#pragma region  //定位
    //重定位状态上报
    void createRelocalizeStatus(
        bool status
    ) {
        json msg;
        msg["type"] = "relocalize_status";
        msg["forward"] = false;
        msg["status"] = status;
        msg["map_id"] = get_map_id();
        msg["sn"] = get_sn();
        msg["area_id"] = get_area_id();
        broadcast(msg.dump());
    }

    //定位状态上行报文
    void createLocalizeStatus(
        bool status
    ) {
        json msg;
        msg["type"] = "localize_status";
        msg["forward"] = false;
        msg["status"] = status;
        msg["map_id"] = get_map_id();
        msg["sn"] = get_sn();
        msg["area_id"] = get_area_id();
        broadcast(msg.dump());
    }

    //是否融合RTK
    void createRTKFusionStatus(
        bool status
    ) {
        json msg;
        msg["type"] = "rtk_fusion_status";
        msg["forward"] = false;
        msg["status"] = status;
        msg["map_id"] = get_map_id();
        msg["sn"] = get_sn();
        msg["area_id"] = get_area_id();
        broadcast(msg.dump());
    }

//#pragma endregion

//#pragma region //导航
    //到达目标点上报
    void createUploadGoalReached(void) {
        json msg;
        msg["type"] = "goal_reached";
        msg["forward"] = false;
        msg["map_id"] = get_map_id();
        msg["sn"] = get_sn();
        msg["area_id"] = get_area_id();
        broadcast(msg.dump());
    }

    //路径上报
    nav_msgs::msg::Path simplifyPath(const std::vector<geometry_msgs::msg::PoseStamped> &original_path, int max_points) {
        nav_msgs::msg::Path simplified_path;
        int total_points = static_cast<int>(original_path.size());

        if (total_points <= max_points || max_points <= 0) {
            simplified_path.poses = original_path;
            return simplified_path;
        }

        int step = total_points / max_points;
        for (int i = 0; i < total_points; i += step) {
            simplified_path.poses.push_back(original_path[i]);
        }

        // Ensure the last point is always included
        if (simplified_path.poses.empty() || simplified_path.poses.back().pose != original_path.back().pose) {
            simplified_path.poses.push_back(original_path.back());
        }

        return simplified_path;
    }

    void createUploadPath(const nav_msgs::msg::Path &path_msg) {
        json path_json;

        // 可选：你需要在 ROS2 中实现 simplifyPath 函数或略过这步
        const auto &poses = path_msg.poses;
        nav_msgs::msg::Path simplified_path = simplifyPath(poses, 10);
        // const auto &simplified_path = poses; // 简化处理

        path_json["type"] = "upload_path";
        path_json["forward"] = false;
        path_json["map_id"] = get_map_id();
        path_json["sn"] = get_sn();
        path_json["area_id"] = get_area_id();
        path_json["frame_id"] = path_msg.header.frame_id;

        path_json["waypoints"] = json::array();
        for (size_t i = 0; i < simplified_path.poses.size(); ++i) {
            const auto &pose = simplified_path.poses[i];
            json waypoint;
            waypoint["seq"] = static_cast<int>(i);
            nlohmann::json pos_json;
            pos_json["x"] = pose.pose.position.x;
            pos_json["y"] = pose.pose.position.y;
            pos_json["z"] = pose.pose.position.z;
            waypoint["position"] = pos_json;

            nlohmann::json ori_json;
            ori_json["x"] = pose.pose.orientation.x;
            ori_json["y"] = pose.pose.orientation.y;
            ori_json["z"] = pose.pose.orientation.z;
            ori_json["w"] = pose.pose.orientation.w;
            waypoint["orientation"] = ori_json;
            
            path_json["waypoints"].push_back(waypoint);
        }

        broadcast(path_json.dump());
    }

    //设备上报定位节点启动状态
    void createLocationNodeStatus(
        bool status
    ) {
        json msg;
        msg["type"] = "upload_location_node_status";
        msg["forward"] = false;
        msg["status"] = status;
        msg["map_id"] = get_map_id();
        msg["sn"] = get_sn();
        msg["area_id"] = get_area_id();
        broadcast(msg.dump());
    }

    //设备上报导航节点启动状态
    void createNavigationNodeStatus(
        bool status
    ) {
        json msg;
        msg["type"] = "upload_navigation_node_status";
        msg["forward"] = false;
        msg["status"] = status;
        msg["map_id"] = get_map_id();
        msg["sn"] = get_sn();
        msg["area_id"] = get_area_id();
        broadcast(msg.dump());
    }

//#pragma endregion

//#pragma region //机器人实时位置和里程信息上报
    void createRobotStatus(double cur_travel_distance, double total_travel_distance) {
        json j;

        j["type"] = "robot_status";
        j["forward"] = false;
        j["sn"] = get_sn();
        j["area_id"] = get_area_id();
        j["map_id"] = get_map_id();
        j["cur_travel_distance"] = cur_travel_distance;
        j["total_travel_distance"] = total_travel_distance;
#if XG_CHANXUECHE
        //车辆回传信息
        j["engine_rpm"] = vehicleMotionState.engine_rpm;
        j["coolant_temp"] = vehicleMotionState.coolant_temp;
        j["fuel_level"] = vehicleMotionState.fuel_level;
        j["tilt_x"] = vehicleMotionState.tilt_x;
        j["tilt_y"] = vehicleMotionState.tilt_y;
        j["left_track_speed"] = vehicleMotionState.left_track_speed;
        j["right_track_speed"] = vehicleMotionState.right_track_speed;
        j["speed"] = (vehicleMotionState.left_track_speed + vehicleMotionState.right_track_speed)/72;
        //状态标志位
        j["remote_mode"] = vehicleControlFlags.remote_mode;
        j["emergency_stop"] = vehicleControlFlags.emergency_stop;
        //诊断信息
        j["mode_raw"] = vehicleDiagnosticStatus.mode_raw;
        j["implement_rpm"] = vehicleDiagnosticStatus.implement_rpm;
        j["implement_pressure"] = vehicleDiagnosticStatus.implement_pressure;
        j["engine_dtc_low"] = vehicleDiagnosticStatus.engine_dtc_low;
        j["engine_dtc_mid"] = vehicleDiagnosticStatus.engine_dtc_mid;
        j["engine_dtc_high_fmi"] = vehicleDiagnosticStatus.engine_dtc_high_fmi;
        j["engine_dtc_extra"] = vehicleDiagnosticStatus.engine_dtc_extra;
        j["battery_voltage_raw"] = vehicleDiagnosticStatus.battery_voltage_raw;
        //除雪具模式
        // j["cxjms"] = cxjms;  //1（空载）、2（铲雪）、3（辊雪）、4（抛雪）

        j["latitude"] = latest_gps_data_.latitude;
        j["longitude"] = latest_gps_data_.longitude;
        j["altitude"] = latest_gps_data_.altitude;
        j["fix_status"] = latest_gps_data_.status.status;
#endif
        j["linear_velocity"] = {
            {"x", odom_msg.twist.twist.linear.x},
            {"y", odom_msg.twist.twist.linear.y},
            {"z", odom_msg.twist.twist.linear.z}
        };

        j["angular_velocity"] = {
            {"x", odom_msg.twist.twist.angular.x},
            {"y", odom_msg.twist.twist.angular.y},
            {"z", odom_msg.twist.twist.angular.z}
        };

        j["position"] = {
            {"x", odom_msg.pose.pose.position.x},
            {"y", odom_msg.pose.pose.position.y},
            {"z", odom_msg.pose.pose.position.z}
        };

        json orientation;
        orientation["roll"] = roll_;
        orientation["pitch"] = pitch_;
        orientation["yaw"] = yaw_;
        j["orientation"] = orientation;

        broadcast(j.dump());
    }
//#pragma endregion

    void navTimeoutCallback(){
        if(nav_timeoutTime > 0){
            nav_timeoutTime--;
            if(nav_timeoutTime <= 0){
                if (task_running_) {
                    RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "navTimeoutCallback, processNextTarget %zu", current_index_ + 1);
                    // 停止当前任务，处理下一个目标点
                    task_running_ = false;
                    current_index_++;
                    processNextTarget();
                }
            }
        }
    }

    int add_get_heart_cnt(){
        heart_cnt++;
        return heart_cnt;
    }

    void clear_heart_cnt(){
        heart_cnt = 0;
    }

    int32_t delete_cloud_map_files(const std::string& dir_path) {
        // 检查目录是否存在且有效
        if (!fs::exists(dir_path) || !fs::is_directory(dir_path)) {
            return -1; // 目录无效返回-1
        }

        int32_t deleted_count = 0;

        try {
            // 遍历目录中的所有条目
            for (const auto& entry : fs::directory_iterator(dir_path)) {
                // 只处理常规文件，不处理目录
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().filename().string();
                    if (is_cloud_map_file(filename)) {
                        // 执行删除操作
                        if (fs::remove(entry.path())) {
                            deleted_count++;
                        }
                    }
                }
            }
        } catch (const fs::filesystem_error&) {
            return -1; // 文件系统错误返回-1
        } catch (...) {
            return -1; // 其他未知错误返回-1
        }

        return deleted_count;
    }

    void start_localization(){
        
        node_start(PKG_TYPE_LOCALIZATION);
    }

    void start_nr(){
        
        node_start(PKG_TYPE_NAVIGATION);
    }

    // 发布启动导航消息
    void publishStartNavigation(int8_t value = 1)
    {
        std_msgs::msg::Int8 msg;
        msg.data = value;
        start_nav_pub->publish(msg);
        RCLCPP_INFO(node_->get_logger(), "已发布启动导航消息: %d", msg.data);
    }


private:
    WebSocketServerSingleton(int port) : port_(port) {}
    int port_;
    WebSocketService ws_;
    WebSocketServer server_;
    std::unordered_set<WebSocketChannelPtr> clients_;
    std::mutex mutex_;
    TimerID timer_id_ = -1;
    bool timer_running_ = false;
    int path_cnt = 0;

    std::string sn_;
    std::string area_id_;
    std::string map_id_;
    WebSocketClient ws;
    std::unordered_map<std::string, MsgHandler> msgHandlers;
    rclcpp::Node::SharedPtr node_;
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr goal_loc_pub;  //重定位点
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr goal_pub;  //目标点
    // rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr cmd_vel_pub;
	rclcpp::Publisher<std_msgs::msg::String>::SharedPtr mode_pub;  //模式切换
    rclcpp::Publisher<std_msgs::msg::Float32>::SharedPtr max_speed_pub;  //模式切换
    //重规划
    rclcpp::Publisher<std_msgs::msg::Int8>::SharedPtr replan_pub;
    rclcpp::Publisher<std_msgs::msg::Int8>::SharedPtr start_nav_pub;  
    rclcpp::Publisher<std_msgs::msg::Int8>::SharedPtr stop_nav_pub;

    rclcpp::Subscription<sensor_msgs::msg::PointCloud2>::SharedPtr pointcloud_sub;
    rclcpp::Subscription<sensor_msgs::msg::PointCloud2>::SharedPtr pointcloud_scan_sub;
    rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr odom_sub;
    rclcpp::Subscription<std_msgs::msg::Bool>::SharedPtr sub_relocal_;
    rclcpp::Subscription<std_msgs::msg::Bool>::SharedPtr sub_local_;
    rclcpp::Subscription<std_msgs::msg::Bool>::SharedPtr sub_gps_add;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr sub_arrive_goal;
    rclcpp::Subscription<std_msgs::msg::Bool>::SharedPtr sub_pcd_mapping_node_ready_;
    rclcpp::Subscription<std_msgs::msg::Bool>::SharedPtr sub_localization_node_ready_;
    rclcpp::Subscription<std_msgs::msg::Bool>::SharedPtr sub_topogrid_node_ready_;
    rclcpp::Subscription<std_msgs::msg::Bool>::SharedPtr sub_navigation_node_ready_;
    rclcpp::Subscription<nav_msgs::msg::Path>::SharedPtr path_sub;
    rclcpp::Subscription<can_bridge::msg::VehicleMotion>::SharedPtr motion_sub;
    rclcpp::Subscription<can_bridge::msg::VehicleControlFlags>::SharedPtr flags_sub;
    rclcpp::Subscription<can_bridge::msg::VehicleDiagnostic>::SharedPtr diag_sub;
    rclcpp::Subscription<keyframe_msgs::msg::KeyFramePoseArray>::SharedPtr key_frame_pose_array_sub;
    rclcpp::Subscription<keyframe_msgs::msg::KeyFrame>::SharedPtr key_frame_sub;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr task_completed_sub;
    rclcpp::Subscription<sensor_msgs::msg::NavSatFix>::SharedPtr gps_subscriber_;

    rclcpp::Subscription<global_traj_generate::msg::LateralDeviation>::SharedPtr offset_remind_sub;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr avoidance_remind_sub;

    rclcpp::Subscription<remotecontrol_msgs::msg::JoystickState>::SharedPtr joystick_state_sub;
    rclcpp::Subscription<remotecontrol_msgs::msg::KeyState>::SharedPtr key_state_sub;
    rclcpp::Subscription<geometry_msgs::msg::Vector3>::SharedPtr point_record_sub;

    sensor_msgs::msg::NavSatFix latest_gps_data_;  // 最新GPS数据

    int updateBusy = 0;
    int bevFlag = 0;
    float voxel_leaf_size_ = 0.2; 
    double roll_, pitch_, yaw_;
    int heart_cnt = 0;
    int bev_count = 0;
    int cxjms = 0;

    //底盘
    //车辆回传信息
    struct VehicleMotionState {
        uint8_t engine_rpm = 0;         // 发动机转速 (*10 rpm)
        uint8_t coolant_temp = 0;       // 冷却液温度   //can偏移值-40
        uint8_t fuel_level = 0;         // 燃油油量 (百分比)
        uint8_t tilt_x = 0;             // 倾角X (±127°)     //（理解为偏移-127，假设127度，则实际显示为254？）
        uint8_t tilt_y = 0;             // 倾角Y (±127°)
        uint8_t left_track_speed = 0;   // 左履带速度 (保留km/h）
        uint8_t right_track_speed = 0;  // 右履带速度 (保留km/h)
    };

    // 0x11877760 字节 7：状态标志位
    struct VehicleControlFlags {
        bool remote_mode = false;       // bit 0: 远程(1)/本地(0)
        bool emergency_stop = false;    // bit 1: 急停按键 (0=按下，1=未按)
    };

    //0x11877761
    struct VehicleDiagnosticStatus {
        uint8_t mode_raw;             // Byte 0: 运行模式bit0  bit1~7保留，区分有人/遥控/无人（这里是三个状态，如何使用1bit解释？）
        uint8_t implement_rpm;        // Byte 1: 机具转速 (0~255 转)
        uint8_t implement_pressure;   // Byte 2: 机具压力，实际 = *2（单位 bar）  //此值*2得到实际值
        uint8_t engine_dtc_low;       // Byte 3: 故障低位         //（byte3~6另有一套dtc协议解析，实际应用对算法是否有效？是否需要做解析？）
        uint8_t engine_dtc_mid;       // Byte 4: 故障中位
        uint8_t engine_dtc_high_fmi;  // Byte 5: 故障最高位 + FMI
        uint8_t engine_dtc_extra;     // Byte 6: 故障扩展码              
        uint8_t battery_voltage_raw;  // Byte 7: 电池电压原始值（➗10 得实际电压）
    };

    VehicleMotionState vehicleMotionState;
    VehicleControlFlags vehicleControlFlags;
    VehicleDiagnosticStatus vehicleDiagnosticStatus;

    //机器人模式
    // 定义机器人模式的枚举类型
    enum RobotMode
    {
        IDLE,
        AUTO,      // 自动模式
        MANUAL,    // 手动模式
    };
    // 选择初始模式
    RobotMode current_mode = AUTO; 
    int point_seq = 0;
    int point_record_seq = 0;
    //实时点云
    void uploadPCDThread(const std::string &filename) {
        updateBusy = 1;
        // std::cout << "uploadPCDThread begin" << std::endl;
        std::string url = URL_FILE_UPLOAD + "map_id=" + get_map_id();
        // std::string url = "http://58.48.53.254:50080/showroom-api/minio/upload";
        std::string data = file_upload(url, filename);
        RCLCPP_INFO(node_->get_logger(), "data->%s", data.c_str());

        try {
            // 解析 JSON 字符串
             json j = json::parse(data);
            // 先获取data对象，再从中提取filename和url
            json data_obj = j["data"];
            std::string filename = data_obj["filename"];
            std::string url = data_obj["url"];
            
            std::cout << "filename: " << filename << std::endl;
            std::cout << "url: " << url << std::endl;

            json response;
            response["type"] = "upload_realtime_point_cloud";
            response["forward"] = false;
            response["url"] = filename;
            response["sn"] = get_sn();
            response["area_id"] = get_area_id();
            response["map_id"] = get_map_id();
            point_seq++;
            response["seq"] = point_seq;
            json point;
            point["x"] = odom_msg.pose.pose.position.x;
            point["y"] = odom_msg.pose.pose.position.y;
            point["z"] = odom_msg.pose.pose.position.z;
            response["points"] = point;
            broadcast(response.dump());
            std::cout << response.dump() << std::endl;
        }
        catch (const std::exception& e) {
            std::cerr << "解析错误: " << e.what() << std::endl;
        }
        updateBusy = 0;
        // std::cout << "uploadPCDThread end" << std::endl;
    }

    std::mutex pcdFileMutex;

    void savePCDThread(pcl::PointCloud<pcl::PointXYZ>::Ptr cloud, const std::string &filename)
    {
        std::lock_guard<std::mutex> lock(pcdFileMutex);

        if (pcl::io::savePCDFileBinary(filename, *cloud) == -1)
        {
            std::cerr << "Error saving PCD file: " << filename << std::endl;
            return;
        }

        std::cout << "PCD saved: " << filename << std::endl;

        // 保存成功后，启动上传线程
        std::thread(&WebSocketServerSingleton::uploadPCDThread, this, filename).detach();
    }

    // 检查文件名是否以"cloud_map"开头
    bool is_cloud_map_file(const std::string& filename) {
        const std::string prefix = "cloud_map";
        return filename.size() >= prefix.size() && 
            filename.substr(0, prefix.size()) == prefix;
    }

    void pointCloudCallback(const sensor_msgs::msg::PointCloud2::SharedPtr msg)
    {
        // if (bevFlag)
        {
            
            // if (rclcpp::ok())
            {
                bev_count++;
                // if ((bev_count % 60 == 0) && (ws.isConnected()))
                if (bev_count % 60 == 0)
                {
                    bev_count = 0;
                    if (updateBusy == 1) 
                    {
                        std::cout << "pointCloudCallback updateBusy return" << std::endl;
                        return;
                    }
                    updateBusy = 1;

                    RCLCPP_INFO(rclcpp::get_logger("pointcloud_subscriber"),
                    "Received point cloud: width=%u height=%u",
                    msg->width, msg->height);

                    pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZ>);
                    pcl::fromROSMsg(*msg, *cloud);

                    if (cloud->empty())
                    {
                        std::cout << "Received empty point cloud!" << std::endl;
                        return;
                    }

                    // 体素滤波降采样
                    pcl::PointCloud<pcl::PointXYZ>::Ptr filtered(new pcl::PointCloud<pcl::PointXYZ>);
                    pcl::VoxelGrid<pcl::PointXYZ> voxel;
                    voxel.setInputCloud(cloud);
                    voxel.setLeafSize(voxel_leaf_size_, voxel_leaf_size_, voxel_leaf_size_);
                    voxel.filter(*filtered);

                    if (!fs::exists(LOCAL_MAPS_DIR))
                    {
                        fs::create_directories(LOCAL_MAPS_DIR);
                    }

                    // 使用转换后的时间字符串拼接文件名
                    std::string filename = std::string(LOCAL_MAPS_DIR) + "/cloud_map_" + PCD_getCurrentTime() + ".pcd";
                    std::cout << "save cloud_pcd filename = " << filename << std::endl;
                    // 启动异步线程保存 PCD
                    std::thread(&WebSocketServerSingleton::savePCDThread, this, filtered, filename).detach();
                }
            }
        }
    }

    void quaternionToEuler(const geometry_msgs::msg::Quaternion &quat) {
        tf2::Quaternion q(
            quat.x,
            quat.y,
            quat.z,
            quat.w);
        tf2::Matrix3x3 m(q);
        m.getRPY(roll_, pitch_, yaw_);

        // RCLCPP_INFO(rclcpp::get_logger("quaternionToEuler"),
        //             "Roll: %f, Pitch: %f, Yaw: %f", roll_, pitch_, yaw_);
    }

    //odomCallback
    void odomCallback(const nav_msgs::msg::Odometry::SharedPtr msg)
    {
        // RCLCPP_INFO(node_->get_logger(), "Got odometry pose: x=%.2f y=%.2f", 
        //             msg->pose.pose.position.x, msg->pose.pose.position.y);

        geometry_msgs::msg::Quaternion quat;

        odom_msg.header.stamp = node_->now();          // ROS2 获取当前时间
        odom_msg.header.frame_id = "odom";            // 坐标系定义

        odom_msg.pose.pose.position.x = msg->pose.pose.position.x;
        odom_msg.pose.pose.position.y = msg->pose.pose.position.y;
        odom_msg.pose.pose.position.z = msg->pose.pose.position.z;

        odom_msg.pose.pose.orientation.x = msg->pose.pose.orientation.x;
        odom_msg.pose.pose.orientation.y = msg->pose.pose.orientation.y;
        odom_msg.pose.pose.orientation.z = msg->pose.pose.orientation.z;
        odom_msg.pose.pose.orientation.w = msg->pose.pose.orientation.w;

        odom_msg.twist.twist.linear.x = msg->twist.twist.linear.x;
        odom_msg.twist.twist.linear.y = msg->twist.twist.linear.y;
        odom_msg.twist.twist.linear.z = msg->twist.twist.linear.z;

        odom_msg.twist.twist.angular.x = msg->twist.twist.angular.x;
        odom_msg.twist.twist.angular.y = msg->twist.twist.angular.y;
        odom_msg.twist.twist.angular.z = msg->twist.twist.angular.z;

        quat.x = odom_msg.pose.pose.orientation.x;
        quat.y = odom_msg.pose.pose.orientation.y;
        quat.z = odom_msg.pose.pose.orientation.z;
        quat.w = odom_msg.pose.pose.orientation.w;

        quaternionToEuler(quat);
    }

    //偏移提醒  
    void offset_remind_callback(const global_traj_generate::msg::LateralDeviation::SharedPtr msg)
    {
        // RCLCPP_INFO(node_->get_logger(), "offset_remind_callback reference_path_id=%s lateral_error_m=%f heading_error_rad=%f", msg->reference_path_id.c_str(), msg->lateral_error_m, msg->heading_error_rad);

        json j;
        j["type"] = "offset_remind_up";
        j["forward"] = false;
        j["map_id"] = get_map_id(); 
        j["sn"] = get_sn(); 
        j["area_id"] = get_area_id(); 
        j["code"] = 0; 
        j["reference_path_id"] = msg->reference_path_id; 
        j["lateral_error_m"] = msg->lateral_error_m; 
        j["heading_error_rad"] = msg->heading_error_rad; 
        j["errmsg"] = "探测到线路发生偏移"; 
        // std::cout << j.dump(4) << std::endl;
        broadcast(j.dump());
    }

    //避障提醒
    void avoidance_remind_callback(const std_msgs::msg::Int8::SharedPtr msg)
    {
        // RCLCPP_INFO(node_->get_logger(), "avoidance_remind_callback接收到的布尔消息值为: %d", msg->data);

        json j;
        j["type"] = "avoidance_remind_up";
        j["forward"] = false;
        j["map_id"] = get_map_id(); 
        j["sn"] = get_sn(); 
        j["area_id"] = get_area_id(); 
        j["code"] = 0; 
        j["errmsg"] = "探测到前方有障碍物"; 
        // std::cout << j.dump(4) << std::endl;
        broadcast(j.dump());
    }

    //任务完成  
    void task_completed_callback(const std_msgs::msg::Int8::SharedPtr msg)
    {
        RCLCPP_INFO(node_->get_logger(), "task_completed_callback接收到的布尔消息值为: %d", msg->data);

        json j;
        j["type"] = "task_completed";
        j["forward"] = false;
        j["sn"] = get_sn();          
        j["map_id"] = get_map_id();      
        j["time"] = getCurrentTime();  
        j["area_id"] = get_area_id();
        std::cout << j.dump(4) << std::endl;
        broadcast(j.dump());  
    }

    void chattergps_callback(const sensor_msgs::msg::NavSatFix::SharedPtr msg)
    {
        // 打印接收到的GPS核心数据
        // RCLCPP_INFO(rclcpp::get_logger("chattergps_subscriber"), 
        //     "2222 收到GPS数据:\n"
        //     "  5纬度: %.8f\n"
        //     "  6经度: %.8f\n"
        //     "  海拔: %.2f米\n"
        //     "  状态: %d",
        //     msg->latitude, 
        //     msg->longitude, 
        //     msg->altitude,
        //     msg->status.status
        // );

        // 可在此处添加自定义处理逻辑：
        // 1. 解析经纬度用于定位
        // 2. 转发数据到其他模块（如WebSocket）
        // 3. 存储到本地文件或数据库
        latest_gps_data_ = *msg;  // 直接赋值，类型完全匹配
        // 打印接收到的GPS信息
        // RCLCPP_INFO(
        //     node_->get_logger(), 
        //     "GPS定位信息:\n"
        //     "  纬度: %.8f 度\n"
        //     "  经度: %.8f 度\n"
        //     "  海拔: %.2f 米\n"
        //     "  定位状态: %d\n"
        //     "  服务类型: %d",
        //     latest_gps_data_.latitude,    // 可以直接使用已存储的变量
        //     latest_gps_data_.longitude,
        //     latest_gps_data_.altitude,
        //     latest_gps_data_.status.status,
        //     latest_gps_data_.status.service
        // );
    }

    void RelocalCallback(const std_msgs::msg::Bool::SharedPtr msg)
    {
        RCLCPP_INFO(node_->get_logger(), "RelocalCallback接收到的布尔消息值为: %d", msg->data);

        json j;
        j["type"] = "relocalize_status";

        j["forward"] = false;
        if (msg->data) {
            std::cout << "relocal ok" << std::endl;
            j["status"] = true;
        } else {
            std::cout << "relocal fail" << std::endl;
            j["status"] = false;
        }
        j["map_id"] = get_map_id(); 
        j["sn"] = get_sn(); 
        j["area_id"] = get_area_id(); 
        std::cout << j.dump(4) << std::endl;
        broadcast(j.dump());
    }

    void LocalCallback(const std_msgs::msg::Bool::SharedPtr msg)
    {
        RCLCPP_INFO(node_->get_logger(), "LocalCallback接收到的布尔消息值为: %d", msg->data);

        json j;
        j["type"] = "localize_status";

        j["forward"] = false;
        if (msg->data) {
            std::cout << "local ok" << std::endl;
            j["status"] = true;
        } else {
            std::cout << "local fail" << std::endl;
            j["status"] = false;
        }
        j["map_id"] = get_map_id(); 
        j["sn"] = get_sn(); 
        j["area_id"] = get_area_id(); 
        std::cout << j.dump(4) << std::endl;
        broadcast(j.dump());
    }

    void GpsAddCallback(const std_msgs::msg::Bool::SharedPtr msg)
    {
        RCLCPP_INFO(node_->get_logger(), "GpsAddCallback接收到的布尔消息值为: %d", msg->data);

        json j;
        j["type"] = "rtk_fusion_status";

        j["forward"] = false;
        // if (msg->data) {
        //     std::cout << "gps add ok" << std::endl;
        //     j["status"] = true;
        // } else {
        //     std::cout << "gps add fail" << std::endl;
        //     j["status"] = false;
        // }
        std::cout << "gps add ok" << std::endl;
        j["status"] = true;
        j["map_id"] = get_map_id(); 
        j["sn"] = get_sn(); 
        j["area_id"] = get_area_id(); 
        std::cout << j.dump(4) << std::endl;
        broadcast(j.dump());
    }

    void ArriveCallback(const std_msgs::msg::Int8::SharedPtr msg)
    {
        RCLCPP_INFO(node_->get_logger(), "ArriveCallback接收到的布尔消息值为: %d", msg->data);

        json j;
        j["type"] = "goal_reached";
        j["forward"] = false;
        std::cout << "arrive ok" << std::endl;
        j["status"] = true;
        j["map_id"] = get_map_id(); 
        j["sn"] = get_sn(); 
        j["area_id"] = get_area_id(); 
        std::cout << j.dump(4) << std::endl;
        broadcast(j.dump());

        onTaskCompleted();
    }

    void pathCallback(const nav_msgs::msg::Path::SharedPtr msg)
    {
        // path_cnt++;
        // if(path_cnt%50 == 0)
        {
            path_cnt = 0;
            // createUploadPath(*msg);
            json path_json;

            // 可选：你需要在 ROS2 中实现 simplifyPath 函数或略过这步
            const auto &poses = msg->poses;
            nav_msgs::msg::Path simplified_path = simplifyPath(poses, 10);
            // const auto &simplified_path = poses; // 简化处理

            path_json["type"] = "upload_path";
            path_json["forward"] = false;
            path_json["map_id"] = get_map_id();
            path_json["sn"] = get_sn();
            path_json["area_id"] = get_area_id();
            path_json["frame_id"] = msg->header.frame_id;

            path_json["waypoints"] = json::array();
            for (size_t i = 0; i < simplified_path.poses.size(); ++i) {
                const auto &pose = simplified_path.poses[i];
                json waypoint;
                waypoint["seq"] = static_cast<int>(i);
                nlohmann::json pos_json;
                pos_json["x"] = pose.pose.position.x;
                pos_json["y"] = pose.pose.position.y;
                pos_json["z"] = pose.pose.position.z;
                waypoint["position"] = pos_json;

                nlohmann::json ori_json;
                ori_json["x"] = pose.pose.orientation.x;
                ori_json["y"] = pose.pose.orientation.y;
                ori_json["z"] = pose.pose.orientation.z;
                ori_json["w"] = pose.pose.orientation.w;
                waypoint["orientation"] = ori_json;
                
                path_json["waypoints"].push_back(waypoint);
            }
            std::cout << path_json.dump(4) << std::endl;
            broadcast(path_json.dump());
        }
    }

    struct Orientation {
        double w, x, y, z;
    };
    struct TargetPoint {
        double x, y, z;
        Orientation orientation;
        double timeout;
    };
    
    nav_msgs::msg::Odometry odom_msg;
    //ntrip
    struct NRTKConfig {
        std::string server_ip;
        std::string port;
        std::string mount_point;
        std::string username;
        std::string password;
    };
    NRTKConfig nRTKConfig;

    struct RemoteControl {
        std::string db;   //up|down
        std::string xb;   //left|right
        std::string fx;   //up|down|left|right
    };
    RemoteControl remoteControl;

    struct CameraConfig {
        std::string qzsxt; // 前置摄像头地址
        std::string hzsxt; // 后置摄像头地址
    };

    // 读取配置
    CameraConfig loadConfig(const std::string& path) {
        CameraConfig cfg;
        YAML::Node config = YAML::LoadFile(path);
        cfg.qzsxt = config["qzsxt"].as<std::string>();
        cfg.hzsxt = config["hzsxt"].as<std::string>();
        return cfg;
    }

    CameraConfig cameraConfig;

    // 保存配置
    void saveConfig(const CameraConfig& cfg, const std::string& path) {
        YAML::Node config;
        config["qzsxt"] = cfg.qzsxt;
        config["hzsxt"] = cfg.hzsxt;

        std::ofstream fout(path);
        fout << config;  // yaml-cpp 会自动格式化
    }

    //导航多目标点
    std::vector<TargetPoint> target_points_;
    bool task_running_ = false;
    size_t current_index_ = 0;
    int nav_timeoutTime = 0;
    int nav_large_val = 10;

    //包管理
    // 定义包类型枚举
    enum PkgType {
        PKG_TYPE_RTK,
        PKG_TYPE_BASE,  //底盘
        PKG_TYPE_LIDAR,
        PKG_TYPE_MAPPING,
        PKG_TYPE_GRID,
        PKG_TYPE_LOCALIZATION,
        PKG_TYPE_NAVIGATION,
        PKG_TYPE_ALL,
    };

    // 定义节点配置结构体
    struct PkgConfig {
        std::string path;
        std::string pkg_name;
        std::vector<std::string> node_name;
        std::string launch_file;
        std::string script_path;
        std::string launch_param;
        std::string script_param;
    };

    // 自定义比较函数，用于 std::map 对比 PkgType
    struct PkgTypeCompare {
        bool operator()(const PkgType lhs, const PkgType rhs) const {
            return static_cast<int>(lhs) < static_cast<int>(rhs); // 通过枚举值的整数值来比较
        }
    };

    std::map<PkgType, PkgConfig, PkgTypeCompare> pkg_configs;

    // 从 YAML 中解析包配置
    PkgConfig parsePkgConfig(const YAML::Node& node) {
        PkgConfig config;
        config.path = node["path"].as<std::string>();
        config.pkg_name = node["pkg_name"].as<std::string>();
        config.node_name = node["node_name"].as<std::vector<std::string>>();
        config.launch_file = node["launch_file"].as<std::string>();
        config.script_path = node["script_path"].as<std::string>();
        config.launch_param = node["launch_param"].as<std::string>();
        config.script_param = node["script_param"].as<std::string>();
        return config;
    }

    // 将 YAML 中的包配置添加到 map 中
    void loadConfigFromYAML(const std::string& yaml_file, 
                            std::map<PkgType, PkgConfig, PkgTypeCompare>& pkg_configs) {
        // 加载 YAML 文件
        YAML::Node config = YAML::LoadFile(yaml_file);
        // 遍历 YAML 中的节点，解析并添加到 map 中
        for (YAML::const_iterator it = config.begin(); it != config.end(); ++it) {
            std::string name = it->first.as<std::string>();
            // 判断节点的类型并映射到对应的 PkgType
            PkgType pkg_type;
            if (name == "pkg_rtk") {
                pkg_type = PKG_TYPE_RTK;
            } else if (name == "pkg_base") {
                pkg_type = PKG_TYPE_BASE;
            } else if (name == "pkg_lidar") {
                pkg_type = PKG_TYPE_LIDAR;
            } else if (name == "na_mapping") {
                pkg_type = PKG_TYPE_MAPPING;
            } else if (name == "topological_grid") {
                pkg_type = PKG_TYPE_GRID;
            } else if (name == "na_localization") {
                pkg_type = PKG_TYPE_LOCALIZATION;
            } else if (name == "NR_Navigation") {
                pkg_type = PKG_TYPE_NAVIGATION;
            } else {
                std::cerr << "Unknown name type: " << name << std::endl;
                continue;
            }
            // 解析节点配置并添加到 map 中
            PkgConfig pkg_config = parsePkgConfig(it->second);
            pkg_configs[pkg_type] = pkg_config;
        }
    }

     // 启动 ROS 包
    void startPkg(const PkgConfig& config) {
    std::string command =  "gnome-terminal -- bash -c 'source " + config.path + "/install/setup.bash; "
        "ros2 launch " + config.pkg_name + " " + config.launch_file + " " + config.launch_param + "; exec bash'" ;
        std::cout << "command " << command << std::endl;
        std::system(command.c_str());  // 执行启动命令
    }

     // 执行脚本
    void executeScript(const PkgConfig& config) {
        if (!config.script_path.empty()) {
            std::string command = "chmod 777 " + config.script_path + " " + config.script_param;
            int ret = std::system(command.c_str());  // 执行脚本
            if (ret == 0) {
                std::cout << "Command " << command << " executed successfully.\n";
            } else {
                // std::cout << "Command failed with return code: " << ret << "\n";
                std::cout << "Command " << command << " failed with return code.\n";
            }
            command = "bash -c " + config.script_path + " " + config.script_param;
            ret = std::system(command.c_str());  // 执行脚本
            if (ret == 0) {
                std::cout << "Command " << command << " executed successfully.\n";
            } else {
                // std::cout << "Command failed with return code: " << ret << "\n";
                std::cout << "Command " << command << " failed with return code.\n";
            }
        }
    }

    void stopNode(const std::string& node_name) {
        // std::string command = "rosnode kill " + node_name;  pkill -f
        std::string command = "pkill -f " + node_name; 
        std::system(command.c_str());  // 执行停止命令
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }

    bool isNodeRunning(const std::string& node_name) {
        std::stringstream command;
        command << "ros2 node list | grep " << node_name << " > /dev/null 2>&1";
        return system(command.str().c_str()) == 0;
    }

    void node_start(const PkgType& type) {
        try {
            const auto& pkg_config = pkg_configs.at(type);
            for (const auto& name : pkg_config.node_name) {
                if (!isNodeRunning(name)) {
                    startPkg(pkg_config);
                    break; // 启动一次就够
                }
            }
            std::this_thread::sleep_for(std::chrono::seconds(1));
        } catch (const std::exception& e) {
            std::cerr << "Error starting node: " << e.what() << std::endl;
        }
    }

    void node_stop(const PkgType& type) {
        try {
            const auto& pkg_config = pkg_configs.at(type);
            for (const auto& name : pkg_config.node_name) {
                if (!name.empty()) {
                    if (isNodeRunning(name)) {
                        stopNode(name);
                    } else {
                        std::cout << "Node " << name << " is not running." << std::endl;
                    }
                }
            }
        } catch (const std::exception& e) {
            std::cerr << "Error stopping node: " << e.what() << std::endl;
        }
    }

    void printPkgConfig() {
        for (const auto& [pkg_type, config] : pkg_configs) {
            std::cout << "PkgType: " << static_cast<int>(pkg_type) << std::endl;

            std::cout << "Node Names: ";
            for (const auto& node : config.node_name) {
                std::cout << node << " ";
            }
            std::cout << std::endl;

            std::cout << "Package Name: " << config.pkg_name << std::endl;
            std::cout << "Launch File: " << config.launch_file << std::endl;
            std::cout << "Script Path: " << config.script_path << std::endl;
            std::cout << "Launch Param: " << config.launch_param << std::endl;
            std::cout << "Script Param: " << config.script_param << std::endl;
            std::cout << "----------------------------------------" << std::endl;
        }
    }

    // 模拟任务完成
    void onTaskCompleted()
    {
        if (task_running_) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "Target %zu completed successfully.", current_index_ + 1);

            if (target_points_[current_index_].timeout == 0) {
                target_points_[current_index_].timeout = 1.0;
            }

            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "timeout time val = %f", target_points_[current_index_].timeout);
            nav_timeoutTime = static_cast<int>(target_points_[current_index_].timeout) * nav_large_val;
        }
    }

    // 发送导航命令
    void broadcastNavigationCommand(const TargetPoint& target)
    {
        RCLCPP_INFO(rclcpp::get_logger(NODE_NAME),
            "broadcasting navigation command to: [x=%.2f, y=%.2f, z=%.2f, timeout=%.2f, qx=%.2f, qy=%.2f, qz=%.2f, qw=%.2f]",
            target.x, target.y, target.z, target.timeout,
            target.orientation.x, target.orientation.y, target.orientation.z, target.orientation.w);

        geometry_msgs::msg::PoseStamped goal;
        goal.header.frame_id = "map";
        goal.header.stamp = node_->now();

        goal.pose.position.x = target.x;
        goal.pose.position.y = target.y;
        goal.pose.position.z = target.z;

        goal.pose.orientation.x = target.orientation.x;
        goal.pose.orientation.y = target.orientation.y;
        goal.pose.orientation.z = target.orientation.z;
        goal.pose.orientation.w = target.orientation.w;

        RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "Publishing goal pose...");
        goal_pub->publish(goal);
    }

    // ==== 处理下一个目标点 ====
    void processNextTarget() {
        if (current_index_ >= target_points_.size()) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "All target points processed.");
            // 非循环任务完成
            json msg;
            msg["type"] = "task_completed";
            msg["forward"] = false;
            msg["sn"] = get_sn();          
            msg["map_id"] = get_map_id();      
            msg["time"] = getCurrentTime();  
            msg["area_id"] = get_area_id();

            broadcast(msg.dump());  
            return;
        }

        if (target_points_.empty()) {
            json msg;
            msg["type"] = "task_completed";
            msg["forward"] = false;
            msg["sn"] = get_sn();  
            msg["map_id"] = get_map_id();  
            msg["time"] = getCurrentTime();              
            msg["area_id"] = get_area_id();

            broadcast(msg.dump());
            return;
        }
        const auto& target = target_points_[current_index_];
        RCLCPP_INFO(rclcpp::get_logger(NODE_NAME),
            "Processing Target %zu: [x=%.2f, y=%.2f, z=%.2f, timeout=%.2f, qx=%.2f, qy=%.2f, qz=%.2f, qw=%.2f]",
            current_index_ + 1, target.x, target.y, target.z, target.timeout,
            target.orientation.x, target.orientation.y, target.orientation.z, target.orientation.w);
        task_running_ = true;
        broadcastNavigationCommand(target);
    }

    void startNavigation()
    {
        if (!target_points_.empty()) {
            processNextTarget();
        }
    }

    // 动态添加目标点
    void addTargetPoint(const TargetPoint &point) {
        target_points_.push_back(point);
        RCLCPP_INFO(rclcpp::get_logger(NODE_NAME),"Added new target point: [x=%.2f, y=%.2f, z=%.2f, qx=%.2f, qy=%.2f, qz=%.2f, w=%.2f timeout=%.2f]", point.x, point.y, point.z, point.orientation.x, point.orientation.y, point.orientation.z, point.orientation.w, point.timeout);
    }

    // 保存目标点到 JSON 文件
    bool saveJsonTargetPoint(const std::string& file_path) {
        nlohmann::json j;
        j["target_points"] = nlohmann::json::array();
        for (const auto& point : target_points_) {
            j["target_points"].push_back({
                {"x", point.x},
                {"y", point.y},
                {"z", point.z},
                {"orientation", {
                    {"w", point.orientation.w},
                    {"x", point.orientation.x},
                    {"y", point.orientation.y},
                    {"z", point.orientation.z}
                }},
                {"timeout", point.timeout}
            });
        }
        std::ofstream file(file_path);
        if (!file.is_open()) {
            std::cout << "Failed to open JSON file: " << file_path << std::endl;
            return false;
        }
        file << j.dump(4);  // 格式化输出，缩进4个空格
        file.close();
        std::cout << "Saved navigation task to JSON file: " << file_path << std::endl;
        return true;
    }

    // 加载 JSON 文件
    bool loadJsonTargetPoint(const std::string& file_path) {
        std::ifstream file(file_path);
        if (!file.is_open()) {
            std::cout << "Unable to open JSON file: " << file_path << std::endl;
            return false;
        }
        nlohmann::json j;
        file >> j;

        target_points_.clear();

        for (const auto& target : j["target_points"]) {
            TargetPoint point;
            point.x = target["x"];
            point.y = target["y"];
            point.z = target["z"];
            point.orientation.w = target["orientation"]["w"];
            point.orientation.x = target["orientation"]["x"];
            point.orientation.y = target["orientation"]["y"];
            point.orientation.z = target["orientation"]["z"];
            point.timeout = target["timeout"];
            target_points_.push_back(point);
            std::cout << "x: " << point.x << ", y: " << point.y << ", z: " << point.z << ", timeout: " << point.timeout << std::endl;
            std::cout << "orientation: w=" << point.orientation.w << ", x=" << point.orientation.x << ", y=" << point.orientation.y << ", z=" << point.orientation.z << std::endl;
        }
        return true;
    }

    void loadTargetPointFromJson()
    {
        if (!g_navigation_filename.empty()) {
            // 如果提供了 JSON 文件，尝试加载目标点
            if (!loadJsonTargetPoint(g_navigation_filename)) {
                std::cout << "Failed to load navigation task from JSON file. Starting with an empty target list." << std::endl;
            }
        } else {
            // 如果未提供文件路径，提示动态添加目标点
            std::cout << "No JSON file provided. Please add target points dynamically." << std::endl;
        }
    }

    void handleSlamCommand(const std::string& msg, const std::string& expectedType) {
        RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "%s", msg.c_str());
        json response;

        try {
            json j = json::parse(msg);

            //TODO 建图
            if(expectedType == "slam_start_down"){
                std::cout << "Start Bev " << std::endl; 
               

                bevFlag = true;
                node_stop(PKG_TYPE_LOCALIZATION);
                node_stop(PKG_TYPE_NAVIGATION);
                // node_stop(PKG_TYPE_BASE);
                node_start(PKG_TYPE_MAPPING);
                node_start(PKG_TYPE_GRID);

                response["type"] = "slam_start_up";
                 // 提取字段
                response["sn"] = get_sn();
                response["area_id"] = get_area_id();
                response["code"] = 0;
                response["forward"] = false;
#if XG_CHANXUECHE
                if (j.contains("map_id")) {
                    set_map_id(j["map_id"]);
                }
                response["map_id"] = get_map_id();
#endif
                broadcast(response.dump());
            }else{
               
                std::cout << "Stop Bev " << std::endl; 
                // PkgConfig pkg_config_grid = pkg_configs[PKG_TYPE_MAPPING];
                // executeScript(pkg_config_grid);
                // std::this_thread::sleep_for(std::chrono::seconds(5));
                node_stop(PKG_TYPE_MAPPING);
                node_stop(PKG_TYPE_GRID);

                node_start(PKG_TYPE_LOCALIZATION);
                node_start(PKG_TYPE_NAVIGATION);

// #if XG_CHANXUECHE

// #else
//                 int int_map_id = std::stoi(get_map_id());
//                 int_map_id += 1;
//                 set_map_id(std::to_string(int_map_id));
// #endif
                
//                 std::string local_maps_path = LOCAL_MAPS_DIR + "/" + get_sn() + "/" + get_map_id();
//                 if (!fs::exists(local_maps_path))
//                 {
//                     fs::create_directories(local_maps_path);
//                 }else{
//                     if (fs::exists(local_maps_path) && fs::is_directory(local_maps_path)) {
//                         for (const auto& entry : fs::directory_iterator(local_maps_path)) {
//                             fs::remove_all(entry.path()); // 删除文件或文件夹
//                         }
//                         std::cout << "目录已清空: " << local_maps_path << std::endl;
//                     } else {
//                         std::cout << "目录不存在: " << local_maps_path << std::endl;
//                     }
//                 }
//                 MapManager::getInstance().copyPgmFile(ALG_PGM_DIR, local_maps_path);
//                 MapManager::getInstance().copyPcdFile(ALG_PCD_MAPPING_DIR, local_maps_path);
//                 MapManager::getInstance().copyPointRecordFile(ALG_PCD_MAPPING_DIR, local_maps_path);
//                 MapManager::getInstance().copyPcdFile(ALG_PCD_LOCALIZATION_DIR, local_maps_path);
//                 MapManager::getInstance().setCurrentMap(get_sn(), get_map_id());
//                 node_start(PKG_TYPE_LOCALIZATION);
//                 node_start(PKG_TYPE_NAVIGATION);

//                  // 依次处理需要上传的文件
//                 std::string url_pcd = upload_file_and_get_url(local_maps_path + "/cloud_map.pcd");
//                 std::string url_txt = upload_file_and_get_url(local_maps_path + "/pose.txt");
//                 std::string url_pgm = upload_file_and_get_url(local_maps_path + "/map.pgm");
//                 std::string url_yaml = upload_file_and_get_url(local_maps_path + "/map.yaml");
//                 std::string url_path = "";
//                 // point_record.txt 需要先判断是否存在
//                 fs::path path_file = local_maps_path + "/point_record.txt";
//                 if (fs::exists(path_file) && fs::is_regular_file(path_file)) {
//                     url_path = upload_file_and_get_url(path_file.string());
//                 }
//                 createUploadMaps(url_pcd, url_txt, url_pgm, url_yaml, url_path);

                response["type"] = "slam_stop_up";
                // 提取字段
                response["sn"] = get_sn();
                response["area_id"] = get_area_id();
                response["code"] = 0;
                response["forward"] = false;

                broadcast(response.dump());
            }
        } catch (const std::exception& e) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
        }
    }

    void publish_pose(const geometry_msgs::msg::PoseStamped& pose) {
        if (goal_loc_pub) {
            goal_loc_pub->publish(pose);
        } else {
            RCLCPP_WARN(node_->get_logger(), "Publisher not initialized!");
        }
    }

    // void publish_cmd_vel(const geometry_msgs::msg::Twist& vel) {
    //     if (cmd_vel_pub) {
    //         cmd_vel_pub->publish(vel);
    //     } else {
    //         RCLCPP_WARN(node_->get_logger(), "Publisher not initialized!");
    //     }
    // }
	
	 void publish_goal(const geometry_msgs::msg::PoseStamped& pose) {
        if (goal_pub) {
            goal_pub->publish(pose);
        } else {
            RCLCPP_WARN(node_->get_logger(), "Publisher not initialized!");
        }
    }

    void publishReplan(int8_t value = 1)
    {
        std_msgs::msg::Int8 msg;
        msg.data = value;
        replan_pub->publish(msg);
        RCLCPP_INFO(node_->get_logger(), "已发布重规划消息: %d", msg.data);
    }

    // // 发布启动导航消息
    // void publishStartNavigation(int8_t value = 1)
    // {
    //     std_msgs::msg::Int8 msg;
    //     msg.data = value;
    //     start_nav_pub->publish(msg);
    //     RCLCPP_INFO(node_->get_logger(), "已发布启动导航消息: %d", msg.data);
    // }

    // 发布终止导航消息
    void publishStopNavigation(int8_t value = 0)
    {
        std_msgs::msg::Int8 msg;
        msg.data = value;
        stop_nav_pub->publish(msg);
        RCLCPP_INFO(node_->get_logger(), "已发布终止导航消息: %d", msg.data);
    }

    void handleNavigationCommand(const std::string& msg, const std::string& expectedType) {
        RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "%s", msg.c_str());
        json response;

        try {
            json j = json::parse(msg);
            //TODO 导航启动/停止
            if(expectedType == "navigation_start_down"){
                response["type"] = "navigation_start_up";
                // node_start(PKG_TYPE_BASE);
                // target_points_.clear();
                // current_index_ = 0;
                // loadTargetPointFromJson();
                // startNavigation();
                publishStartNavigation(1);
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "publishStartNavigation");
            }else{
                response["type"] = "navigation_stop_up";
                // target_points_.clear();
                // task_running_ = false;
                // current_index_ = 0;
                publishStopNavigation(1);
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "publishStopNavigation");
            }
            // 提取字段
            response["sn"] = get_sn();
            response["area_id"] = get_area_id();
            response["code"] = 0;
            response["forward"] = false;

            broadcast(response.dump());
        } catch (const std::exception& e) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
        }
    }

    // 上传文件并返回 URL，如果失败返回空字符串
    std::string upload_file_and_get_url(const std::string& local_path) {
        try {
            std::string url = URL_FILE_UPLOAD + "map_id=" + get_map_id();
            std::string data = file_upload(url, local_path);
            RCLCPP_INFO(node_->get_logger(), "data->%s", data.c_str());

            json j = json::parse(data);
            // 先获取data对象，再从中提取filename和url
            json data_obj = j["data"];
            std::string filename = data_obj["filename"];
            std::string url_dest = data_obj["url"];
            
            std::cout << "filename: " << filename << std::endl;
            std::cout << "url: " << url_dest << std::endl;
            return url_dest;
        } catch (const std::exception& e) {
            std::cerr << "解析错误: " << e.what() << std::endl;
        }
        return "";
    }

    std::string getCurrentTime() {
        auto now = std::chrono::system_clock::now();
        auto in_time_t = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&in_time_t), "%Y-%m-%d %H:%M:%S");
        return ss.str();
    }

    std::string PCD_getCurrentTime() {
        auto now = std::chrono::system_clock::now();
        auto in_time_t = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&in_time_t), "%Y_%m_%d_%H_%M_%S");
        return ss.str();
    }

    void initMessageHandlers() {

//#pragma region  //cmd_vel
        // msgHandlers["cmd_vel"] = [this](const std::string& msg) {
        //     RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "%s", msg.c_str());
        //     json response;

        //     try {
        //         json j = json::parse(msg);

        //         // 提取字段
        //         response["type"] = j.value("type", "");
        //         response["forward"] = false;
        //         response["sn"] = get_sn();
        //         response["area_id"] = get_area_id();
        //         response["code"] = 0;

        //         broadcast(response.dump());

        //         // 转换成 ROS2 的 Twist 消息
        //         geometry_msgs::msg::Twist twist_msg;
        //         twist_msg.linear.x  = j["linear"]["x"].get<double>();
        //         twist_msg.linear.y  = j["linear"]["y"].get<double>();
        //         twist_msg.linear.z  = j["linear"]["z"].get<double>();
        //         twist_msg.angular.x = j["angular"]["x"].get<double>();
        //         twist_msg.angular.y = j["angular"]["y"].get<double>();
        //         twist_msg.angular.z = j["angular"]["z"].get<double>();

        //         // 输出检查
        //         std::cout << "Type: " << j["type"] << "\n";
        //         std::cout << "Linear: " << twist_msg.linear.x << ", "
        //                 << twist_msg.linear.y << ", " << twist_msg.linear.z << "\n";
        //         std::cout << "Angular: " << twist_msg.angular.x << ", "
        //                 << twist_msg.angular.y << ", " << twist_msg.angular.z << "\n";

        //         publish_cmd_vel(twist_msg);
                
        //     } catch (const std::exception& e) {
        //         RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
        //     }
        // };

//#pragma endregion

#if XG_CHANXUECHE
//#pragma region //logic
        msgHandlers["remote_control_down"] = [this](const std::string& msg) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "%s", msg.c_str());
            json response;

            try {
                json j = json::parse(msg);

                // 提取字段
                response["type"] = "remote_control_up";
                response["forward"] = false;
                response["sn"] = get_sn();
                response["area_id"] = get_area_id();
                response["code"] = 0;
                response["time"] = getCurrentTime();
                response["db"] = remoteControl.db;
                response["xb"] = remoteControl.xb;
                response["fx"] = remoteControl.fx;
                broadcast(response.dump());
            } catch (const std::exception& e) {
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
            }
        };

        msgHandlers["rtl_instruction_info_down"] = [this](const std::string& msg) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "%s", msg.c_str());
            json response;

            try {
                json j = json::parse(msg);

                // 提取字段
                response["type"] = "crtl_instruction_info_up";
                response["forward"] = false;
                response["sn"] = get_sn();
                response["area_id"] = get_area_id();
                response["code"] = 0;
                response["time"] = getCurrentTime();
                response["qzsxt"] = cameraConfig.qzsxt;
                response["hzsxt"] = cameraConfig.hzsxt;
                broadcast(response.dump());
            } catch (const std::exception& e) {
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
            }
        };

        msgHandlers["line_edit_save_down"] = [this](const std::string& msg) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "%s", msg.c_str());
            json response;

            try {
                json j = json::parse(msg);

                // 提取字段
                response["type"] = "line_edit_save_up";
                response["forward"] = false;
                response["sn"] = get_sn();
                response["area_id"] = get_area_id();
                response["code"] = 0;
                response["time"] = getCurrentTime();
                if (j.contains("map_id")) {
                    set_map_id(j["map_id"]);
                }
                response["map_id"] = get_map_id();
                if (j.contains("url_path")) {
                    std::string url_path = j["url_path"].get<std::string>();
                    RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "url_path: %s", url_path.c_str());
                    std::string local_maps_path = LOCAL_MAPS_DIR + "/" + get_sn() + "/" + get_map_id();
                    std::string output_file = local_maps_path + "/point_record.txt";
                    if (fs::exists(output_file) && fs::is_regular_file(output_file)) {
                        try {
                            fs::remove(output_file);
                            std::cout << "文件已删除: " << output_file << std::endl;
                        } catch (const fs::filesystem_error& e) {
                            std::cerr << "删除失败: " << e.what() << std::endl;
                        }
                    } 
                    file_download(url_path, output_file);
                    MapManager::getInstance().copyPointRecordFile(local_maps_path, ALG_PCD_MAPPING_DIR);
                }
                broadcast(response.dump());
            } catch (const std::exception& e) {
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
            }
        };

        msgHandlers["vehicle_start_task_down"] = [this](const std::string& msg) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "%s", msg.c_str());
            json response;

            try {
                json j = json::parse(msg);

                // 提取字段
                response["type"] = "vehicle_start_task_up";
                response["forward"] = false;
                response["sn"] = get_sn();
                response["area_id"] = get_area_id();
                response["code"] = 0;
                response["time"] = getCurrentTime();
                if (j.contains("map_id")) {
                    set_map_id(j["map_id"]);
                }
                response["map_id"] = get_map_id();

                if (j.contains("map_name")) {
                    std::cout << "map_name " << j["map_name"] << std::endl;
                }
                response["errmsg"] = "车辆启动成功";
                broadcast(response.dump());
                publishStartNavigation(1);
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "vehicle_start_task_down");
            } catch (const std::exception& e) {
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
            }
        };

        msgHandlers["vehicle_stop_task_down"] = [this](const std::string& msg) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "%s", msg.c_str());
            json response;

            try {
                json j = json::parse(msg);

                // 提取字段
                response["type"] = "vehicle_stop_task_up";
                response["forward"] = false;
                response["sn"] = get_sn();
                response["area_id"] = get_area_id();
                response["code"] = 0;
                response["time"] = getCurrentTime();
                if (j.contains("map_id")) {
                    set_map_id(j["map_id"]);
                }
                response["map_id"] = get_map_id();
                response["errmsg"] = "车辆作业急停成功";
                broadcast(response.dump());
                publishStopNavigation(1);
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "vehicle_stop_task_down");
            } catch (const std::exception& e) {
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
            }
        };

//#pragma endregion
#endif

//#pragma region  //建图
        msgHandlers["slam_start_down"] = [this](const std::string& msg) {
            handleSlamCommand(msg, "slam_start_down");
        };

        msgHandlers["slam_stop_down"] = [this](const std::string& msg) {
            handleSlamCommand(msg, "slam_stop_down");
        };

        msgHandlers["upload_maps_down"] = [this](const std::string& msg) {
            try{
                 json j = json::parse(msg);
                std::cout << "upload_maps_down Bev " << std::endl; 
                PkgConfig pkg_config_grid = pkg_configs[PKG_TYPE_MAPPING];
                executeScript(pkg_config_grid);
                std::this_thread::sleep_for(std::chrono::seconds(5));
                node_stop(PKG_TYPE_MAPPING);
                node_stop(PKG_TYPE_GRID);
    #if XG_CHANXUECHE

    #else
                int int_map_id = std::stoi(get_map_id());
                int_map_id += 1;
                set_map_id(std::to_string(int_map_id));
    #endif
                
                std::string local_maps_path = LOCAL_MAPS_DIR + "/" + get_sn() + "/" + get_map_id();
                if (!fs::exists(local_maps_path))
                {
                    fs::create_directories(local_maps_path);
                }else{
                    if (fs::exists(local_maps_path) && fs::is_directory(local_maps_path)) {
                        for (const auto& entry : fs::directory_iterator(local_maps_path)) {
                            fs::remove_all(entry.path()); // 删除文件或文件夹
                        }
                        std::cout << "目录已清空: " << local_maps_path << std::endl;
                    } else {
                        std::cout << "目录不存在: " << local_maps_path << std::endl;
                    }
                }
                MapManager::getInstance().copyPgmFile(ALG_PGM_DIR, local_maps_path);
                MapManager::getInstance().copyPcdFile(ALG_PCD_MAPPING_DIR, local_maps_path);
                MapManager::getInstance().copyPointRecordFile(ALG_PCD_MAPPING_DIR, local_maps_path);
                MapManager::getInstance().copyPcdFile(ALG_PCD_LOCALIZATION_DIR, local_maps_path);
                MapManager::getInstance().setCurrentMap(get_sn(), get_map_id());
                node_start(PKG_TYPE_LOCALIZATION);
                node_start(PKG_TYPE_NAVIGATION);

                    // 依次处理需要上传的文件
                std::string url_pcd = upload_file_and_get_url(local_maps_path + "/cloud_map.pcd");
                std::string url_txt = upload_file_and_get_url(local_maps_path + "/pose.txt");
                std::string url_pgm = upload_file_and_get_url(local_maps_path + "/map.pgm");
                std::string url_yaml = upload_file_and_get_url(local_maps_path + "/map.yaml");
                std::string url_path = "";
                // point_record.txt 需要先判断是否存在
                fs::path path_file = local_maps_path + "/point_record.txt";
                if (fs::exists(path_file) && fs::is_regular_file(path_file)) {
                    url_path = upload_file_and_get_url(path_file.string());
                }
                createUploadMaps(url_pcd, url_txt, url_pgm, url_yaml, url_path);
            }catch (const std::exception& e) {
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
            }
        };

        msgHandlers["download_fusion_map_down"] = [this](const std::string& msg) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "%s", msg.c_str());
            json response;

            try {
                json j = json::parse(msg);

                // 提取字段
                response["type"] = "download_fusion_map_up";
                response["forward"] = false;
                response["sn"] = get_sn();
                response["area_id"] = get_area_id();
                response["code"] = 0;
                broadcast(response.dump());

                node_stop(PKG_TYPE_MAPPING);
                node_stop(PKG_TYPE_GRID);
                node_stop(PKG_TYPE_LOCALIZATION);
                node_stop(PKG_TYPE_NAVIGATION);
                // node_stop(PKG_TYPE_BASE);
                std::string local_maps_path = LOCAL_MAPS_DIR + "/" + get_sn() + "/" + get_map_id();
                if (j.contains("url_pcd")) {
                    //TODO 下载url_pcd
                    std::string url_pcd = j["url_pcd"].get<std::string>();
                    RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "url_pcd: %s", url_pcd.c_str());
                    std::string output_file = local_maps_path + "/cloud_map.pcd";
                    file_download(url_pcd, output_file);
                }
                if (j.contains("url_txt")) {
                    //TODO 下载url_txt
                    std::string url_txt = j["url_txt"].get<std::string>();
                    RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "url_txt: %s", url_txt.c_str());
                    std::string output_file = local_maps_path + "/pose.txt";
                    file_download(url_txt, output_file);
                }
                if (j.contains("url_pgm")) {
                    //TODO 下载url_pgm
                    std::string url_pgm = j["url_pgm"].get<std::string>();
                    RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "url_pgm: %s", url_pgm.c_str());
                    std::string output_file = local_maps_path + "/map.pgm";
                    file_download(url_pgm, output_file);
                }
                if (j.contains("url_yaml")) {
                    //TODO 下载url_yaml
                    std::string url_yaml = j["url_yaml"].get<std::string>();
                    RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "url_yaml: %s", url_yaml.c_str());
                    std::string output_file = local_maps_path + "/map.yaml";
                    file_download(url_yaml, output_file);
                }
                if (j.contains("url_path")) {
                    //TODO 下载url_path
                    std::string url_path = j["url_path"].get<std::string>();
                    RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "url_path: %s", url_path.c_str());
                    std::string output_file = local_maps_path + "/point_record.txt";
                    file_download(url_path, output_file);
                }
                MapManager::getInstance().setCurrentMap(get_sn(), get_map_id());
                MapManager::getInstance().copyPgmFile(local_maps_path, ALG_PGM_DIR);
                MapManager::getInstance().copyPcdFile(local_maps_path, ALG_PCD_MAPPING_DIR);
                MapManager::getInstance().copyPointRecordFile(local_maps_path, ALG_PCD_MAPPING_DIR);
                MapManager::getInstance().copyPcdFile(local_maps_path, ALG_PCD_LOCALIZATION_DIR);

                node_start(PKG_TYPE_LOCALIZATION);
                node_start(PKG_TYPE_NAVIGATION);
                
            } catch (const std::exception& e) {
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
            }
        };

//#pragma endregion

//#pragma region  //定位
        msgHandlers["relocalize_pose_down"] = [this](const std::string& msg) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "%s", msg.c_str());
            try {
                json j = json::parse(msg);

                json response;
                response["type"] = "relocalize_pose_up";
                response["forward"] = false;
                response["sn"] = get_sn();
                response["map_id"] = get_map_id();
                response["area_id"] = get_area_id();
                response["code"] = 0;

                broadcast(response.dump());

                if (j.contains("pose")){
                    auto pose = j["pose"];

                    double px = pose["position"]["x"];
                    double py = pose["position"]["y"];
                    double pz = pose["position"]["z"];

                    double ox = pose["orientation"]["x"];
                    double oy = pose["orientation"]["y"];
                    double oz = pose["orientation"]["z"];
                    double ow = pose["orientation"]["w"];

                    RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "Pose Position: [%.3f, %.3f, %.3f]", px, py, pz);
                    RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "Pose Orientation: [%.3f, %.3f, %.3f, %.3f]", ox, oy, oz, ow);

                    geometry_msgs::msg::PoseStamped goal;
                    goal.header.frame_id = "map";
                    goal.header.stamp = node_->get_clock()->now();  

                    goal.pose.position.x = px;
                    goal.pose.position.y = py;
                    goal.pose.position.z = pz;
                    goal.pose.orientation.x = ox;
                    goal.pose.orientation.y = oy;
                    goal.pose.orientation.z = oz;
                    goal.pose.orientation.w = ow;

                    publish_pose(goal);
                }
                } catch (const std::exception& e) {
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
            }
        };

//#pragma endregion

//#pragma region //导航
    msgHandlers["multi_goal_down"] = [this](const std::string& msg) {
        RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "%s", msg.c_str());
        try {
            json j = json::parse(msg);

            json response;
            response["type"] = "multi_goal_up";
            response["forward"] = false;
            response["sn"] = get_sn();
            response["map_id"] = get_map_id();
            response["area_id"] = get_area_id();
            response["code"] = 0;

            broadcast(response.dump());

            target_points_.clear();
            if (j.contains("target_points") && j["target_points"].is_array()) {
                for (const auto& point : j["target_points"]) {
                    TargetPoint tp;
                    if (point.contains("x") && point["x"].is_number())
                        tp.x = point["x"].get<double>();
                    if (point.contains("y") && point["y"].is_number())
                        tp.y = point["y"].get<double>();
                    if (point.contains("z") && point["z"].is_number())
                        tp.z = point["z"].get<double>();
                    if (point.contains("timeout") && point["timeout"].is_number()){
                        tp.timeout = point["timeout"].get<double>();
                    }else{
                        tp.timeout = 0;
                    }                     
                    if (point.contains("orientation") && point["orientation"].is_object()) {
                        const auto& ori = point["orientation"];
                        if (ori.contains("w") && ori["w"].is_number())
                            tp.orientation.w = ori["w"].get<double>();
                        if (ori.contains("x") && ori["x"].is_number())
                            tp.orientation.x = ori["x"].get<double>();
                        if (ori.contains("y") && ori["y"].is_number())
                            tp.orientation.y = ori["y"].get<double>();
                        if (ori.contains("z") && ori["z"].is_number())
                            tp.orientation.z = ori["z"].get<double>();
                    }

                    target_points_.push_back(tp);
                    RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), 
                        "Added new target point: [x=%.2f, y=%.2f, z=%.2f, qx=%.2f, qy=%.2f, qz=%.2f, w=%.2f timeout=%.2f]",
                        tp.x, tp.y, tp.z,
                        tp.orientation.x, tp.orientation.y, tp.orientation.z, tp.orientation.w,
                        tp.timeout);
                }
                saveJsonTargetPoint(g_navigation_filename);
            }
        } catch (const std::exception& e) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
        }
    };

    msgHandlers["navigation_start_down"] = [this](const std::string& msg) {
        handleNavigationCommand(msg, "navigation_start_down");
    };

    msgHandlers["navigation_stop_down"] = [this](const std::string& msg) {
        handleNavigationCommand(msg, "navigation_stop_down");
    };

//#pragma endregion

//#pragma region //地图管理
    msgHandlers["set_current_map_down"] = [this](const std::string& msg) {
        RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "%s", msg.c_str());
        json response;

        try {
            json j = json::parse(msg);

            // 提取字段
            response["type"] = "set_current_map_up";
            response["forward"] = false;
            response["sn"] = get_sn();
            response["area_id"] = get_area_id();
            response["code"] = 0;

#if XG_CHANXUECHE
            if (j.contains("map_id")) {
                set_map_id(j["map_id"]);
            }
            response["map_id"] = get_map_id();
#else
            int int_map_id = std::stoi(get_map_id());
            int_map_id += 1;
            set_map_id(std::to_string(int_map_id));
#endif

            broadcast(response.dump());

            // node_stop(PKG_TYPE_MAPPING);
            // node_stop(PKG_TYPE_GRID);
            node_stop(PKG_TYPE_LOCALIZATION);
            node_stop(PKG_TYPE_NAVIGATION);
            // node_stop(PKG_TYPE_BASE);

            std::string local_maps_path = LOCAL_MAPS_DIR + "/" + get_sn() + "/" + get_map_id();
            if (!fs::exists(local_maps_path))
            {
                fs::create_directories(local_maps_path);
            }else{
                if (fs::exists(local_maps_path) && fs::is_directory(local_maps_path)) {
                    for (const auto& entry : fs::directory_iterator(local_maps_path)) {
                        fs::remove_all(entry.path()); // 删除文件或文件夹
                    }
                    std::cout << "目录已清空: " << local_maps_path << std::endl;
                } else {
                    std::cout << "目录不存在: " << local_maps_path << std::endl;
                }
            }

            if (j.contains("url_pcd")) {
                //TODO 下载url_pcd
                std::string url_pcd = j["url_pcd"].get<std::string>();
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "url_pcd: %s", url_pcd.c_str());
                std::string output_file = local_maps_path + "/cloud_map.pcd";
                file_download(url_pcd, output_file);
            }
            if (j.contains("url_txt")) {
                //TODO 下载url_txt
                std::string url_txt = j["url_txt"].get<std::string>();
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "url_txt: %s", url_txt.c_str());
                std::string output_file = local_maps_path + "/pose.txt";
                file_download(url_txt, output_file);
            }
            if (j.contains("url_pgm")) {
                //TODO 下载url_pgm
                std::string url_pgm = j["url_pgm"].get<std::string>();
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "url_pgm: %s", url_pgm.c_str());
                std::string output_file = local_maps_path + "/map.pgm";
                file_download(url_pgm, output_file);
            }
            if (j.contains("url_yaml")) {
                //TODO 下载url_yaml
                std::string url_yaml = j["url_yaml"].get<std::string>();
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "url_yaml: %s", url_yaml.c_str());
                std::string output_file = local_maps_path + "/map.yaml";
                file_download(url_yaml, output_file);
            }
            if (j.contains("url_path")) {
                //TODO 下载url_path
                std::string url_path = j["url_path"].get<std::string>();
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "url_path: %s", url_path.c_str());
                std::string output_file = local_maps_path + "/point_record.txt";
                file_download(url_path, output_file);
            }
            MapManager::getInstance().setCurrentMap(get_sn(), get_map_id());
            MapManager::getInstance().copyPgmFile(local_maps_path, ALG_PGM_DIR);
            MapManager::getInstance().copyPcdFile(local_maps_path, ALG_PCD_MAPPING_DIR);
            MapManager::getInstance().copyPointRecordFile(local_maps_path, ALG_PCD_MAPPING_DIR);
            MapManager::getInstance().copyPcdFile(local_maps_path, ALG_PCD_LOCALIZATION_DIR);

            node_start(PKG_TYPE_LOCALIZATION);
            node_start(PKG_TYPE_NAVIGATION);
            
        } catch (const std::exception& e) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
        }
    };

    msgHandlers["get_current_map_down"] = [this](const std::string& msg) {
        RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "%s", msg.c_str());
        json response;

        try {
            json j = json::parse(msg);

            // 基本信息
            response["type"]     = "get_current_map_up";
            response["forward"]  = false;
            response["sn"]       = get_sn();
            response["area_id"]  = get_area_id();

            // 基础目录
            std::string local_maps_path = LOCAL_MAPS_DIR + "/" + get_sn() + "/" + get_map_id();

            // 依次处理需要上传的文件
            response["url_pcd"]  = upload_file_and_get_url(local_maps_path + "/cloud_map.pcd");
            response["url_txt"]  = upload_file_and_get_url(local_maps_path + "/pose.txt");
            response["url_pgm"]  = upload_file_and_get_url(local_maps_path + "/map.pgm");
            response["url_yaml"] = upload_file_and_get_url(local_maps_path + "/map.yaml");

            // point_record.txt 需要先判断是否存在
            fs::path path_file = local_maps_path + "/point_record.txt";
            if (fs::exists(path_file) && fs::is_regular_file(path_file)) {
                response["url_path"] = upload_file_and_get_url(path_file.string());
            }
            // 发送结果
            broadcast(response.dump());
        } catch (const std::exception& e) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
        }
    };

    msgHandlers["edit_current_map_down"] = [this](const std::string& msg) {
        RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "%s", msg.c_str());
        json response;

        try {
            json j = json::parse(msg);

            // 提取字段
            response["type"] = "edit_current_map_up";
            response["forward"] = false;
            response["sn"] = get_sn();
            response["area_id"] = get_area_id();
            response["code"] = 0;

            broadcast(response.dump());

            if (j.contains("url_pgm")) {
                //TODO 下载url_pgm 更新
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "url_pgm: %s", j["url_pgm"].get<std::string>().c_str());
                std::string url_pgm = j["url_pgm"].get<std::string>();
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "url_pgm: %s", url_pgm.c_str());
                std::string local_maps_path = LOCAL_MAPS_DIR + "/" + get_sn() + "/" + get_map_id();
                std::string output_file = local_maps_path + "/map.pgm";
                fs::path filePath = local_maps_path + "/map.pgm";
                if (fs::exists(filePath) && fs::is_regular_file(filePath)) {
                    try {
                        fs::remove(filePath);
                        std::cout << "文件已删除: " << filePath << std::endl;
                    } catch (const fs::filesystem_error& e) {
                        std::cerr << "删除失败: " << e.what() << std::endl;
                    }
                } 
                file_download(url_pgm, output_file);
                // node_stop(PKG_TYPE_NAVIGATION);
                MapManager::getInstance().copyPgmFile(local_maps_path, ALG_PGM_DIR);
                // node_start(PKG_TYPE_NAVIGATION);
            }
        } catch (const std::exception& e) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
        }
    };

//#pragma endregion

//#pragma region //业务接口
    //NRTK账号设置接口
    msgHandlers["set_nrtk_down"] = [this](const std::string& msg) {
        RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "%s", msg.c_str());
        json response;

        try {
            json j = json::parse(msg);

            // 提取字段
            response["type"] = "set_nrtk_up";
            response["forward"] = false;
            response["sn"] = get_sn();
            response["area_id"] = get_area_id();
            response["code"] = 0;

            broadcast(response.dump());

            if (j.contains("server_ip")) {
                nRTKConfig.server_ip = j["server_ip"].get<std::string>();
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "server_ip: %s", nRTKConfig.server_ip.c_str());
                
            }
            if (j.contains("port")) {
                nRTKConfig.port = j["port"].get<std::string>();
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "port: %s", nRTKConfig.port.c_str());
            }
            if (j.contains("mount_point")) {
                nRTKConfig.mount_point = j["mount_point"].get<std::string>();
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "mount_point: %s", nRTKConfig.mount_point.c_str());
            }
            if (j.contains("username")) {
                nRTKConfig.username = j["username"].get<std::string>();
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "username: %s", nRTKConfig.username.c_str());
            }
            if (j.contains("password")) {
                nRTKConfig.password = j["password"].get<std::string>();
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "password: %s", nRTKConfig.password.c_str());
            }

            //publish  发布话题

        } catch (const std::exception& e) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
        }
    };

    //NRTK账号查询接口
    msgHandlers["get_nrtk_down"] = [this](const std::string& msg) {
        RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "%s", msg.c_str());
        json response;

        try {
            json j = json::parse(msg);

            // 提取字段
            response["type"] = "get_nrtk_up";
            response["forward"] = false;
            response["sn"] = get_sn();
            response["area_id"] = get_area_id();

            response["server_ip"] = nRTKConfig.server_ip;
            response["port"] = nRTKConfig.port;
            response["mount_point"] = nRTKConfig.mount_point;
            response["username"] = nRTKConfig.username;
            response["password"] = nRTKConfig.password;

            broadcast(response.dump());

        } catch (const std::exception& e) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
        }
    };

    //设置最大导航速度
    msgHandlers["set_max_speed_down"] = [this](const std::string& msg) {
        RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "%s", msg.c_str());
        json response;

        try {
            json j = json::parse(msg);

            // 提取字段
            response["type"] = "set_max_speed_up";
            response["forward"] = false;
            response["sn"] = get_sn();
            response["area_id"] = get_area_id();
            response["code"] = 0;

            broadcast(response.dump());

            if (j.contains("server_ip")) {
                nRTKConfig.server_ip = j["server_ip"].get<std::string>();
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "server_ip: %s", nRTKConfig.server_ip.c_str());
                
            }
            if (j.contains("max_speed")) {
                double max_speed = j["max_speed"].get<double>();
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "max_speed: %f", max_speed);
            }

        } catch (const std::exception& e) {
            RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "JSON parse error: %s", e.what());
        }
    };

//#pragma endregion

    }
};

#endif

