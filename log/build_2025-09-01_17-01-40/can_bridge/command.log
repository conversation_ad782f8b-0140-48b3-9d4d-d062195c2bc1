Invoking command in '/home/<USER>/xugong_web_ws/build/can_bridge': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/can_bridge -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/can_bridge
Invoked command in '/home/<USER>/xugong_web_ws/build/can_bridge' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/can_bridge -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/can_bridge
Invoking command in '/home/<USER>/xugong_web_ws/build/can_bridge': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/can_bridge -- -j16 -l16
Invoked command in '/home/<USER>/xugong_web_ws/build/can_bridge' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/can_bridge -- -j16 -l16
Invoking command in '/home/<USER>/xugong_web_ws/build/can_bridge': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/can_bridge
Invoked command in '/home/<USER>/xugong_web_ws/build/can_bridge' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/can_bridge
