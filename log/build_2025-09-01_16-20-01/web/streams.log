[0.019s] Invoking command in '/home/<USER>/xugong_web_ws/build/web': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/web -- -j16 -l16
[0.080s] [35m[1mConsolidate compiler generated dependencies of target web_node[0m
[0.119s] [100%] Built target web_node
[0.128s] Invoked command in '/home/<USER>/xugong_web_ws/build/web' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/web -- -j16 -l16
[0.130s] Invoking command in '/home/<USER>/xugong_web_ws/build/web': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/web
[0.137s] -- Install configuration: ""
[0.138s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/web/lib/web/web_node
[0.138s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/launch
[0.138s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/launch/web_launch.py
[0.139s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/package_run_dependencies/web
[0.139s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/parent_prefix_path/web
[0.139s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/environment/ament_prefix_path.sh
[0.139s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/environment/ament_prefix_path.dsv
[0.139s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/environment/path.sh
[0.139s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/environment/path.dsv
[0.139s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.bash
[0.139s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.sh
[0.139s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.zsh
[0.139s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.dsv
[0.139s] -- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/package.dsv
[0.139s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/packages/web
[0.139s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/cmake/webConfig.cmake
[0.139s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/cmake/webConfig-version.cmake
[0.139s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/package.xml
[0.140s] Invoked command in '/home/<USER>/xugong_web_ws/build/web' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/web
