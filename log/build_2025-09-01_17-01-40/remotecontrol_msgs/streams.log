[0.011s] Invoking command in '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/remotecontrol_msgs -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs
[0.104s] -- The C compiler identification is GNU 11.4.0
[0.170s] -- The CXX compiler identification is GNU 11.4.0
[0.178s] -- Detecting C compiler ABI info
[0.240s] -- Detecting C compiler ABI info - done
[0.245s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.245s] -- Detecting C compile features
[0.246s] -- Detecting C compile features - done
[0.248s] -- Detecting CXX compiler ABI info
[0.311s] -- Detecting CXX compiler ABI info - done
[0.317s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.317s] -- Detecting CXX compile features
[0.317s] -- Detecting CXX compile features - done
[0.320s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.433s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.510s] -- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)
[0.535s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.538s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.544s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.552s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.563s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.580s] -- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[0.589s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.600s] -- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
[0.858s] -- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
[1.195s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.327s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.348s] -- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") 
[1.349s] -- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
[1.368s] -- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") 
[1.368s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[1.368s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
[1.368s] -- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so
[1.385s] -- Found PythonExtra: .so  
[1.437s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.455s] -- Added test 'lint_cmake' to check CMake code style
[1.456s] -- Added test 'xmllint' to check XML markup files
[1.457s] -- Configuring done
[1.480s] -- Generating done
[1.488s] -- Build files have been written to: /home/<USER>/xugong_web_ws/build/remotecontrol_msgs
[1.498s] Invoked command in '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/remotecontrol_msgs -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs
[1.499s] Invoking command in '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/remotecontrol_msgs -- -j16 -l16
[1.533s] [  2%] [34m[1mGenerating C code for ROS interfaces[0m
[1.539s] [  5%] [34m[1mGenerating C++ code for ROS interfaces[0m
[1.551s] [  5%] Built target ament_cmake_python_copy_remotecontrol_msgs
[1.875s] running egg_info
[1.878s] creating remotecontrol_msgs.egg-info
[1.878s] writing remotecontrol_msgs.egg-info/PKG-INFO
[1.878s] writing dependency_links to remotecontrol_msgs.egg-info/dependency_links.txt
[1.878s] writing top-level names to remotecontrol_msgs.egg-info/top_level.txt
[1.878s] writing manifest file 'remotecontrol_msgs.egg-info/SOURCES.txt'
[1.880s] reading manifest file 'remotecontrol_msgs.egg-info/SOURCES.txt'
[1.881s] writing manifest file 'remotecontrol_msgs.egg-info/SOURCES.txt'
[1.950s] [  5%] Built target ament_cmake_python_build_remotecontrol_msgs_egg
[2.106s] [  7%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_generator_c.dir/rosidl_generator_c/remotecontrol_msgs/msg/detail/key_state__functions.c.o[0m
[2.109s] [ 10%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_generator_c.dir/rosidl_generator_c/remotecontrol_msgs/msg/detail/joystick_state__functions.c.o[0m
[2.156s] [ 10%] Built target remotecontrol_msgs__cpp
[2.174s] [ 15%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[2.174s] [ 15%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[2.175s] [ 17%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[2.196s] [ 20%] [32m[1mLinking C shared library libremotecontrol_msgs__rosidl_generator_c.so[0m
[2.246s] [ 20%] Built target remotecontrol_msgs__rosidl_generator_c
[2.259s] [ 23%] [34m[1mGenerating C introspection for ROS interfaces[0m
[2.267s] [ 25%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
[2.268s] [ 28%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[2.967s] [ 30%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/remotecontrol_msgs/msg/detail/key_state__type_support.cpp.o[0m
[2.968s] [ 33%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/remotecontrol_msgs/msg/detail/joystick_state__type_support.cpp.o[0m
[3.163s] [ 35%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/remotecontrol_msgs/msg/joystick_state__type_support.cpp.o[0m
[3.168s] [ 41%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/remotecontrol_msgs/msg/detail/joystick_state__type_support_c.cpp.o[0m
[3.169s] [ 41%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/remotecontrol_msgs/msg/detail/key_state__type_support_c.cpp.o[0m
[3.170s] [ 43%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/remotecontrol_msgs/msg/key_state__type_support.cpp.o[0m
[3.226s] [ 46%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/remotecontrol_msgs/msg/joystick_state__type_support.cpp.o[0m
[3.229s] [ 51%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/remotecontrol_msgs/msg/key_state__type_support.cpp.o[0m
[3.229s] [ 48%] [32m[1mLinking CXX shared library libremotecontrol_msgs__rosidl_typesupport_c.so[0m
[3.231s] [ 53%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/remotecontrol_msgs/msg/detail/dds_fastrtps/joystick_state__type_support.cpp.o[0m
[3.240s] [ 56%] [32mBuilding CXX object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/remotecontrol_msgs/msg/detail/dds_fastrtps/key_state__type_support.cpp.o[0m
[3.296s] [ 56%] Built target remotecontrol_msgs__rosidl_typesupport_c
[3.505s] [ 58%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/remotecontrol_msgs/msg/detail/key_state__type_support.c.o[0m
[3.507s] [ 61%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/remotecontrol_msgs/msg/detail/joystick_state__type_support.c.o[0m
[3.540s] [ 64%] [32m[1mLinking C shared library libremotecontrol_msgs__rosidl_typesupport_introspection_c.so[0m
[3.572s] [ 64%] Built target remotecontrol_msgs__rosidl_typesupport_introspection_c
[3.593s] [ 66%] [32m[1mLinking CXX shared library libremotecontrol_msgs__rosidl_typesupport_cpp.so[0m
[3.623s] [ 66%] Built target remotecontrol_msgs__rosidl_typesupport_cpp
[3.687s] [ 69%] [32m[1mLinking CXX shared library libremotecontrol_msgs__rosidl_typesupport_fastrtps_cpp.so[0m
[3.696s] [ 71%] [32m[1mLinking CXX shared library libremotecontrol_msgs__rosidl_typesupport_introspection_cpp.so[0m
[3.721s] [ 74%] [32m[1mLinking CXX shared library libremotecontrol_msgs__rosidl_typesupport_fastrtps_c.so[0m
[3.745s] [ 74%] Built target remotecontrol_msgs__rosidl_typesupport_introspection_cpp
[3.769s] [ 74%] Built target remotecontrol_msgs__rosidl_typesupport_fastrtps_cpp
[3.808s] [ 74%] Built target remotecontrol_msgs__rosidl_typesupport_fastrtps_c
[3.825s] [ 74%] Built target remotecontrol_msgs
[3.840s] [ 76%] [34m[1mGenerating Python code for ROS interfaces[0m
[5.335s] [ 76%] Built target remotecontrol_msgs__py
[5.352s] [ 79%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_generator_py.dir/rosidl_generator_py/remotecontrol_msgs/msg/_key_state_s.c.o[0m
[5.353s] [ 82%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_generator_py.dir/rosidl_generator_py/remotecontrol_msgs/msg/_joystick_state_s.c.o[0m
[5.432s] [ 84%] [32m[1mLinking C shared library rosidl_generator_py/remotecontrol_msgs/libremotecontrol_msgs__rosidl_generator_py.so[0m
[5.467s] [ 84%] Built target remotecontrol_msgs__rosidl_generator_py
[5.483s] [ 87%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_fastrtps_c__pyext.dir/rosidl_generator_py/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_fastrtps_c.c.o[0m
[5.485s] [ 89%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_c__pyext.dir/rosidl_generator_py/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_c.c.o[0m
[5.486s] [ 92%] [32mBuilding C object CMakeFiles/remotecontrol_msgs__rosidl_typesupport_introspection_c__pyext.dir/rosidl_generator_py/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_introspection_c.c.o[0m
[5.551s] [ 94%] [32m[1mLinking C shared library rosidl_generator_py/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so[0m
[5.554s] [ 97%] [32m[1mLinking C shared library rosidl_generator_py/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so[0m
[5.557s] [100%] [32m[1mLinking C shared library rosidl_generator_py/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so[0m
[5.582s] [100%] Built target remotecontrol_msgs__rosidl_typesupport_c__pyext
[5.586s] [100%] Built target remotecontrol_msgs__rosidl_typesupport_fastrtps_c__pyext
[5.589s] [100%] Built target remotecontrol_msgs__rosidl_typesupport_introspection_c__pyext
[5.599s] Invoked command in '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/remotecontrol_msgs -- -j16 -l16
[5.601s] Invoking command in '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/remotecontrol_msgs
[5.606s] -- Install configuration: ""
[5.606s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/ament_index/resource_index/rosidl_interfaces/remotecontrol_msgs
[5.606s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs
[5.607s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg
[5.607s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/joystick_state.h
[5.607s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/key_state.h
[5.607s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_generator_c__visibility_control.h
[5.607s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail
[5.607s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__struct.h
[5.607s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__type_support.h
[5.607s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__functions.h
[5.607s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__functions.c
[5.607s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__struct.h
[5.607s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__functions.h
[5.607s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__type_support.h
[5.607s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__functions.c
[5.607s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/library_path.sh
[5.607s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/library_path.dsv
[5.607s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_generator_c.so
[5.607s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_generator_c.so" to ""
[5.607s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs
[5.608s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg
[5.608s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
[5.608s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail
[5.608s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__rosidl_typesupport_fastrtps_c.h
[5.608s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__rosidl_typesupport_fastrtps_c.h
[5.608s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_fastrtps_c.so
[5.608s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_fastrtps_c.so" to ""
[5.608s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs
[5.608s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg
[5.608s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/joystick_state.hpp
[5.608s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/key_state.hpp
[5.608s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_generator_cpp__visibility_control.hpp
[5.609s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail
[5.609s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__struct.hpp
[5.609s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__type_support.hpp
[5.609s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__builder.hpp
[5.609s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__traits.hpp
[5.609s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__struct.hpp
[5.609s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__traits.hpp
[5.609s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__builder.hpp
[5.609s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__type_support.hpp
[5.609s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs
[5.610s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg
[5.610s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
[5.610s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail
[5.610s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/dds_fastrtps
[5.610s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__rosidl_typesupport_fastrtps_cpp.hpp
[5.610s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__rosidl_typesupport_fastrtps_cpp.hpp
[5.610s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_fastrtps_cpp.so
[5.610s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_fastrtps_cpp.so" to ""
[5.610s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs
[5.611s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg
[5.611s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h
[5.611s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail
[5.611s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__type_support.c
[5.611s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__type_support.c
[5.611s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__rosidl_typesupport_introspection_c.h
[5.611s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__rosidl_typesupport_introspection_c.h
[5.611s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_introspection_c.so
[5.611s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_introspection_c.so" to ""
[5.611s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_c.so
[5.612s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_c.so" to ""
[5.612s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs
[5.612s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg
[5.612s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__type_support.cpp
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__type_support.cpp
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__rosidl_typesupport_introspection_cpp.hpp
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__rosidl_typesupport_introspection_cpp.hpp
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_introspection_cpp.so
[5.612s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_introspection_cpp.so" to ""
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_cpp.so
[5.612s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_cpp.so" to ""
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/pythonpath.sh
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/pythonpath.dsv
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info/dependency_links.txt
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info/SOURCES.txt
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info/top_level.txt
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info/PKG-INFO
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_c.c
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_introspection_c.c
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/libremotecontrol_msgs__rosidl_generator_py.so
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_fastrtps_c.c
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_joystick_state.py
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_key_state.py
[5.612s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_key_state_s.c
[5.613s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_joystick_state_s.c
[5.613s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/__init__.py
[5.613s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/__init__.py
[5.642s] Listing '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs'...
[5.642s] Compiling '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/__init__.py'...
[5.642s] Listing '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg'...
[5.642s] Compiling '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/__init__.py'...
[5.642s] Compiling '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_joystick_state.py'...
[5.642s] Compiling '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_key_state.py'...
[5.646s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
[5.646s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so" to ""
[5.646s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
[5.647s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so" to ""
[5.647s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
[5.647s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so" to ""
[5.647s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_generator_py.so
[5.648s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_generator_py.so" to ""
[5.648s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/msg/JoystickState.idl
[5.648s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/msg/KeyState.idl
[5.648s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/msg/JoystickState.msg
[5.648s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/msg/KeyState.msg
[5.648s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/ament_index/resource_index/package_run_dependencies/remotecontrol_msgs
[5.648s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/ament_index/resource_index/parent_prefix_path/remotecontrol_msgs
[5.648s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/ament_prefix_path.sh
[5.648s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/ament_prefix_path.dsv
[5.648s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/path.sh
[5.648s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/path.dsv
[5.648s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/local_setup.bash
[5.648s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/local_setup.sh
[5.649s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/local_setup.zsh
[5.649s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/local_setup.dsv
[5.649s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.dsv
[5.649s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/ament_index/resource_index/packages/remotecontrol_msgs
[5.649s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_cExport.cmake
[5.649s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_cExport-noconfig.cmake
[5.649s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_typesupport_fastrtps_cExport.cmake
[5.649s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_typesupport_fastrtps_cExport-noconfig.cmake
[5.649s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_cppExport.cmake
[5.649s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_typesupport_fastrtps_cppExport.cmake
[5.649s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake
[5.649s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_introspection_cExport.cmake
[5.649s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_introspection_cExport-noconfig.cmake
[5.649s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_cExport.cmake
[5.649s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_cExport-noconfig.cmake
[5.649s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_introspection_cppExport.cmake
[5.650s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_introspection_cppExport-noconfig.cmake
[5.650s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_cppExport.cmake
[5.650s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_cppExport-noconfig.cmake
[5.650s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_pyExport.cmake
[5.650s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_pyExport-noconfig.cmake
[5.650s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/rosidl_cmake-extras.cmake
[5.650s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/ament_cmake_export_dependencies-extras.cmake
[5.650s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/ament_cmake_export_include_directories-extras.cmake
[5.650s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/ament_cmake_export_libraries-extras.cmake
[5.650s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/ament_cmake_export_targets-extras.cmake
[5.650s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
[5.650s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
[5.651s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgsConfig.cmake
[5.651s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgsConfig-version.cmake
[5.651s] -- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.xml
[5.653s] Invoked command in '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/remotecontrol_msgs
