[0m** WARNING ** io features related to pcap will be disabled[0m
[0m** WARNING ** io features related to png will be disabled[0m
In file included from [01m[K/home/<USER>/xugong_web_ws/src/web/src/web_node.cpp:3[m[K:
[01m[K/home/<USER>/xugong_web_ws/src/web/src/websocket_server.h:[m[K In member function ‘[01m[Kvoid WebSocketServerSingleton::avoidance_remind_callback(std_msgs::msg::Int8_<std::allocator<void> >::SharedPtr)[m[K’:
[01m[K/home/<USER>/xugong_web_ws/src/web/src/websocket_server.h:1270:73:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kmsg[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
 1270 |     void avoidance_remind_callback([01;35m[Kconst std_msgs::msg::Int8::SharedPtr msg[m[K)
      |                                    [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~[m[K
