[0.000000] (-) TimerEvent: {}
[0.000651] (can_bridge) JobQueued: {'identifier': 'can_bridge', 'dependencies': OrderedDict()}
[0.000704] (global_traj_generate) JobQueued: {'identifier': 'global_traj_generate', 'dependencies': OrderedDict()}
[0.000737] (keyframe_msgs) JobQueued: {'identifier': 'keyframe_msgs', 'dependencies': OrderedDict()}
[0.000813] (remotecontrol_msgs) JobQueued: {'identifier': 'remotecontrol_msgs', 'dependencies': OrderedDict()}
[0.000840] (web) JobQueued: {'identifier': 'web', 'dependencies': OrderedDict([('can_bridge', '/home/<USER>/xugong_web_ws/install/can_bridge'), ('global_traj_generate', '/home/<USER>/xugong_web_ws/install/global_traj_generate'), ('keyframe_msgs', '/home/<USER>/xugong_web_ws/install/keyframe_msgs'), ('remotecontrol_msgs', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs')])}
[0.000883] (can_bridge) JobStarted: {'identifier': 'can_bridge'}
[0.006463] (global_traj_generate) JobStarted: {'identifier': 'global_traj_generate'}
[0.009129] (keyframe_msgs) JobStarted: {'identifier': 'keyframe_msgs'}
[0.011647] (remotecontrol_msgs) JobStarted: {'identifier': 'remotecontrol_msgs'}
[0.015351] (can_bridge) JobProgress: {'identifier': 'can_bridge', 'progress': 'cmake'}
[0.016656] (can_bridge) JobProgress: {'identifier': 'can_bridge', 'progress': 'build'}
[0.016697] (can_bridge) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/xugong_web_ws/build/can_bridge', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/xugong_web_ws/build/can_bridge', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/can_bridge'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[0.018804] (global_traj_generate) JobProgress: {'identifier': 'global_traj_generate', 'progress': 'cmake'}
[0.019419] (global_traj_generate) JobProgress: {'identifier': 'global_traj_generate', 'progress': 'build'}
[0.019755] (global_traj_generate) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/xugong_web_ws/build/global_traj_generate', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/xugong_web_ws/build/global_traj_generate', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/global_traj_generate'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[0.021416] (keyframe_msgs) JobProgress: {'identifier': 'keyframe_msgs', 'progress': 'cmake'}
[0.022016] (keyframe_msgs) JobProgress: {'identifier': 'keyframe_msgs', 'progress': 'build'}
[0.022462] (keyframe_msgs) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/xugong_web_ws/build/keyframe_msgs', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/xugong_web_ws/build/keyframe_msgs', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/keyframe_msgs'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[0.026513] (remotecontrol_msgs) JobProgress: {'identifier': 'remotecontrol_msgs', 'progress': 'cmake'}
[0.027185] (remotecontrol_msgs) JobProgress: {'identifier': 'remotecontrol_msgs', 'progress': 'build'}
[0.027462] (remotecontrol_msgs) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[0.078842] (global_traj_generate) StdoutLine: {'line': b'[  3%] Built target global_traj_generate__cpp\n'}
[0.079971] (keyframe_msgs) StdoutLine: {'line': b'[  1%] Built target keyframe_msgs__cpp\n'}
[0.080612] (can_bridge) StdoutLine: {'line': b'[  2%] Built target can_bridge__cpp\n'}
[0.081530] (remotecontrol_msgs) StdoutLine: {'line': b'[  2%] Built target remotecontrol_msgs__cpp\n'}
[0.084307] (remotecontrol_msgs) StdoutLine: {'line': b'[ 12%] Built target remotecontrol_msgs__rosidl_generator_c\n'}
[0.085168] (global_traj_generate) StdoutLine: {'line': b'[ 12%] Built target global_traj_generate__rosidl_generator_c\n'}
[0.086303] (global_traj_generate) StdoutLine: {'line': b'[ 12%] Built target ament_cmake_python_copy_global_traj_generate\n'}
[0.087721] (can_bridge) StdoutLine: {'line': b'[ 12%] Built target can_bridge__rosidl_generator_c\n'}
[0.093291] (can_bridge) StdoutLine: {'line': b'[ 12%] Built target ament_cmake_python_copy_can_bridge\n'}
[0.093530] (keyframe_msgs) StdoutLine: {'line': b'[ 12%] Built target keyframe_msgs__rosidl_generator_c\n'}
[0.093968] (remotecontrol_msgs) StdoutLine: {'line': b'[ 12%] Built target ament_cmake_python_copy_remotecontrol_msgs\n'}
[0.099916] (-) TimerEvent: {}
[0.110433] (global_traj_generate) StdoutLine: {'line': b'[ 22%] Built target global_traj_generate__rosidl_typesupport_fastrtps_cpp\n'}
[0.110848] (global_traj_generate) StdoutLine: {'line': b'[ 32%] Built target global_traj_generate__rosidl_typesupport_cpp\n'}
[0.111921] (can_bridge) StdoutLine: {'line': b'[ 23%] Built target can_bridge__rosidl_typesupport_fastrtps_cpp\n'}
[0.112056] (keyframe_msgs) StdoutLine: {'line': b'[ 12%] Built target ament_cmake_python_copy_keyframe_msgs\n'}
[0.112173] (keyframe_msgs) StdoutLine: {'line': b'[ 24%] Built target keyframe_msgs__rosidl_typesupport_introspection_cpp\n'}
[0.112275] (global_traj_generate) StdoutLine: {'line': b'[ 41%] Built target global_traj_generate__rosidl_typesupport_introspection_cpp\n'}
[0.115343] (keyframe_msgs) StdoutLine: {'line': b'[ 35%] Built target keyframe_msgs__rosidl_typesupport_fastrtps_cpp\n'}
[0.117020] (keyframe_msgs) StdoutLine: {'line': b'[ 47%] Built target keyframe_msgs__rosidl_typesupport_cpp\n'}
[0.117263] (remotecontrol_msgs) StdoutLine: {'line': b'[ 23%] Built target remotecontrol_msgs__rosidl_typesupport_fastrtps_cpp\n'}
[0.117428] (can_bridge) StdoutLine: {'line': b'[ 34%] Built target can_bridge__rosidl_typesupport_introspection_cpp\n'}
[0.117898] (global_traj_generate) StdoutLine: {'line': b'[ 51%] Built target global_traj_generate__rosidl_typesupport_c\n'}
[0.120285] (remotecontrol_msgs) StdoutLine: {'line': b'[ 33%] Built target remotecontrol_msgs__rosidl_typesupport_cpp\n'}
[0.122114] (can_bridge) StdoutLine: {'line': b'[ 44%] Built target can_bridge__rosidl_typesupport_cpp\n'}
[0.122299] (can_bridge) StdoutLine: {'line': b'[ 55%] Built target can_bridge__rosidl_typesupport_c\n'}
[0.122489] (keyframe_msgs) StdoutLine: {'line': b'[ 58%] Built target keyframe_msgs__rosidl_typesupport_c\n'}
[0.122592] (remotecontrol_msgs) StdoutLine: {'line': b'[ 51%] Built target remotecontrol_msgs__rosidl_typesupport_introspection_cpp\n'}
[0.123082] (can_bridge) StdoutLine: {'line': b'[ 65%] Built target can_bridge__rosidl_typesupport_introspection_c\n'}
[0.123283] (remotecontrol_msgs) StdoutLine: {'line': b'[ 53%] Built target remotecontrol_msgs__rosidl_typesupport_introspection_c\n'}
[0.123503] (global_traj_generate) StdoutLine: {'line': b'[ 61%] Built target global_traj_generate__rosidl_typesupport_fastrtps_c\n'}
[0.125454] (global_traj_generate) StdoutLine: {'line': b'[ 70%] Built target global_traj_generate__rosidl_typesupport_introspection_c\n'}
[0.126376] (remotecontrol_msgs) StdoutLine: {'line': b'[ 64%] Built target remotecontrol_msgs__rosidl_typesupport_c\n'}
[0.127325] (keyframe_msgs) StdoutLine: {'line': b'[ 70%] Built target keyframe_msgs__rosidl_typesupport_fastrtps_c\n'}
[0.127462] (can_bridge) StdoutLine: {'line': b'[ 76%] Built target can_bridge__rosidl_typesupport_fastrtps_c\n'}
[0.128780] (remotecontrol_msgs) StdoutLine: {'line': b'[ 74%] Built target remotecontrol_msgs__rosidl_typesupport_fastrtps_c\n'}
[0.130845] (keyframe_msgs) StdoutLine: {'line': b'[ 82%] Built target keyframe_msgs__rosidl_typesupport_introspection_c\n'}
[0.139737] (global_traj_generate) StdoutLine: {'line': b'[ 70%] Built target global_traj_generate\n'}
[0.140670] (can_bridge) StdoutLine: {'line': b'[ 76%] Built target can_bridge\n'}
[0.143116] (remotecontrol_msgs) StdoutLine: {'line': b'[ 74%] Built target remotecontrol_msgs\n'}
[0.144987] (keyframe_msgs) StdoutLine: {'line': b'[ 82%] Built target keyframe_msgs\n'}
[0.155922] (can_bridge) StdoutLine: {'line': b'[ 78%] Built target can_bridge__py\n'}
[0.157299] (remotecontrol_msgs) StdoutLine: {'line': b'[ 76%] Built target remotecontrol_msgs__py\n'}
[0.158386] (global_traj_generate) StdoutLine: {'line': b'[ 74%] Built target global_traj_generate__py\n'}
[0.159201] (keyframe_msgs) StdoutLine: {'line': b'[ 83%] Built target keyframe_msgs__py\n'}
[0.173312] (global_traj_generate) StdoutLine: {'line': b'[ 80%] Built target global_traj_generate__rosidl_generator_py\n'}
[0.173512] (can_bridge) StdoutLine: {'line': b'[ 87%] Built target can_bridge__rosidl_generator_py\n'}
[0.178632] (keyframe_msgs) StdoutLine: {'line': b'[ 93%] Built target keyframe_msgs__rosidl_generator_py\n'}
[0.178881] (remotecontrol_msgs) StdoutLine: {'line': b'[ 84%] Built target remotecontrol_msgs__rosidl_generator_py\n'}
[0.193186] (can_bridge) StdoutLine: {'line': b'[ 91%] Built target can_bridge__rosidl_typesupport_fastrtps_c__pyext\n'}
[0.193362] (can_bridge) StdoutLine: {'line': b'[ 95%] Built target can_bridge__rosidl_typesupport_c__pyext\n'}
[0.194611] (global_traj_generate) StdoutLine: {'line': b'[ 93%] Built target global_traj_generate__rosidl_typesupport_c__pyext\n'}
[0.194762] (global_traj_generate) StdoutLine: {'line': b'[ 93%] Built target global_traj_generate__rosidl_typesupport_introspection_c__pyext\n'}
[0.195795] (can_bridge) StdoutLine: {'line': b'[100%] Built target can_bridge__rosidl_typesupport_introspection_c__pyext\n'}
[0.195939] (global_traj_generate) StdoutLine: {'line': b'[100%] Built target global_traj_generate__rosidl_typesupport_fastrtps_c__pyext\n'}
[0.198767] (keyframe_msgs) StdoutLine: {'line': b'[ 95%] Built target keyframe_msgs__rosidl_typesupport_introspection_c__pyext\n'}
[0.198964] (keyframe_msgs) StdoutLine: {'line': b'[ 97%] Built target keyframe_msgs__rosidl_typesupport_fastrtps_c__pyext\n'}
[0.199367] (remotecontrol_msgs) StdoutLine: {'line': b'[ 89%] Built target remotecontrol_msgs__rosidl_typesupport_introspection_c__pyext\n'}
[0.200002] (-) TimerEvent: {}
[0.200948] (remotecontrol_msgs) StdoutLine: {'line': b'[ 94%] Built target remotecontrol_msgs__rosidl_typesupport_c__pyext\n'}
[0.201056] (remotecontrol_msgs) StdoutLine: {'line': b'[100%] Built target remotecontrol_msgs__rosidl_typesupport_fastrtps_c__pyext\n'}
[0.201319] (keyframe_msgs) StdoutLine: {'line': b'[100%] Built target keyframe_msgs__rosidl_typesupport_c__pyext\n'}
[0.278633] (remotecontrol_msgs) StdoutLine: {'line': b'running egg_info\n'}
[0.279090] (remotecontrol_msgs) StdoutLine: {'line': b'writing remotecontrol_msgs.egg-info/PKG-INFO\n'}
[0.279267] (remotecontrol_msgs) StdoutLine: {'line': b'writing dependency_links to remotecontrol_msgs.egg-info/dependency_links.txt\n'}
[0.279390] (remotecontrol_msgs) StdoutLine: {'line': b'writing top-level names to remotecontrol_msgs.egg-info/top_level.txt\n'}
[0.281039] (remotecontrol_msgs) StdoutLine: {'line': b"reading manifest file 'remotecontrol_msgs.egg-info/SOURCES.txt'\n"}
[0.281634] (remotecontrol_msgs) StdoutLine: {'line': b"writing manifest file 'remotecontrol_msgs.egg-info/SOURCES.txt'\n"}
[0.281918] (can_bridge) StdoutLine: {'line': b'running egg_info\n'}
[0.282507] (can_bridge) StdoutLine: {'line': b'writing can_bridge.egg-info/PKG-INFO\n'}
[0.282657] (can_bridge) StdoutLine: {'line': b'writing dependency_links to can_bridge.egg-info/dependency_links.txt\n'}
[0.282771] (can_bridge) StdoutLine: {'line': b'writing top-level names to can_bridge.egg-info/top_level.txt\n'}
[0.284276] (can_bridge) StdoutLine: {'line': b"reading manifest file 'can_bridge.egg-info/SOURCES.txt'\n"}
[0.285206] (can_bridge) StdoutLine: {'line': b"writing manifest file 'can_bridge.egg-info/SOURCES.txt'\n"}
[0.297389] (keyframe_msgs) StdoutLine: {'line': b'running egg_info\n'}
[0.297929] (keyframe_msgs) StdoutLine: {'line': b'writing keyframe_msgs.egg-info/PKG-INFO\n'}
[0.298110] (keyframe_msgs) StdoutLine: {'line': b'writing dependency_links to keyframe_msgs.egg-info/dependency_links.txt\n'}
[0.298247] (keyframe_msgs) StdoutLine: {'line': b'writing top-level names to keyframe_msgs.egg-info/top_level.txt\n'}
[0.300031] (global_traj_generate) StdoutLine: {'line': b'running egg_info\n'}
[0.300193] (-) TimerEvent: {}
[0.300377] (keyframe_msgs) StdoutLine: {'line': b"reading manifest file 'keyframe_msgs.egg-info/SOURCES.txt'\n"}
[0.300629] (global_traj_generate) StdoutLine: {'line': b'writing global_traj_generate.egg-info/PKG-INFO\n'}
[0.300796] (global_traj_generate) StdoutLine: {'line': b'writing dependency_links to global_traj_generate.egg-info/dependency_links.txt\n'}
[0.300908] (global_traj_generate) StdoutLine: {'line': b'writing top-level names to global_traj_generate.egg-info/top_level.txt\n'}
[0.301138] (keyframe_msgs) StdoutLine: {'line': b"writing manifest file 'keyframe_msgs.egg-info/SOURCES.txt'\n"}
[0.302472] (global_traj_generate) StdoutLine: {'line': b"reading manifest file 'global_traj_generate.egg-info/SOURCES.txt'\n"}
[0.303010] (global_traj_generate) StdoutLine: {'line': b"writing manifest file 'global_traj_generate.egg-info/SOURCES.txt'\n"}
[0.306436] (remotecontrol_msgs) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_remotecontrol_msgs_egg\n'}
[0.311449] (can_bridge) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_can_bridge_egg\n'}
[0.318117] (remotecontrol_msgs) CommandEnded: {'returncode': 0}
[0.320207] (remotecontrol_msgs) JobProgress: {'identifier': 'remotecontrol_msgs', 'progress': 'install'}
[0.324184] (keyframe_msgs) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_keyframe_msgs_egg\n'}
[0.326843] (global_traj_generate) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_global_traj_generate_egg\n'}
[0.327805] (remotecontrol_msgs) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs'], 'cwd': '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[0.329552] (can_bridge) CommandEnded: {'returncode': 0}
[0.330422] (can_bridge) JobProgress: {'identifier': 'can_bridge', 'progress': 'install'}
[0.330583] (can_bridge) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/xugong_web_ws/build/can_bridge'], 'cwd': '/home/<USER>/xugong_web_ws/build/can_bridge', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/can_bridge'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[0.334180] (remotecontrol_msgs) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.334566] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/ament_index/resource_index/rosidl_interfaces/remotecontrol_msgs\n'}
[0.334728] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs\n'}
[0.334883] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg\n'}
[0.334983] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/joystick_state.h\n'}
[0.335029] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/key_state.h\n'}
[0.335065] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_generator_c__visibility_control.h\n'}
[0.335099] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail\n'}
[0.335223] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__struct.h\n'}
[0.335399] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__type_support.h\n'}
[0.335508] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__functions.h\n'}
[0.335692] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__functions.c\n'}
[0.336117] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__struct.h\n'}
[0.336562] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__functions.h\n'}
[0.336938] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__type_support.h\n'}
[0.337591] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__functions.c\n'}
[0.337645] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/library_path.sh\n'}
[0.337682] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/library_path.dsv\n'}
[0.337714] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_generator_c.so\n'}
[0.337745] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs\n'}
[0.337776] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg\n'}
[0.337807] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[0.338020] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail\n'}
[0.338059] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__rosidl_typesupport_fastrtps_c.h\n'}
[0.338092] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__rosidl_typesupport_fastrtps_c.h\n'}
[0.338125] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_fastrtps_c.so\n'}
[0.338161] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs\n'}
[0.338473] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg\n'}
[0.338595] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/joystick_state.hpp\n'}
[0.338908] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/key_state.hpp\n'}
[0.339814] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[0.340469] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail\n'}
[0.340960] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__struct.hpp\n'}
[0.341099] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__type_support.hpp\n'}
[0.341235] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__builder.hpp\n'}
[0.341334] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__traits.hpp\n'}
[0.341426] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__struct.hpp\n'}
[0.341513] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__traits.hpp\n'}
[0.341599] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__builder.hpp\n'}
[0.341684] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__type_support.hpp\n'}
[0.341768] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs\n'}
[0.341853] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg\n'}
[0.341947] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[0.342082] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail\n'}
[0.342182] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/dds_fastrtps\n'}
[0.342315] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.342399] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.342482] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_fastrtps_cpp.so\n'}
[0.342565] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs\n'}
[0.342646] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg\n'}
[0.342727] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[0.342892] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail\n'}
[0.342977] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__type_support.c\n'}
[0.343060] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__type_support.c\n'}
[0.343151] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__rosidl_typesupport_introspection_c.h\n'}
[0.343275] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__rosidl_typesupport_introspection_c.h\n'}
[0.343359] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_introspection_c.so\n'}
[0.343442] (global_traj_generate) CommandEnded: {'returncode': 0}
[0.344022] (can_bridge) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.344169] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/rosidl_interfaces/can_bridge\n'}
[0.345276] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge\n'}
[0.345600] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg\n'}
[0.345676] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_diagnostic.h\n'}
[0.345738] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_generator_c__visibility_control.h\n'}
[0.345799] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_motion.h\n'}
[0.345958] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_control_flags.h\n'}
[0.346012] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail\n'}
[0.346062] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.h\n'}
[0.346111] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.h\n'}
[0.346207] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__functions.h\n'}
[0.346370] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__struct.h\n'}
[0.346495] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__functions.c\n'}
[0.346615] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_c.so\n'}
[0.346760] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.h\n'}
[0.346874] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__functions.c\n'}
[0.346957] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs\n'}
[0.347039] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg\n'}
[0.347138] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail\n'}
[0.347242] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__type_support.cpp\n'}
[0.347365] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__type_support.cpp\n'}
[0.347472] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/key_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.347552] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/include/remotecontrol_msgs/remotecontrol_msgs/msg/detail/joystick_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.347649] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_introspection_cpp.so\n'}
[0.347729] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_typesupport_cpp.so\n'}
[0.347832] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__functions.c\n'}
[0.347953] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__struct.h\n'}
[0.348073] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__functions.h\n'}
[0.348195] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__functions.h\n'}
[0.348294] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__struct.h\n'}
[0.348374] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/library_path.sh\n'}
[0.348468] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/library_path.dsv\n'}
[0.348550] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_generator_c.so\n'}
[0.348658] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge\n'}
[0.348774] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/pythonpath.sh\n'}
[0.348865] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/pythonpath.dsv\n'}
[0.348959] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info\n'}
[0.349048] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[0.349130] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[0.349240] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg\n'}
[0.349352] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info/top_level.txt\n'}
[0.349482] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[0.349564] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail\n'}
[0.349659] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_fastrtps_c.h\n'}
[0.349738] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_fastrtps_c.h\n'}
[0.349817] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_fastrtps_c.h\n'}
[0.349896] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_fastrtps_c.so\n'}
[0.349993] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge\n'}
[0.350072] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg\n'}
[0.350227] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_control_flags.hpp\n'}
[0.350302] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[0.350347] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_diagnostic.hpp\n'}
[0.350381] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail\n'}
[0.350414] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__traits.hpp\n'}
[0.350540] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.hpp\n'}
[0.350643] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__traits.hpp\n'}
[0.350737] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.hpp\n'}
[0.350815] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__traits.hpp\n'}
[0.350893] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__struct.hpp\n'}
[0.351009] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[0.351137] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs\n'}
[0.351264] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_c.c\n'}
[0.351344] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_introspection_c.c\n'}
[0.351450] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/libremotecontrol_msgs__rosidl_generator_py.so\n'}
[0.351502] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.351545] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.351577] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.351609] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/_remotecontrol_msgs_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[0.351639] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg\n'}
[0.351669] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_joystick_state.py\n'}
[0.351700] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_key_state.py\n'}
[0.351729] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_key_state_s.c\n'}
[0.351760] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/_joystick_state_s.c\n'}
[0.351791] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg/__init__.py\n'}
[0.351821] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/__init__.py\n'}
[0.351851] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__builder.hpp\n'}
[0.351887] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__builder.hpp\n'}
[0.351916] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.hpp\n'}
[0.351943] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__builder.hpp\n'}
[0.351973] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__struct.hpp\n'}
[0.352002] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__struct.hpp\n'}
[0.352031] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_motion.hpp\n'}
[0.352058] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge\n'}
[0.352086] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg\n'}
[0.352113] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[0.352141] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail\n'}
[0.352182] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/dds_fastrtps\n'}
[0.352211] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.352239] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.352267] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.352297] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_fastrtps_cpp.so\n'}
[0.352326] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge\n'}
[0.352354] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg\n'}
[0.352382] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[0.352410] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail\n'}
[0.352439] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_introspection_c.h\n'}
[0.352467] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_introspection_c.h\n'}
[0.352494] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_introspection_c.h\n'}
[0.352522] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.c\n'}
[0.352549] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.c\n'}
[0.352575] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.c\n'}
[0.352602] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_introspection_c.so\n'}
[0.352630] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_c.so\n'}
[0.352658] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge\n'}
[0.352689] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg\n'}
[0.352718] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail\n'}
[0.352747] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.cpp\n'}
[0.352777] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.352806] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.cpp\n'}
[0.352837] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.352867] (global_traj_generate) JobProgress: {'identifier': 'global_traj_generate', 'progress': 'install'}
[0.352880] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.cpp\n'}
[0.352916] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.352946] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_introspection_cpp.so\n'}
[0.352975] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_cpp.so\n'}
[0.353006] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/pythonpath.sh\n'}
[0.353035] (global_traj_generate) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/xugong_web_ws/build/global_traj_generate'], 'cwd': '/home/<USER>/xugong_web_ws/build/global_traj_generate', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/global_traj_generate'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[0.353332] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/pythonpath.dsv\n'}
[0.353379] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info\n'}
[0.353415] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[0.353447] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[0.353479] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/top_level.txt\n'}
[0.353512] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[0.353543] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge\n'}
[0.353574] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/_can_bridge_s.ep.rosidl_typesupport_c.c\n'}
[0.353713] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.353768] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.353801] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.353833] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/_can_bridge_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[0.353864] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg\n'}
[0.353895] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_diagnostic_s.c\n'}
[0.353926] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_motion_s.c\n'}
[0.353956] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_motion.py\n'}
[0.353986] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_diagnostic.py\n'}
[0.354016] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_control_flags_s.c\n'}
[0.354046] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_control_flags.py\n'}
[0.354076] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/__init__.py\n'}
[0.354106] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/__init__.py\n'}
[0.354137] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/libcan_bridge__rosidl_generator_py.so\n'}
[0.354174] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/_can_bridge_s.ep.rosidl_typesupport_introspection_c.c\n'}
[0.354207] (keyframe_msgs) CommandEnded: {'returncode': 0}
[0.354424] (keyframe_msgs) JobProgress: {'identifier': 'keyframe_msgs', 'progress': 'install'}
[0.354439] (keyframe_msgs) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/xugong_web_ws/build/keyframe_msgs'], 'cwd': '/home/<USER>/xugong_web_ws/build/keyframe_msgs', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/keyframe_msgs'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble')]), 'shell': False}
[0.354758] (global_traj_generate) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.354805] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/ament_index/resource_index/rosidl_interfaces/global_traj_generate\n'}
[0.354842] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[0.354877] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[0.354908] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_generator_c__visibility_control.h\n'}
[0.354940] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/lateral_deviation.h\n'}
[0.354972] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[0.355003] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__functions.c\n'}
[0.355033] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__functions.h\n'}
[0.355064] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__type_support.h\n'}
[0.355095] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__struct.h\n'}
[0.355126] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/environment/library_path.sh\n'}
[0.355163] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/environment/library_path.dsv\n'}
[0.355271] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_c.so\n'}
[0.355329] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[0.355363] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[0.355394] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[0.355423] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[0.355455] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__rosidl_typesupport_fastrtps_c.h\n'}
[0.355486] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_c.so\n'}
[0.355518] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[0.355552] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[0.355584] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[0.355614] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/lateral_deviation.hpp\n'}
[0.355661] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[0.355691] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__traits.hpp\n'}
[0.355723] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__struct.hpp\n'}
[0.355752] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__type_support.hpp\n'}
[0.355782] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__builder.hpp\n'}
[0.355812] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[0.355841] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[0.355871] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[0.355900] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[0.355931] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.355961] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/dds_fastrtps\n'}
[0.355992] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_cpp.so\n'}
[0.356023] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[0.356053] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[0.356083] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[0.356115] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[0.356150] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__rosidl_typesupport_introspection_c.h\n'}
[0.356187] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__type_support.c\n'}
[0.356218] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_c.so\n'}
[0.356249] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_c.so\n'}
[0.356280] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[0.356310] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[0.356339] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[0.356368] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.356397] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/lateral_deviation__type_support.cpp\n'}
[0.356428] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_cpp.so\n'}
[0.356565] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_cpp.so\n'}
[0.356598] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/environment/pythonpath.sh\n'}
[0.356630] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/environment/pythonpath.dsv\n'}
[0.356660] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info\n'}
[0.356711] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[0.356744] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[0.356777] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/top_level.txt\n'}
[0.356808] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[0.356840] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate\n'}
[0.356871] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_introspection_c.c\n'}
[0.356902] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[0.356933] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.356965] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/libglobal_traj_generate__rosidl_generator_py.so\n'}
[0.357023] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_c.c\n'}
[0.357055] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.357086] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.357120] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg\n'}
[0.357158] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_lateral_deviation.py\n'}
[0.357192] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_lateral_deviation_s.c\n'}
[0.357221] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/__init__.py\n'}
[0.357251] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/__init__.py\n'}
[0.357280] (keyframe_msgs) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.357315] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/ament_index/resource_index/rosidl_interfaces/keyframe_msgs\n'}
[0.357348] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs\n'}
[0.357459] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg\n'}
[0.357489] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/joystick_state.h\n'}
[0.357517] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/key_state.h\n'}
[0.357544] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/vehicle_diagnostic.h\n'}
[0.357575] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/rosidl_generator_c__visibility_control.h\n'}
[0.357604] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/key_frame_pose_array.h\n'}
[0.357646] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/vehicle_motion.h\n'}
[0.357673] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/key_frame_pose.h\n'}
[0.357700] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/vehicle_control_flags.h\n'}
[0.357726] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/key_frame.h\n'}
[0.357766] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/lateral_deviation.h\n'}
[0.357796] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail\n'}
[0.357826] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__type_support.h\n'}
[0.357856] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__functions.c\n'}
[0.357886] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__functions.h\n'}
[0.357917] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__type_support.h\n'}
[0.357946] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__functions.h\n'}
[0.357976] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__type_support.h\n'}
[0.358005] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__struct.h\n'}
[0.358035] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__struct.h\n'}
[0.358066] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__struct.h\n'}
[0.358100] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__functions.c\n'}
[0.358130] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__type_support.h\n'}
[0.358175] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__type_support.h\n'}
[0.358213] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__type_support.h\n'}
[0.358244] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__functions.h\n'}
[0.358273] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__functions.h\n'}
[0.358303] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__functions.c\n'}
[0.358333] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__type_support.h\n'}
[0.358363] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__struct.h\n'}
[0.358392] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__type_support.h\n'}
[0.358440] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__functions.c\n'}
[0.358471] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__functions.c\n'}
[0.358501] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__functions.h\n'}
[0.358530] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__functions.c\n'}
[0.358560] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__functions.c\n'}
[0.358587] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__functions.c\n'}
[0.358613] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__functions.h\n'}
[0.358639] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__type_support.h\n'}
[0.358668] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__struct.h\n'}
[0.358698] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__functions.h\n'}
[0.358798] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__struct.h\n'}
[0.358860] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__struct.h\n'}
[0.358893] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__functions.h\n'}
[0.358927] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__functions.h\n'}
[0.358958] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__struct.h\n'}
[0.358987] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__functions.c\n'}
[0.359017] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__struct.h\n'}
[0.359047] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/environment/library_path.sh\n'}
[0.359076] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/environment/library_path.dsv\n'}
[0.359105] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_generator_c.so\n'}
[0.359134] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs\n'}
[0.359237] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg\n'}
[0.359279] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[0.359309] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail\n'}
[0.359339] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__rosidl_typesupport_fastrtps_c.h\n'}
[0.359369] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__rosidl_typesupport_fastrtps_c.h\n'}
[0.359399] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__rosidl_typesupport_fastrtps_c.h\n'}
[0.359428] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__rosidl_typesupport_fastrtps_c.h\n'}
[0.359458] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__rosidl_typesupport_fastrtps_c.h\n'}
[0.359487] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__rosidl_typesupport_fastrtps_c.h\n'}
[0.359516] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__rosidl_typesupport_fastrtps_c.h\n'}
[0.359546] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__rosidl_typesupport_fastrtps_c.h\n'}
[0.359575] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__rosidl_typesupport_fastrtps_c.h\n'}
[0.359605] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_fastrtps_c.so\n'}
[0.359635] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs\n'}
[0.359665] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg\n'}
[0.359693] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/key_frame_pose.hpp\n'}
[0.359721] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/joystick_state.hpp\n'}
[0.359750] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/key_state.hpp\n'}
[0.359780] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/key_frame_pose_array.hpp\n'}
[0.359808] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/vehicle_control_flags.hpp\n'}
[0.359838] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[0.359868] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/vehicle_diagnostic.hpp\n'}
[0.359895] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/lateral_deviation.hpp\n'}
[0.359922] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/key_frame.hpp\n'}
[0.359950] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail\n'}
[0.359978] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__traits.hpp\n'}
[0.360007] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__struct.hpp\n'}
[0.360036] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__type_support.hpp\n'}
[0.360065] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__builder.hpp\n'}
[0.360094] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__traits.hpp\n'}
[0.360122] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__type_support.hpp\n'}
[0.360168] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__builder.hpp\n'}
[0.360199] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__type_support.hpp\n'}
[0.360228] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__type_support.hpp\n'}
[0.360256] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__traits.hpp\n'}
[0.360285] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__type_support.hpp\n'}
[0.360314] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__builder.hpp\n'}
[0.360386] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__struct.hpp\n'}
[0.360413] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__traits.hpp\n'}
[0.360439] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__builder.hpp\n'}
[0.360467] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__traits.hpp\n'}
[0.360495] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__traits.hpp\n'}
[0.360524] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__struct.hpp\n'}
[0.360552] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__struct.hpp\n'}
[0.360580] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__struct.hpp\n'}
[0.360609] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__builder.hpp\n'}
[0.360638] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__builder.hpp\n'}
[0.360667] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__traits.hpp\n'}
[0.360697] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__traits.hpp\n'}
[0.360725] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__type_support.hpp\n'}
[0.360753] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__traits.hpp\n'}
[0.360781] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__builder.hpp\n'}
[0.360810] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__type_support.hpp\n'}
[0.360840] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__builder.hpp\n'}
[0.360869] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__struct.hpp\n'}
[0.360897] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__struct.hpp\n'}
[0.360926] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__builder.hpp\n'}
[0.360955] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__struct.hpp\n'}
[0.361006] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__type_support.hpp\n'}
[0.361036] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__type_support.hpp\n'}
[0.361065] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__struct.hpp\n'}
[0.361096] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/vehicle_motion.hpp\n'}
[0.361125] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs\n'}
[0.361157] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg\n'}
[0.361188] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[0.361216] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail\n'}
[0.361245] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.361275] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/dds_fastrtps\n'}
[0.361303] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.361332] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.361362] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.361390] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.361419] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.361448] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.361478] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.361547] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.361582] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_fastrtps_cpp.so\n'}
[0.361612] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs\n'}
[0.361641] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg\n'}
[0.361670] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[0.361702] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail\n'}
[0.361731] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__rosidl_typesupport_introspection_c.h\n'}
[0.361762] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__rosidl_typesupport_introspection_c.h\n'}
[0.361791] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__type_support.c\n'}
[0.361820] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__type_support.c\n'}
[0.361850] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__rosidl_typesupport_introspection_c.h\n'}
[0.361879] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__rosidl_typesupport_introspection_c.h\n'}
[0.361908] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__type_support.c\n'}
[0.361938] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__rosidl_typesupport_introspection_c.h\n'}
[0.361968] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__rosidl_typesupport_introspection_c.h\n'}
[0.362001] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__rosidl_typesupport_introspection_c.h\n'}
[0.362033] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__type_support.c\n'}
[0.362063] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__rosidl_typesupport_introspection_c.h\n'}
[0.362097] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__rosidl_typesupport_introspection_c.h\n'}
[0.362139] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__type_support.c\n'}
[0.362381] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__type_support.c\n'}
[0.362464] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__type_support.c\n'}
[0.362521] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__type_support.c\n'}
[0.362575] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__type_support.c\n'}
[0.362622] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_introspection_c.so\n'}
[0.362678] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_c.so\n'}
[0.362728] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs\n'}
[0.362779] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg\n'}
[0.362828] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail\n'}
[0.362879] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__type_support.cpp\n'}
[0.362925] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__type_support.cpp\n'}
[0.362970] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.363016] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.363065] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_motion__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.363112] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__type_support.cpp\n'}
[0.363248] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame__type_support.cpp\n'}
[0.363394] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose__type_support.cpp\n'}
[0.363494] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__type_support.cpp\n'}
[0.363588] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.363677] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/joystick_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.363763] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/key_frame_pose_array__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.363849] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__type_support.cpp\n'}
[0.363935] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.364017] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.364110] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/lateral_deviation__type_support.cpp\n'}
[0.364212] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_diagnostic__type_support.cpp\n'}
[0.364304] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/include/keyframe_msgs/keyframe_msgs/msg/detail/vehicle_control_flags__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.364387] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_introspection_cpp.so\n'}
[0.364469] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_typesupport_cpp.so\n'}
[0.364550] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/environment/pythonpath.sh\n'}
[0.364631] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/environment/pythonpath.dsv\n'}
[0.364711] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs-0.0.0-py3.10.egg-info\n'}
[0.364791] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[0.364872] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[0.364996] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs-0.0.0-py3.10.egg-info/top_level.txt\n'}
[0.365078] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[0.365191] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs\n'}
[0.365279] (remotecontrol_msgs) StdoutLine: {'line': b"Listing '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs'...\n"}
[0.365387] (remotecontrol_msgs) StdoutLine: {'line': b"Listing '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/msg'...\n"}
[0.365471] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/_keyframe_msgs_s.ep.rosidl_typesupport_c.c\n'}
[0.365585] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.365669] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.365798] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.365921] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/_keyframe_msgs_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[0.366037] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/libkeyframe_msgs__rosidl_generator_py.so\n'}
[0.366162] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/_keyframe_msgs_s.ep.rosidl_typesupport_introspection_c.c\n'}
[0.366255] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg\n'}
[0.366342] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_vehicle_diagnostic_s.c\n'}
[0.366420] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_frame_pose_array.py\n'}
[0.366498] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_vehicle_motion_s.c\n'}
[0.366576] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_vehicle_motion.py\n'}
[0.366655] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_frame_pose.py\n'}
[0.366734] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_lateral_deviation.py\n'}
[0.366812] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_frame_pose_array_s.c\n'}
[0.366894] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_vehicle_diagnostic.py\n'}
[0.366982] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_vehicle_control_flags_s.c\n'}
[0.367060] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_frame.py\n'}
[0.367139] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_joystick_state.py\n'}
[0.367256] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_state.py\n'}
[0.367335] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_lateral_deviation_s.c\n'}
[0.367414] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_frame_pose_s.c\n'}
[0.367495] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_vehicle_control_flags.py\n'}
[0.367580] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_state_s.c\n'}
[0.367663] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_key_frame_s.c\n'}
[0.367744] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/_joystick_state_s.c\n'}
[0.367875] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg/__init__.py\n'}
[0.367954] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/__init__.py\n'}
[0.368035] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.368141] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.368260] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages/remotecontrol_msgs/remotecontrol_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.368347] (can_bridge) StdoutLine: {'line': b"Listing '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge'...\n"}
[0.368436] (can_bridge) StdoutLine: {'line': b"Listing '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg'...\n"}
[0.368544] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/libremotecontrol_msgs__rosidl_generator_py.so\n'}
[0.368627] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/msg/JoystickState.idl\n'}
[0.368708] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/msg/KeyState.idl\n'}
[0.368836] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/msg/JoystickState.msg\n'}
[0.368913] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/msg/KeyState.msg\n'}
[0.369003] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/ament_index/resource_index/package_run_dependencies/remotecontrol_msgs\n'}
[0.369083] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/ament_index/resource_index/parent_prefix_path/remotecontrol_msgs\n'}
[0.369204] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/ament_prefix_path.sh\n'}
[0.369304] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/ament_prefix_path.dsv\n'}
[0.369383] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/path.sh\n'}
[0.369460] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/environment/path.dsv\n'}
[0.369536] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/local_setup.bash\n'}
[0.369614] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/local_setup.sh\n'}
[0.369749] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/local_setup.zsh\n'}
[0.369864] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/local_setup.dsv\n'}
[0.369988] (remotecontrol_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.dsv\n'}
[0.370087] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/ament_index/resource_index/packages/remotecontrol_msgs\n'}
[0.370191] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_cExport.cmake\n'}
[0.370297] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_cExport-noconfig.cmake\n'}
[0.370411] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[0.370513] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[0.370606] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_cppExport.cmake\n'}
[0.370680] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[0.370755] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[0.370872] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_introspection_cExport.cmake\n'}
[0.371131] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[0.371717] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.372162] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_cExport.cmake\n'}
[0.372397] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_cExport-noconfig.cmake\n'}
[0.372483] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_introspection_cppExport.cmake\n'}
[0.372587] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[0.372668] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.372763] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_cppExport.cmake\n'}
[0.372838] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgs__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[0.372917] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.373097] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_generator_py.so\n'}
[0.373229] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_pyExport.cmake\n'}
[0.373322] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/export_remotecontrol_msgs__rosidl_generator_pyExport-noconfig.cmake\n'}
[0.373446] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/rosidl_cmake-extras.cmake\n'}
[0.373549] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.373627] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[0.373703] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[0.373780] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/ament_cmake_export_targets-extras.cmake\n'}
[0.373857] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[0.373936] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[0.374029] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgsConfig.cmake\n'}
[0.374212] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/cmake/remotecontrol_msgsConfig-version.cmake\n'}
[0.374323] (remotecontrol_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.xml\n'}
[0.374467] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleControlFlags.idl\n'}
[0.374606] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleDiagnostic.idl\n'}
[0.374780] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleMotion.idl\n'}
[0.374860] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleControlFlags.msg\n'}
[0.374936] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleDiagnostic.msg\n'}
[0.375013] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleMotion.msg\n'}
[0.375090] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/package_run_dependencies/can_bridge\n'}
[0.375176] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/parent_prefix_path/can_bridge\n'}
[0.375282] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/ament_prefix_path.sh\n'}
[0.375364] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/ament_prefix_path.dsv\n'}
[0.375443] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/path.sh\n'}
[0.375835] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/path.dsv\n'}
[0.375918] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.bash\n'}
[0.376014] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.sh\n'}
[0.376092] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.zsh\n'}
[0.376254] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.dsv\n'}
[0.376360] (can_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.dsv\n'}
[0.376542] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/packages/can_bridge\n'}
[0.376620] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_cExport.cmake\n'}
[0.376697] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_cExport-noconfig.cmake\n'}
[0.376774] (remotecontrol_msgs) CommandEnded: {'returncode': 0}
[0.377182] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[0.377330] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[0.377413] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_cppExport.cmake\n'}
[0.377492] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[0.377646] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[0.377729] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cExport.cmake\n'}
[0.377808] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[0.377885] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cExport.cmake\n'}
[0.377977] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cExport-noconfig.cmake\n'}
[0.378058] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cppExport.cmake\n'}
[0.378135] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[0.378221] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cppExport.cmake\n'}
[0.378297] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[0.378373] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_pyExport.cmake\n'}
[0.378451] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_pyExport-noconfig.cmake\n'}
[0.378525] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/rosidl_cmake-extras.cmake\n'}
[0.378616] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.378819] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[0.379081] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[0.379189] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_targets-extras.cmake\n'}
[0.379270] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[0.379346] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[0.379471] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridgeConfig.cmake\n'}
[0.379601] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridgeConfig-version.cmake\n'}
[0.379688] (can_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.xml\n'}
[0.379762] (global_traj_generate) StdoutLine: {'line': b"Listing '/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate'...\n"}
[0.379954] (global_traj_generate) StdoutLine: {'line': b"Listing '/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg'...\n"}
[0.382155] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.382287] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.382683] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.383006] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_py.so\n'}
[0.383260] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/msg/LateralDeviation.idl\n'}
[0.383571] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/msg/LateralDeviation.msg\n'}
[0.383955] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/ament_index/resource_index/package_run_dependencies/global_traj_generate\n'}
[0.384086] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/ament_index/resource_index/parent_prefix_path/global_traj_generate\n'}
[0.384307] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/environment/ament_prefix_path.sh\n'}
[0.384364] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/environment/ament_prefix_path.dsv\n'}
[0.384529] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/environment/path.sh\n'}
[0.384571] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/environment/path.dsv\n'}
[0.384604] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/local_setup.bash\n'}
[0.384659] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/local_setup.sh\n'}
[0.384691] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/local_setup.zsh\n'}
[0.384722] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/local_setup.dsv\n'}
[0.384753] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.dsv\n'}
[0.384783] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/ament_index/resource_index/packages/global_traj_generate\n'}
[0.384814] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_cExport.cmake\n'}
[0.384845] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_cExport-noconfig.cmake\n'}
[0.384900] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[0.384932] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[0.384967] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_cppExport.cmake\n'}
[0.385000] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[0.385032] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[0.385216] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cExport.cmake\n'}
[0.385279] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[0.386159] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cExport.cmake\n'}
[0.386201] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cExport-noconfig.cmake\n'}
[0.386682] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cppExport.cmake\n'}
[0.386824] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[0.386856] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cppExport.cmake\n'}
[0.386887] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[0.387247] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_pyExport.cmake\n'}
[0.387596] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_pyExport-noconfig.cmake\n'}
[0.387733] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/rosidl_cmake-extras.cmake\n'}
[0.387826] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.387927] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[0.388013] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[0.388096] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_targets-extras.cmake\n'}
[0.388387] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[0.388547] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[0.388651] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generateConfig.cmake\n'}
[0.388743] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generateConfig-version.cmake\n'}
[0.388831] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.xml\n'}
[0.389933] (keyframe_msgs) StdoutLine: {'line': b"Listing '/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs'...\n"}
[0.390071] (keyframe_msgs) StdoutLine: {'line': b"Listing '/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/msg'...\n"}
[0.393075] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.393377] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.393673] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages/keyframe_msgs/keyframe_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[0.394046] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/libkeyframe_msgs__rosidl_generator_py.so\n'}
[0.394175] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/KeyFrame.idl\n'}
[0.394389] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/KeyFramePose.idl\n'}
[0.394572] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/KeyFramePoseArray.idl\n'}
[0.394732] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/VehicleControlFlags.idl\n'}
[0.394839] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/VehicleDiagnostic.idl\n'}
[0.394974] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/VehicleMotion.idl\n'}
[0.395065] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/JoystickState.idl\n'}
[0.395317] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/KeyState.idl\n'}
[0.395409] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/LateralDeviation.idl\n'}
[0.395542] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/KeyFrame.msg\n'}
[0.395647] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/KeyFramePose.msg\n'}
[0.395760] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/KeyFramePoseArray.msg\n'}
[0.395895] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/VehicleControlFlags.msg\n'}
[0.395978] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/VehicleDiagnostic.msg\n'}
[0.396066] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/VehicleMotion.msg\n'}
[0.396183] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/JoystickState.msg\n'}
[0.396294] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/KeyState.msg\n'}
[0.396399] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/msg/LateralDeviation.msg\n'}
[0.396735] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/ament_index/resource_index/package_run_dependencies/keyframe_msgs\n'}
[0.396849] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/ament_index/resource_index/parent_prefix_path/keyframe_msgs\n'}
[0.397098] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/environment/ament_prefix_path.sh\n'}
[0.397417] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/environment/ament_prefix_path.dsv\n'}
[0.397533] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/environment/path.sh\n'}
[0.397720] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/environment/path.dsv\n'}
[0.397957] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/local_setup.bash\n'}
[0.398075] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/local_setup.sh\n'}
[0.398367] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/local_setup.zsh\n'}
[0.398457] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/local_setup.dsv\n'}
[0.398866] (keyframe_msgs) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.dsv\n'}
[0.398990] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/ament_index/resource_index/packages/keyframe_msgs\n'}
[0.399260] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_generator_cExport.cmake\n'}
[0.399505] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_generator_cExport-noconfig.cmake\n'}
[0.399724] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[0.399892] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[0.400580] (-) TimerEvent: {}
[0.400937] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_generator_cppExport.cmake\n'}
[0.401192] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[0.401524] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[0.401869] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgs__rosidl_typesupport_introspection_cExport.cmake\n'}
[0.401992] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgs__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[0.402311] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgs__rosidl_typesupport_cExport.cmake\n'}
[0.402511] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgs__rosidl_typesupport_cExport-noconfig.cmake\n'}
[0.402728] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgs__rosidl_typesupport_introspection_cppExport.cmake\n'}
[0.402844] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgs__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[0.403037] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgs__rosidl_typesupport_cppExport.cmake\n'}
[0.403120] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgs__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[0.403330] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_generator_pyExport.cmake\n'}
[0.403595] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/export_keyframe_msgs__rosidl_generator_pyExport-noconfig.cmake\n'}
[0.403743] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/rosidl_cmake-extras.cmake\n'}
[0.403825] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.403903] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[0.403980] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[0.404056] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/ament_cmake_export_targets-extras.cmake\n'}
[0.404132] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[0.404452] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[0.404940] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgsConfig.cmake\n'}
[0.405030] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/cmake/keyframe_msgsConfig-version.cmake\n'}
[0.405109] (keyframe_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.xml\n'}
[0.405565] (remotecontrol_msgs) JobEnded: {'identifier': 'remotecontrol_msgs', 'rc': 0}
[0.406057] (can_bridge) CommandEnded: {'returncode': 0}
[0.420847] (can_bridge) JobEnded: {'identifier': 'can_bridge', 'rc': 0}
[0.421226] (global_traj_generate) CommandEnded: {'returncode': 0}
[0.430723] (global_traj_generate) JobEnded: {'identifier': 'global_traj_generate', 'rc': 0}
[0.431539] (keyframe_msgs) CommandEnded: {'returncode': 0}
[0.440233] (keyframe_msgs) JobEnded: {'identifier': 'keyframe_msgs', 'rc': 0}
[0.440719] (web) JobStarted: {'identifier': 'web'}
[0.456459] (web) JobProgress: {'identifier': 'web', 'progress': 'cmake'}
[0.457780] (web) JobProgress: {'identifier': 'web', 'progress': 'build'}
[0.458074] (web) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/xugong_web_ws/build/web', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/xugong_web_ws/build/web', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/web'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble')]), 'shell': False}
[0.500711] (-) TimerEvent: {}
[0.520970] (web) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target web_node\x1b[0m\n'}
[0.559601] (web) StdoutLine: {'line': b'[100%] Built target web_node\n'}
[0.568180] (web) CommandEnded: {'returncode': 0}
[0.569779] (web) JobProgress: {'identifier': 'web', 'progress': 'install'}
[0.570238] (web) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/xugong_web_ws/build/web'], 'cwd': '/home/<USER>/xugong_web_ws/build/web', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib:/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib:/home/<USER>/xugong_web_ws/install/global_traj_generate/lib:/home/<USER>/xugong_web_ws/install/can_bridge/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1789'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2330'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4188'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:15852'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '27228'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2076,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2076'), ('INVOCATION_ID', '41580637618642b68b3027b388467528'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-e56d24179671deb8.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dbea75e2d0.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/web:/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:9e2d69bb-7df1-4284-b535-d947fc68c718'), ('QT_ACCESSIBILITY', '1'), ('NO_AT_BRIDGE', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/xugong_web_ws/build/web'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/keyframe_msgs/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/global_traj_generate/local/lib/python3.10/dist-packages:/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble')]), 'shell': False}
[0.577975] (web) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.578972] (web) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/lib/web/web_node\n'}
[0.579121] (web) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/launch\n'}
[0.579200] (web) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/launch/web_launch.py\n'}
[0.579298] (web) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/package_run_dependencies/web\n'}
[0.579337] (web) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/parent_prefix_path/web\n'}
[0.579402] (web) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/environment/ament_prefix_path.sh\n'}
[0.579440] (web) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/environment/ament_prefix_path.dsv\n'}
[0.579475] (web) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/environment/path.sh\n'}
[0.579537] (web) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/environment/path.dsv\n'}
[0.579573] (web) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.bash\n'}
[0.579607] (web) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.sh\n'}
[0.579667] (web) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.zsh\n'}
[0.579700] (web) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/local_setup.dsv\n'}
[0.579732] (web) StdoutLine: {'line': b'-- Installing: /home/<USER>/xugong_web_ws/install/web/share/web/package.dsv\n'}
[0.579787] (web) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/ament_index/resource_index/packages/web\n'}
[0.579845] (web) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/cmake/webConfig.cmake\n'}
[0.579904] (web) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/cmake/webConfig-version.cmake\n'}
[0.579940] (web) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/xugong_web_ws/install/web/share/web/package.xml\n'}
[0.581074] (web) CommandEnded: {'returncode': 0}
[0.588788] (web) JobEnded: {'identifier': 'web', 'rc': 0}
[0.589219] (-) EventReactorShutdown: {}
