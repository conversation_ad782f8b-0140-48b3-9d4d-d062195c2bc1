[0.011s] Invoking command in '/home/<USER>/xugong_web_ws/build/keyframe_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/keyframe_msgs -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/keyframe_msgs
[0.106s] -- The C compiler identification is GNU 11.4.0
[0.174s] -- The CXX compiler identification is GNU 11.4.0
[0.181s] -- Detecting C compiler ABI info
[0.231s] -- Detecting C compiler ABI info - done
[0.239s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.239s] -- Detecting C compile features
[0.240s] -- Detecting C compile features - done
[0.242s] -- Detecting CXX compiler ABI info
[0.297s] -- Detecting CXX compiler AB<PERSON> info - done
[0.305s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.305s] -- Detecting CXX compile features
[0.306s] -- Detecting CXX compile features - done
[0.308s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.427s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.494s] -- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)
[0.523s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.528s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.535s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.545s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.558s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.578s] -- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[0.588s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.601s] -- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
[0.882s] -- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
[1.228s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.359s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.376s] -- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") 
[1.377s] -- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
[1.395s] -- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") 
[1.395s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[1.396s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
[1.396s] -- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so
[1.413s] -- Found PythonExtra: .so  
[1.467s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.480s] -- Added test 'lint_cmake' to check CMake code style
[1.480s] -- Added test 'xmllint' to check XML markup files
[1.481s] -- Configuring done
[1.513s] -- Generating done
[1.523s] -- Build files have been written to: /home/<USER>/xugong_web_ws/build/keyframe_msgs
[1.532s] Invoked command in '/home/<USER>/xugong_web_ws/build/keyframe_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/keyframe_msgs -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/keyframe_msgs
[1.534s] Invoking command in '/home/<USER>/xugong_web_ws/build/keyframe_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/keyframe_msgs -- -j16 -l16
[1.580s] [  1%] [34m[1mGenerating C code for ROS interfaces[0m
[1.589s] [  2%] [34m[1mGenerating C++ code for ROS interfaces[0m
[1.597s] [  2%] Built target ament_cmake_python_copy_keyframe_msgs
[1.811s] running egg_info
[1.812s] creating keyframe_msgs.egg-info
[1.812s] writing keyframe_msgs.egg-info/PKG-INFO
[1.812s] writing dependency_links to keyframe_msgs.egg-info/dependency_links.txt
[1.812s] writing top-level names to keyframe_msgs.egg-info/top_level.txt
[1.812s] writing manifest file 'keyframe_msgs.egg-info/SOURCES.txt'
[1.814s] reading manifest file 'keyframe_msgs.egg-info/SOURCES.txt'
[1.814s] writing manifest file 'keyframe_msgs.egg-info/SOURCES.txt'
[1.843s] [  2%] Built target ament_cmake_python_build_keyframe_msgs_egg
[3.035s] [  2%] Built target keyframe_msgs__cpp
[3.061s] [  3%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[3.062s] [  4%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[3.066s] [  5%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[3.085s] [  6%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/key_frame__functions.c.o[0m
[3.086s] [  7%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/key_frame_pose_array__functions.c.o[0m
[3.087s] [  8%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/key_frame_pose__functions.c.o[0m
[3.098s] [  9%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/vehicle_control_flags__functions.c.o[0m
[3.101s] [ 10%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/joystick_state__functions.c.o[0m
[3.109s] [ 12%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/vehicle_diagnostic__functions.c.o[0m
[3.110s] [ 12%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/vehicle_motion__functions.c.o[0m
[3.112s] [ 13%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/key_state__functions.c.o[0m
[3.113s] [ 14%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_generator_c.dir/rosidl_generator_c/keyframe_msgs/msg/detail/lateral_deviation__functions.c.o[0m
[3.242s] [ 15%] [32m[1mLinking C shared library libkeyframe_msgs__rosidl_generator_c.so[0m
[3.318s] [ 15%] Built target keyframe_msgs__rosidl_generator_c
[3.334s] [ 16%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[3.334s] [ 17%] [34m[1mGenerating C introspection for ROS interfaces[0m
[3.343s] [ 18%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
[3.891s] [ 20%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/vehicle_diagnostic__type_support.cpp.o[0m
[3.891s] [ 23%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/vehicle_control_flags__type_support.cpp.o[0m
[3.892s] [ 21%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/key_frame_pose_array__type_support.cpp.o[0m
[3.892s] [ 22%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/key_frame__type_support.cpp.o[0m
[3.893s] [ 24%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/key_frame_pose__type_support.cpp.o[0m
[3.893s] [ 26%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/vehicle_motion__type_support.cpp.o[0m
[3.895s] [ 25%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/joystick_state__type_support.cpp.o[0m
[3.895s] [ 27%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/key_state__type_support.cpp.o[0m
[3.904s] [ 28%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/keyframe_msgs/msg/lateral_deviation__type_support.cpp.o[0m
[4.086s] [ 29%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/key_frame_pose_array__type_support.cpp.o[0m
[4.087s] [ 30%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/key_frame_pose__type_support.cpp.o[0m
[4.088s] [ 31%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/key_frame__type_support.cpp.o[0m
[4.221s] [ 32%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/vehicle_control_flags__type_support.cpp.o[0m
[4.274s] [ 33%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/vehicle_diagnostic__type_support.cpp.o[0m
[4.302s] [ 34%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/vehicle_motion__type_support.cpp.o[0m
[4.332s] [ 35%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/joystick_state__type_support.cpp.o[0m
[4.351s] [ 36%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/key_state__type_support.cpp.o[0m
[4.421s] [ 37%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/keyframe_msgs/msg/detail/lateral_deviation__type_support.cpp.o[0m
[4.475s] [ 38%] [32m[1mLinking CXX shared library libkeyframe_msgs__rosidl_typesupport_cpp.so[0m
[4.587s] [ 38%] Built target keyframe_msgs__rosidl_typesupport_cpp
[4.590s] [ 40%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/key_frame__type_support.cpp.o[0m
[4.599s] [ 41%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/key_frame_pose_array__type_support.cpp.o[0m
[4.610s] [ 42%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/key_frame_pose__type_support.cpp.o[0m
[4.624s] [ 43%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/key_frame__type_support.c.o[0m
[4.635s] [ 44%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/vehicle_control_flags__type_support.cpp.o[0m
[4.640s] [ 45%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/key_frame_pose__type_support.c.o[0m
[4.647s] [ 46%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/vehicle_diagnostic__type_support.cpp.o[0m
[4.651s] [ 47%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/vehicle_control_flags__type_support.c.o[0m
[4.654s] [ 48%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/key_frame_pose_array__type_support.c.o[0m
[4.659s] [ 49%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/key_frame__type_support.cpp.o[0m
[4.662s] [ 50%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/vehicle_diagnostic__type_support.c.o[0m
[4.663s] [ 51%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/vehicle_motion__type_support.cpp.o[0m
[4.685s] [ 52%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/key_frame_pose__type_support.cpp.o[0m
[4.694s] [ 53%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/joystick_state__type_support.cpp.o[0m
[4.699s] [ 54%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/key_state__type_support.cpp.o[0m
[4.705s] [ 55%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/vehicle_motion__type_support.c.o[0m
[4.710s] [ 56%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/key_state__type_support.c.o[0m
[4.714s] [ 57%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/joystick_state__type_support.c.o[0m
[4.715s] [ 58%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/keyframe_msgs/msg/lateral_deviation__type_support.cpp.o[0m
[4.716s] [ 60%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/key_frame__type_support_c.cpp.o[0m
[4.723s] [ 61%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/key_frame_pose_array__type_support.cpp.o[0m
[4.743s] [ 62%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/vehicle_control_flags__type_support.cpp.o[0m
[4.743s] [ 63%] [32mBuilding C object CMakeFiles/keyframe_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/keyframe_msgs/msg/detail/lateral_deviation__type_support.c.o[0m
[4.754s] [ 64%] [32m[1mLinking CXX shared library libkeyframe_msgs__rosidl_typesupport_c.so[0m
[4.755s] [ 65%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/key_frame_pose__type_support_c.cpp.o[0m
[4.768s] [ 66%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/key_frame_pose_array__type_support_c.cpp.o[0m
[4.777s] [ 67%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/vehicle_diagnostic__type_support.cpp.o[0m
[4.827s] [ 68%] [32m[1mLinking C shared library libkeyframe_msgs__rosidl_typesupport_introspection_c.so[0m
[4.828s] [ 69%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/vehicle_motion__type_support.cpp.o[0m
[4.828s] [ 70%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/joystick_state__type_support.cpp.o[0m
[4.833s] [ 71%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/vehicle_control_flags__type_support_c.cpp.o[0m
[4.835s] [ 71%] Built target keyframe_msgs__rosidl_typesupport_c
[4.862s] [ 72%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/vehicle_diagnostic__type_support_c.cpp.o[0m
[4.886s] [ 72%] Built target keyframe_msgs__rosidl_typesupport_introspection_c
[4.919s] [ 73%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/vehicle_motion__type_support_c.cpp.o[0m
[4.936s] [ 74%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/key_state__type_support.cpp.o[0m
[4.974s] [ 75%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/joystick_state__type_support_c.cpp.o[0m
[5.153s] [ 76%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/key_state__type_support_c.cpp.o[0m
[5.157s] [ 77%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/keyframe_msgs/msg/detail/lateral_deviation__type_support_c.cpp.o[0m
[5.176s] [ 78%] [32mBuilding CXX object CMakeFiles/keyframe_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/keyframe_msgs/msg/detail/dds_fastrtps/lateral_deviation__type_support.cpp.o[0m
[5.200s] [ 80%] [32m[1mLinking CXX shared library libkeyframe_msgs__rosidl_typesupport_introspection_cpp.so[0m
[5.337s] [ 80%] Built target keyframe_msgs__rosidl_typesupport_introspection_cpp
[5.461s] [ 81%] [32m[1mLinking CXX shared library libkeyframe_msgs__rosidl_typesupport_fastrtps_c.so[0m
[5.514s] [ 82%] [32m[1mLinking CXX shared library libkeyframe_msgs__rosidl_typesupport_fastrtps_cpp.so[0m
[5.519s] [ 82%] Built target keyframe_msgs__rosidl_typesupport_fastrtps_c
[5.577s] [ 82%] Built target keyframe_msgs__rosidl_typesupport_fastrtps_cpp
[5.593s] [ 82%] Built target keyframe_msgs
[5.608s] [ 83%] [34m[1mGenerating Python code for ROS interfaces[0m
