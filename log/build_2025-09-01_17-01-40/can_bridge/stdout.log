-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") 
-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
-- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") 
-- Using PYTHON_EXECUTABLE: /usr/bin/python3
-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
-- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so
-- Found PythonExtra: .so  
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/xugong_web_ws/build/can_bridge
[  2%] [34m[1mGenerating C code for ROS interfaces[0m
[  4%] [34m[1mGenerating C++ code for ROS interfaces[0m
[  4%] Built target ament_cmake_python_copy_can_bridge
running egg_info
creating can_bridge.egg-info
writing can_bridge.egg-info/PKG-INFO
writing dependency_links to can_bridge.egg-info/dependency_links.txt
writing top-level names to can_bridge.egg-info/top_level.txt
writing manifest file 'can_bridge.egg-info/SOURCES.txt'
reading manifest file 'can_bridge.egg-info/SOURCES.txt'
writing manifest file 'can_bridge.egg-info/SOURCES.txt'
[  4%] Built target ament_cmake_python_build_can_bridge_egg
[  4%] Built target can_bridge__cpp
[  8%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_c.dir/rosidl_generator_c/can_bridge/msg/detail/vehicle_control_flags__functions.c.o[0m
[ 10%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_c.dir/rosidl_generator_c/can_bridge/msg/detail/vehicle_diagnostic__functions.c.o[0m
[ 10%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_c.dir/rosidl_generator_c/can_bridge/msg/detail/vehicle_motion__functions.c.o[0m
[ 12%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[ 14%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[ 17%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[ 19%] [32m[1mLinking C shared library libcan_bridge__rosidl_generator_c.so[0m
[ 19%] Built target can_bridge__rosidl_generator_c
[ 21%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[ 23%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
[ 25%] [34m[1mGenerating C introspection for ROS interfaces[0m
[ 27%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/can_bridge/msg/vehicle_control_flags__type_support.cpp.o[0m
[ 29%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/can_bridge/msg/vehicle_motion__type_support.cpp.o[0m
[ 31%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/can_bridge/msg/vehicle_diagnostic__type_support.cpp.o[0m
[ 34%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/can_bridge/msg/detail/vehicle_control_flags__type_support.cpp.o[0m
[ 36%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/can_bridge/msg/detail/vehicle_motion__type_support.cpp.o[0m
[ 38%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/can_bridge/msg/detail/vehicle_diagnostic__type_support.cpp.o[0m
[ 40%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/can_bridge/msg/detail/dds_fastrtps/vehicle_motion__type_support.cpp.o[0m
[ 42%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/can_bridge/msg/detail/dds_fastrtps/vehicle_diagnostic__type_support.cpp.o[0m
[ 44%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/can_bridge/msg/detail/dds_fastrtps/vehicle_control_flags__type_support.cpp.o[0m
[ 48%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_c.dir/rosidl_typesupport_c/can_bridge/msg/vehicle_control_flags__type_support.cpp.o[0m
[ 48%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_c.dir/rosidl_typesupport_c/can_bridge/msg/vehicle_diagnostic__type_support.cpp.o[0m
[ 51%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_c.dir/rosidl_typesupport_c/can_bridge/msg/vehicle_motion__type_support.cpp.o[0m
[ 53%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/can_bridge/msg/detail/vehicle_control_flags__type_support_c.cpp.o[0m
[ 55%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/can_bridge/msg/detail/vehicle_diagnostic__type_support_c.cpp.o[0m
[ 57%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/can_bridge/msg/detail/vehicle_motion__type_support_c.cpp.o[0m
[ 59%] [32m[1mLinking CXX shared library libcan_bridge__rosidl_typesupport_c.so[0m
[ 61%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/can_bridge/msg/detail/vehicle_control_flags__type_support.c.o[0m
[ 63%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/can_bridge/msg/detail/vehicle_motion__type_support.c.o[0m
[ 65%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/can_bridge/msg/detail/vehicle_diagnostic__type_support.c.o[0m
[ 65%] Built target can_bridge__rosidl_typesupport_c
[ 68%] [32m[1mLinking C shared library libcan_bridge__rosidl_typesupport_introspection_c.so[0m
[ 68%] Built target can_bridge__rosidl_typesupport_introspection_c
[ 70%] [32m[1mLinking CXX shared library libcan_bridge__rosidl_typesupport_cpp.so[0m
[ 70%] Built target can_bridge__rosidl_typesupport_cpp
[ 72%] [32m[1mLinking CXX shared library libcan_bridge__rosidl_typesupport_introspection_cpp.so[0m
[ 74%] [32m[1mLinking CXX shared library libcan_bridge__rosidl_typesupport_fastrtps_c.so[0m
[ 74%] Built target can_bridge__rosidl_typesupport_introspection_cpp
[ 76%] [32m[1mLinking CXX shared library libcan_bridge__rosidl_typesupport_fastrtps_cpp.so[0m
[ 76%] Built target can_bridge__rosidl_typesupport_fastrtps_c
[ 76%] Built target can_bridge__rosidl_typesupport_fastrtps_cpp
[ 76%] Built target can_bridge
[ 78%] [34m[1mGenerating Python code for ROS interfaces[0m
[ 78%] Built target can_bridge__py
[ 80%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_py.dir/rosidl_generator_py/can_bridge/msg/_vehicle_diagnostic_s.c.o[0m
[ 82%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_py.dir/rosidl_generator_py/can_bridge/msg/_vehicle_motion_s.c.o[0m
[ 85%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_py.dir/rosidl_generator_py/can_bridge/msg/_vehicle_control_flags_s.c.o[0m
[ 87%] [32m[1mLinking C shared library rosidl_generator_py/can_bridge/libcan_bridge__rosidl_generator_py.so[0m
[ 87%] Built target can_bridge__rosidl_generator_py
[ 89%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_c__pyext.dir/rosidl_generator_py/can_bridge/_can_bridge_s.ep.rosidl_typesupport_fastrtps_c.c.o[0m
[ 91%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_introspection_c__pyext.dir/rosidl_generator_py/can_bridge/_can_bridge_s.ep.rosidl_typesupport_introspection_c.c.o[0m
[ 93%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_c__pyext.dir/rosidl_generator_py/can_bridge/_can_bridge_s.ep.rosidl_typesupport_c.c.o[0m
[ 95%] [32m[1mLinking C shared library rosidl_generator_py/can_bridge/can_bridge_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so[0m
[ 97%] [32m[1mLinking C shared library rosidl_generator_py/can_bridge/can_bridge_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so[0m
[100%] [32m[1mLinking C shared library rosidl_generator_py/can_bridge/can_bridge_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so[0m
[100%] Built target can_bridge__rosidl_typesupport_c__pyext
[100%] Built target can_bridge__rosidl_typesupport_fastrtps_c__pyext
[100%] Built target can_bridge__rosidl_typesupport_introspection_c__pyext
-- Install configuration: ""
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/rosidl_interfaces/can_bridge
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_diagnostic.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_generator_c__visibility_control.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_motion.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_control_flags.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__functions.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__struct.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__functions.c
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__functions.c
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__functions.c
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__struct.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__functions.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__functions.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__struct.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/library_path.sh
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/library_path.dsv
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_generator_c.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_generator_c.so" to ""
-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_fastrtps_c.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_fastrtps_c.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_fastrtps_c.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_fastrtps_c.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_fastrtps_c.so" to ""
-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_control_flags.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_generator_cpp__visibility_control.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_diagnostic.hpp
-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__traits.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__traits.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__traits.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__struct.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__builder.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__builder.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__builder.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__struct.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__struct.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_motion.hpp
-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/dds_fastrtps
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_fastrtps_cpp.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_fastrtps_cpp.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_fastrtps_cpp.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_fastrtps_cpp.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_fastrtps_cpp.so" to ""
-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_typesupport_introspection_c__visibility_control.h
-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_introspection_c.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_introspection_c.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_introspection_c.h
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.c
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.c
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.c
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_introspection_c.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_introspection_c.so" to ""
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_c.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_c.so" to ""
-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
-- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.cpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_introspection_cpp.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.cpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_introspection_cpp.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.cpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_introspection_cpp.hpp
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_introspection_cpp.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_introspection_cpp.so" to ""
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_cpp.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_cpp.so" to ""
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/pythonpath.sh
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/pythonpath.dsv
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/dependency_links.txt
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/SOURCES.txt
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/top_level.txt
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/PKG-INFO
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/_can_bridge_s.ep.rosidl_typesupport_c.c
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/_can_bridge_s.ep.rosidl_typesupport_fastrtps_c.c
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_diagnostic_s.c
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_motion_s.c
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_motion.py
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_diagnostic.py
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_control_flags_s.c
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_control_flags.py
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/__init__.py
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/__init__.py
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/libcan_bridge__rosidl_generator_py.so
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/_can_bridge_s.ep.rosidl_typesupport_introspection_c.c
Listing '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge'...
Compiling '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/__init__.py'...
Listing '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg'...
Compiling '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/__init__.py'...
Compiling '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_control_flags.py'...
Compiling '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_diagnostic.py'...
Compiling '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_motion.py'...
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so" to ""
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so" to ""
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so" to ""
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_generator_py.so
-- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_generator_py.so" to ""
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleControlFlags.idl
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleDiagnostic.idl
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleMotion.idl
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleControlFlags.msg
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleDiagnostic.msg
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleMotion.msg
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/package_run_dependencies/can_bridge
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/parent_prefix_path/can_bridge
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/path.sh
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/path.dsv
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.bash
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.sh
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.zsh
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.dsv
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.dsv
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/packages/can_bridge
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_cExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_cExport-noconfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cExport-noconfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_cppExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cppExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cExport-noconfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cExport-noconfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cppExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cppExport-noconfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cppExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cppExport-noconfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_pyExport.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_pyExport-noconfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/rosidl_cmake-extras.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_dependencies-extras.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_include_directories-extras.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_libraries-extras.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_targets-extras.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridgeConfig.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridgeConfig-version.cmake
-- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.xml
