-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") 
-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
-- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") 
-- Using PYTHON_EXECUTABLE: /usr/bin/python3
-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
-- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so
-- Found PythonExtra: .so  
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/xugong_web_ws/build/global_traj_generate
[  3%] [34m[1mGenerating C code for ROS interfaces[0m
[  6%] [34m[1mGenerating C++ code for ROS interfaces[0m
[  6%] Built target ament_cmake_python_copy_global_traj_generate
[  9%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_generator_c.dir/rosidl_generator_c/global_traj_generate/msg/detail/lateral_deviation__functions.c.o[0m
running egg_info
creating global_traj_generate.egg-info
writing global_traj_generate.egg-info/PKG-INFO
writing dependency_links to global_traj_generate.egg-info/dependency_links.txt
writing top-level names to global_traj_generate.egg-info/top_level.txt
writing manifest file 'global_traj_generate.egg-info/SOURCES.txt'
reading manifest file 'global_traj_generate.egg-info/SOURCES.txt'
writing manifest file 'global_traj_generate.egg-info/SOURCES.txt'
[ 12%] [32m[1mLinking C shared library libglobal_traj_generate__rosidl_generator_c.so[0m
[ 12%] Built target global_traj_generate__cpp
[ 16%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[ 16%] Built target ament_cmake_python_build_global_traj_generate_egg
[ 19%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[ 22%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[ 22%] Built target global_traj_generate__rosidl_generator_c
[ 25%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[ 29%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
[ 32%] [34m[1mGenerating C introspection for ROS interfaces[0m
[ 35%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/global_traj_generate/msg/detail/lateral_deviation__type_support_c.cpp.o[0m
[ 38%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/global_traj_generate/msg/lateral_deviation__type_support.cpp.o[0m
[ 41%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_c.dir/rosidl_typesupport_c/global_traj_generate/msg/lateral_deviation__type_support.cpp.o[0m
[ 45%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/global_traj_generate/msg/detail/lateral_deviation__type_support.c.o[0m
[ 48%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/global_traj_generate/msg/detail/lateral_deviation__type_support.cpp.o[0m
[ 51%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/global_traj_generate/msg/detail/dds_fastrtps/lateral_deviation__type_support.cpp.o[0m
[ 54%] [32m[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_c.so[0m
[ 58%] [32m[1mLinking C shared library libglobal_traj_generate__rosidl_typesupport_introspection_c.so[0m
[ 58%] Built target global_traj_generate__rosidl_typesupport_c
[ 58%] Built target global_traj_generate__rosidl_typesupport_introspection_c
