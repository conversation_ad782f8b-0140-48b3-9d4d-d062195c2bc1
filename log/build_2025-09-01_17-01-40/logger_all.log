[0.069s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.069s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x78039f539b10>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x78039f5396c0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x78039f5396c0>>)
[0.169s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.169s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.169s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.169s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.169s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.169s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.169s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/xugong_web_ws'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extensions ['ignore', 'ignore_ament_install']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extension 'ignore'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extension 'ignore_ament_install'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extensions ['colcon_pkg']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extension 'colcon_pkg'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extensions ['colcon_meta']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extension 'colcon_meta'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extensions ['ros']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/can_bridge) by extension 'ros'
[0.180s] DEBUG:colcon.colcon_core.package_identification:Package 'src/can_bridge' with type 'ros.ament_cmake' and name 'can_bridge'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extensions ['ignore', 'ignore_ament_install']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extension 'ignore'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extension 'ignore_ament_install'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extensions ['colcon_pkg']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extension 'colcon_pkg'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extensions ['colcon_meta']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extension 'colcon_meta'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extensions ['ros']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extension 'ros'
[0.181s] DEBUG:colcon.colcon_core.package_identification:Package 'src/global_traj_generate' with type 'ros.ament_cmake' and name 'global_traj_generate'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extension 'ignore'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extension 'ignore_ament_install'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extensions ['colcon_pkg']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extension 'colcon_pkg'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extensions ['colcon_meta']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extension 'colcon_meta'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extensions ['ros']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/keyframe_msgs) by extension 'ros'
[0.182s] DEBUG:colcon.colcon_core.package_identification:Package 'src/keyframe_msgs' with type 'ros.ament_cmake' and name 'keyframe_msgs'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extension 'ignore'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extension 'ignore_ament_install'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extensions ['colcon_pkg']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extension 'colcon_pkg'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extensions ['colcon_meta']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extension 'colcon_meta'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extensions ['ros']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/remotecontrol_msgs) by extension 'ros'
[0.183s] DEBUG:colcon.colcon_core.package_identification:Package 'src/remotecontrol_msgs' with type 'ros.ament_cmake' and name 'remotecontrol_msgs'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extensions ['ignore', 'ignore_ament_install']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extension 'ignore'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extension 'ignore_ament_install'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extensions ['colcon_pkg']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extension 'colcon_pkg'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extensions ['colcon_meta']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extension 'colcon_meta'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extensions ['ros']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/web) by extension 'ros'
[0.184s] DEBUG:colcon.colcon_core.package_identification:Package 'src/web' with type 'ros.ament_cmake' and name 'web'
[0.184s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.184s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.184s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.184s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.184s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.203s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.203s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.203s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/xugong_web_ws/install/web' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.203s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.203s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/xugong_web_ws/install/keyframe_msgs' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.203s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/xugong_web_ws/install/global_traj_generate' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.204s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/xugong_web_ws/install/can_bridge' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.204s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/xugong_web_ws/install/web' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.204s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.204s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/xugong_web_ws/install/keyframe_msgs' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.204s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/xugong_web_ws/install/global_traj_generate' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.204s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/xugong_web_ws/install/can_bridge' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.204s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 0 installed packages in /home/<USER>/xugong_web_ws/install
[0.205s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 473 installed packages in /opt/ros/humble
[0.207s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.239s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'cmake_args' from command line to 'None'
[0.239s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'cmake_target' from command line to 'None'
[0.239s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.239s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'cmake_clean_cache' from command line to 'False'
[0.239s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'cmake_clean_first' from command line to 'False'
[0.239s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'cmake_force_configure' from command line to 'False'
[0.239s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'ament_cmake_args' from command line to 'None'
[0.239s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'catkin_cmake_args' from command line to 'None'
[0.239s] Level 5:colcon.colcon_core.verb:set package 'can_bridge' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.239s] DEBUG:colcon.colcon_core.verb:Building package 'can_bridge' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/xugong_web_ws/build/can_bridge', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/xugong_web_ws/install/can_bridge', 'merge_install': False, 'path': '/home/<USER>/xugong_web_ws/src/can_bridge', 'symlink_install': False, 'test_result_base': None}
[0.240s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_args' from command line to 'None'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_target' from command line to 'None'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_clean_cache' from command line to 'False'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_clean_first' from command line to 'False'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_force_configure' from command line to 'False'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'ament_cmake_args' from command line to 'None'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'catkin_cmake_args' from command line to 'None'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.240s] DEBUG:colcon.colcon_core.verb:Building package 'global_traj_generate' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/xugong_web_ws/build/global_traj_generate', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/xugong_web_ws/install/global_traj_generate', 'merge_install': False, 'path': '/home/<USER>/xugong_web_ws/src/global_traj_generate', 'symlink_install': False, 'test_result_base': None}
[0.240s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'cmake_args' from command line to 'None'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'cmake_target' from command line to 'None'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'keyframe_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.240s] DEBUG:colcon.colcon_core.verb:Building package 'keyframe_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/xugong_web_ws/build/keyframe_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/xugong_web_ws/install/keyframe_msgs', 'merge_install': False, 'path': '/home/<USER>/xugong_web_ws/src/keyframe_msgs', 'symlink_install': False, 'test_result_base': None}
[0.241s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'cmake_args' from command line to 'None'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'cmake_target' from command line to 'None'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'remotecontrol_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.241s] DEBUG:colcon.colcon_core.verb:Building package 'remotecontrol_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs', 'merge_install': False, 'path': '/home/<USER>/xugong_web_ws/src/remotecontrol_msgs', 'symlink_install': False, 'test_result_base': None}
[0.241s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'cmake_args' from command line to 'None'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'cmake_target' from command line to 'None'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'cmake_clean_cache' from command line to 'False'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'cmake_clean_first' from command line to 'False'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'cmake_force_configure' from command line to 'False'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'ament_cmake_args' from command line to 'None'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'catkin_cmake_args' from command line to 'None'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'web' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.241s] DEBUG:colcon.colcon_core.verb:Building package 'web' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/xugong_web_ws/build/web', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/xugong_web_ws/install/web', 'merge_install': False, 'path': '/home/<USER>/xugong_web_ws/src/web', 'symlink_install': False, 'test_result_base': None}
[0.241s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.242s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.242s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/xugong_web_ws/src/can_bridge' with build type 'ament_cmake'
[0.243s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/xugong_web_ws/src/can_bridge'
[0.244s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.244s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.244s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.247s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/xugong_web_ws/src/global_traj_generate' with build type 'ament_cmake'
[0.247s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/xugong_web_ws/src/global_traj_generate'
[0.247s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.247s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.249s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/xugong_web_ws/src/keyframe_msgs' with build type 'ament_cmake'
[0.250s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/xugong_web_ws/src/keyframe_msgs'
[0.250s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.250s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.252s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/xugong_web_ws/src/remotecontrol_msgs' with build type 'ament_cmake'
[0.252s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/xugong_web_ws/src/remotecontrol_msgs'
[0.252s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.252s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.258s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/can_bridge': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/can_bridge -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/can_bridge
[0.260s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/global_traj_generate': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/global_traj_generate -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/global_traj_generate
[0.261s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/keyframe_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/keyframe_msgs -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/keyframe_msgs
[0.264s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/remotecontrol_msgs -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs
[1.748s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/global_traj_generate' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/global_traj_generate -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/global_traj_generate
[1.750s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/global_traj_generate': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/global_traj_generate -- -j16 -l16
[1.750s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/remotecontrol_msgs -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs
[1.751s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/remotecontrol_msgs -- -j16 -l16
[1.758s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/can_bridge' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/can_bridge -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/can_bridge
[1.759s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/can_bridge': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/can_bridge -- -j16 -l16
[1.782s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/keyframe_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/keyframe_msgs -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/keyframe_msgs
[1.784s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/keyframe_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/keyframe_msgs -- -j16 -l16
[4.601s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/global_traj_generate' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/global_traj_generate -- -j16 -l16
[4.623s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/global_traj_generate': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/global_traj_generate
[4.710s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(global_traj_generate)
[4.715s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/global_traj_generate' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/global_traj_generate
[4.717s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate' for CMake module files
[4.718s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate' for CMake config files
[4.718s] Level 1:colcon.colcon_core.shell:create_environment_hook('global_traj_generate', 'cmake_prefix_path')
[4.718s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.ps1'
[4.723s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.dsv'
[4.725s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.sh'
[4.727s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/lib'
[4.727s] Level 1:colcon.colcon_core.shell:create_environment_hook('global_traj_generate', 'ld_library_path_lib')
[4.728s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.ps1'
[4.728s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.dsv'
[4.729s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.sh'
[4.729s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/bin'
[4.729s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/lib/pkgconfig/global_traj_generate.pc'
[4.729s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/lib/python3.10/site-packages'
[4.729s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/bin'
[4.730s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.ps1'
[4.735s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.dsv'
[4.735s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.sh'
[4.736s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.bash'
[4.737s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.zsh'
[4.738s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/global_traj_generate/share/colcon-core/packages/global_traj_generate)
[4.739s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(global_traj_generate)
[4.739s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate' for CMake module files
[4.740s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate' for CMake config files
[4.740s] Level 1:colcon.colcon_core.shell:create_environment_hook('global_traj_generate', 'cmake_prefix_path')
[4.740s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.ps1'
[4.741s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.dsv'
[4.741s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.sh'
[4.743s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/lib'
[4.743s] Level 1:colcon.colcon_core.shell:create_environment_hook('global_traj_generate', 'ld_library_path_lib')
[4.743s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.ps1'
[4.744s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.dsv'
[4.748s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.sh'
[4.749s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/bin'
[4.749s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/lib/pkgconfig/global_traj_generate.pc'
[4.749s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/lib/python3.10/site-packages'
[4.749s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/global_traj_generate/bin'
[4.750s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.ps1'
[4.751s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.dsv'
[4.752s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.sh'
[4.754s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.bash'
[4.755s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/global_traj_generate/share/global_traj_generate/package.zsh'
[4.758s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/global_traj_generate/share/colcon-core/packages/global_traj_generate)
[5.040s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/can_bridge' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/can_bridge -- -j16 -l16
[5.077s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/can_bridge': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/can_bridge
[5.158s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(can_bridge)
[5.159s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge' for CMake module files
[5.159s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge' for CMake config files
[5.160s] Level 1:colcon.colcon_core.shell:create_environment_hook('can_bridge', 'cmake_prefix_path')
[5.160s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/cmake_prefix_path.ps1'
[5.161s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/cmake_prefix_path.dsv'
[5.161s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/cmake_prefix_path.sh'
[5.162s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/can_bridge' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/can_bridge
[5.163s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/lib'
[5.163s] Level 1:colcon.colcon_core.shell:create_environment_hook('can_bridge', 'ld_library_path_lib')
[5.163s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/ld_library_path_lib.ps1'
[5.163s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/ld_library_path_lib.dsv'
[5.164s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/ld_library_path_lib.sh'
[5.164s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/bin'
[5.164s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/lib/pkgconfig/can_bridge.pc'
[5.164s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/lib/python3.10/site-packages'
[5.165s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/bin'
[5.165s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.ps1'
[5.166s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.dsv'
[5.166s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.sh'
[5.166s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.bash'
[5.167s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.zsh'
[5.167s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/can_bridge/share/colcon-core/packages/can_bridge)
[5.168s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(can_bridge)
[5.168s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge' for CMake module files
[5.169s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge' for CMake config files
[5.169s] Level 1:colcon.colcon_core.shell:create_environment_hook('can_bridge', 'cmake_prefix_path')
[5.169s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/cmake_prefix_path.ps1'
[5.170s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/cmake_prefix_path.dsv'
[5.170s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/cmake_prefix_path.sh'
[5.171s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/lib'
[5.171s] Level 1:colcon.colcon_core.shell:create_environment_hook('can_bridge', 'ld_library_path_lib')
[5.171s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/ld_library_path_lib.ps1'
[5.171s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/ld_library_path_lib.dsv'
[5.172s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/hook/ld_library_path_lib.sh'
[5.173s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/bin'
[5.173s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/lib/pkgconfig/can_bridge.pc'
[5.173s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/lib/python3.10/site-packages'
[5.174s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/can_bridge/bin'
[5.174s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.ps1'
[5.175s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.dsv'
[5.175s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.sh'
[5.176s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.bash'
[5.176s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.zsh'
[5.177s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/can_bridge/share/colcon-core/packages/can_bridge)
[5.851s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/remotecontrol_msgs -- -j16 -l16
[5.853s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/remotecontrol_msgs
[5.905s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(remotecontrol_msgs)
[5.905s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs' for CMake module files
[5.906s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/remotecontrol_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/remotecontrol_msgs
[5.906s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs' for CMake config files
[5.906s] Level 1:colcon.colcon_core.shell:create_environment_hook('remotecontrol_msgs', 'cmake_prefix_path')
[5.906s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/cmake_prefix_path.ps1'
[5.907s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/cmake_prefix_path.dsv'
[5.907s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/cmake_prefix_path.sh'
[5.907s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib'
[5.907s] Level 1:colcon.colcon_core.shell:create_environment_hook('remotecontrol_msgs', 'ld_library_path_lib')
[5.908s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/ld_library_path_lib.ps1'
[5.908s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/ld_library_path_lib.dsv'
[5.908s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/ld_library_path_lib.sh'
[5.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/bin'
[5.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/pkgconfig/remotecontrol_msgs.pc'
[5.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/python3.10/site-packages'
[5.909s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/bin'
[5.909s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.ps1'
[5.909s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.dsv'
[5.909s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.sh'
[5.910s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.bash'
[5.910s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.zsh'
[5.910s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/colcon-core/packages/remotecontrol_msgs)
[5.910s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(remotecontrol_msgs)
[5.910s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs' for CMake module files
[5.911s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs' for CMake config files
[5.911s] Level 1:colcon.colcon_core.shell:create_environment_hook('remotecontrol_msgs', 'cmake_prefix_path')
[5.911s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/cmake_prefix_path.ps1'
[5.911s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/cmake_prefix_path.dsv'
[5.912s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/cmake_prefix_path.sh'
[5.912s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib'
[5.912s] Level 1:colcon.colcon_core.shell:create_environment_hook('remotecontrol_msgs', 'ld_library_path_lib')
[5.912s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/ld_library_path_lib.ps1'
[5.912s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/ld_library_path_lib.dsv'
[5.912s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/hook/ld_library_path_lib.sh'
[5.913s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/bin'
[5.913s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/pkgconfig/remotecontrol_msgs.pc'
[5.913s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/lib/python3.10/site-packages'
[5.913s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/bin'
[5.913s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.ps1'
[5.914s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.dsv'
[5.914s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.sh'
[5.914s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.bash'
[5.914s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/remotecontrol_msgs/package.zsh'
[5.915s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/remotecontrol_msgs/share/colcon-core/packages/remotecontrol_msgs)
[7.249s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/keyframe_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/keyframe_msgs -- -j16 -l16
[7.251s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/keyframe_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/keyframe_msgs
[7.326s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(keyframe_msgs)
[7.327s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs' for CMake module files
[7.327s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/keyframe_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/keyframe_msgs
[7.327s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs' for CMake config files
[7.327s] Level 1:colcon.colcon_core.shell:create_environment_hook('keyframe_msgs', 'cmake_prefix_path')
[7.328s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/cmake_prefix_path.ps1'
[7.328s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/cmake_prefix_path.dsv'
[7.328s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/cmake_prefix_path.sh'
[7.328s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib'
[7.329s] Level 1:colcon.colcon_core.shell:create_environment_hook('keyframe_msgs', 'ld_library_path_lib')
[7.329s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/ld_library_path_lib.ps1'
[7.329s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/ld_library_path_lib.dsv'
[7.329s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/ld_library_path_lib.sh'
[7.329s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/bin'
[7.329s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/pkgconfig/keyframe_msgs.pc'
[7.329s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/python3.10/site-packages'
[7.330s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/bin'
[7.330s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.ps1'
[7.330s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.dsv'
[7.330s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.sh'
[7.331s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.bash'
[7.331s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.zsh'
[7.331s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/colcon-core/packages/keyframe_msgs)
[7.331s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(keyframe_msgs)
[7.331s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs' for CMake module files
[7.332s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs' for CMake config files
[7.332s] Level 1:colcon.colcon_core.shell:create_environment_hook('keyframe_msgs', 'cmake_prefix_path')
[7.332s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/cmake_prefix_path.ps1'
[7.332s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/cmake_prefix_path.dsv'
[7.332s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/cmake_prefix_path.sh'
[7.333s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib'
[7.333s] Level 1:colcon.colcon_core.shell:create_environment_hook('keyframe_msgs', 'ld_library_path_lib')
[7.333s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/ld_library_path_lib.ps1'
[7.333s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/ld_library_path_lib.dsv'
[7.333s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/hook/ld_library_path_lib.sh'
[7.334s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/bin'
[7.334s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/pkgconfig/keyframe_msgs.pc'
[7.334s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/lib/python3.10/site-packages'
[7.334s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/keyframe_msgs/bin'
[7.334s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.ps1'
[7.335s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.dsv'
[7.335s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.sh'
[7.335s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.bash'
[7.335s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/keyframe_msgs/package.zsh'
[7.336s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/keyframe_msgs/share/colcon-core/packages/keyframe_msgs)
[7.336s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/xugong_web_ws/src/web' with build type 'ament_cmake'
[7.336s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/xugong_web_ws/src/web'
[7.336s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[7.336s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[7.350s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/web': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/web -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/web
[9.961s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/web' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/web -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/web
[9.961s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/web': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/web -- -j16 -l16
[68.148s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/web' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/web -- -j16 -l16
[68.152s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/xugong_web_ws/build/web': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/web
[68.196s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(web)
[68.196s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web' for CMake module files
[68.197s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/xugong_web_ws/build/web' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/xugong_web_ws/install/remotecontrol_msgs:/home/<USER>/xugong_web_ws/install/keyframe_msgs:/home/<USER>/xugong_web_ws/install/global_traj_generate:/home/<USER>/xugong_web_ws/install/can_bridge:/home/<USER>/xugong_web_ws/install/web:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/web
[68.197s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web' for CMake config files
[68.197s] Level 1:colcon.colcon_core.shell:create_environment_hook('web', 'cmake_prefix_path')
[68.198s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/web/share/web/hook/cmake_prefix_path.ps1'
[68.198s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/web/share/web/hook/cmake_prefix_path.dsv'
[68.199s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/web/share/web/hook/cmake_prefix_path.sh'
[68.199s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/lib'
[68.199s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/bin'
[68.199s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/lib/pkgconfig/web.pc'
[68.199s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/lib/python3.10/site-packages'
[68.200s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/bin'
[68.200s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/web/share/web/package.ps1'
[68.200s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/web/share/web/package.dsv'
[68.200s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/web/share/web/package.sh'
[68.201s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/web/share/web/package.bash'
[68.201s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/web/share/web/package.zsh'
[68.201s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/web/share/colcon-core/packages/web)
[68.201s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(web)
[68.202s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web' for CMake module files
[68.202s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web' for CMake config files
[68.202s] Level 1:colcon.colcon_core.shell:create_environment_hook('web', 'cmake_prefix_path')
[68.202s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/web/share/web/hook/cmake_prefix_path.ps1'
[68.202s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/xugong_web_ws/install/web/share/web/hook/cmake_prefix_path.dsv'
[68.203s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/xugong_web_ws/install/web/share/web/hook/cmake_prefix_path.sh'
[68.203s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/lib'
[68.203s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/bin'
[68.203s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/lib/pkgconfig/web.pc'
[68.203s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/lib/python3.10/site-packages'
[68.203s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/xugong_web_ws/install/web/bin'
[68.204s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/web/share/web/package.ps1'
[68.204s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/xugong_web_ws/install/web/share/web/package.dsv'
[68.204s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/web/share/web/package.sh'
[68.204s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/web/share/web/package.bash'
[68.205s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/xugong_web_ws/install/web/share/web/package.zsh'
[68.205s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/xugong_web_ws/install/web/share/colcon-core/packages/web)
[68.205s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[68.205s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[68.205s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[68.205s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[68.210s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[68.210s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[68.210s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[68.221s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[68.221s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/xugong_web_ws/install/local_setup.ps1'
[68.223s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/xugong_web_ws/install/_local_setup_util_ps1.py'
[68.224s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/xugong_web_ws/install/setup.ps1'
[68.225s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/xugong_web_ws/install/local_setup.sh'
[68.225s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/xugong_web_ws/install/_local_setup_util_sh.py'
[68.226s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/xugong_web_ws/install/setup.sh'
[68.227s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/xugong_web_ws/install/local_setup.bash'
[68.227s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/xugong_web_ws/install/setup.bash'
[68.228s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/xugong_web_ws/install/local_setup.zsh'
[68.228s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/xugong_web_ws/install/setup.zsh'
