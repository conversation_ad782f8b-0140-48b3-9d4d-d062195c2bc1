#include <rclcpp/rclcpp.hpp>
#include "websocket_client.h"
#include "websocket_server.h"
#include "map_server.h"
#include "file_update.h"
#include "file_download.h"
#include <nlohmann/json.hpp>
#include <iostream>
#include <string>
#include <yaml-cpp/yaml.h>
#include "common.h"

using json = nlohmann::json; // 简化命名

struct WebNodeConfig {
    std::string url;
    int port;
    std::string sn;
    std::string area_id;
};

WebNodeConfig loadWebNodeConfig(const std::string& yaml_file) {
    WebNodeConfig config;
    YAML::Node root = YAML::LoadFile(yaml_file);

    if (root["web_node"]) {
        auto node = root["web_node"];
        config.url = node["url"].as<std::string>();
        config.port = node["port"].as<int>();
        config.sn = node["sn"].as<std::string>();
        config.area_id = node["area_id"].as<std::string>();
    } else {
        throw std::runtime_error("Missing 'web_node' section in YAML file");
    }
    return config;
}

int startNrFlag = 0;
int main(int argc, char** argv) {
    // 初始化 ROS 2
    rclcpp::init(argc, argv);

    auto node = rclcpp::Node::make_shared("web_node");

    WebNodeConfig cfg = loadWebNodeConfig(params_yaml_path);

    std::cout << "URL: " << cfg.url << std::endl;
    std::cout << "Port: " << cfg.port << std::endl;
    std::cout << "SN: " << cfg.sn << std::endl;
    std::cout << "Area ID: " << cfg.area_id << std::endl;

    cfg.url = cfg.url + cfg.sn;
    RCLCPP_INFO(node->get_logger(), "ws = %s", cfg.url.c_str());

    //加载地图管理
    MapManager::getInstance().set_path(LOCAL_MAPS_DIR);
    if (MapManager::getInstance().loadManagerInfo()) {
        RCLCPP_INFO(node->get_logger(), "Map get currentloadManagerInfo ok");
    }
    else
    {
        RCLCPP_INFO(node->get_logger(), "Map get currentloadManagerInfo failed");
    }
    std::string current_serial_number;
    std::string current_map_id;
    if (MapManager::getInstance().getCurrentMap(current_serial_number, current_map_id)) {
        RCLCPP_INFO(node->get_logger(), "Map get current map ok current_serial_number=%s current_map_id=%s", current_serial_number.c_str(), current_map_id.c_str());
        WebSocketClientSingleton::getInstance().set_map_id(current_map_id);
        WebSocketServerSingleton::getInstance(cfg.port).set_map_id(current_map_id);
    }
    else
    {
        RCLCPP_INFO(node->get_logger(), "Map get current map failed");
    }

    // 初始化 WebSocket 客户端
    WebSocketClientSingleton::getInstance().set_node(node);
    WebSocketClientSingleton::getInstance().set_sn(cfg.sn);
    WebSocketClientSingleton::getInstance().set_area_id(cfg.area_id);

    // 初始化 WebSocket 服务端
    WebSocketServerSingleton::getInstance(cfg.port).set_node(node);
    WebSocketServerSingleton::getInstance(cfg.port).set_sn(cfg.sn);
    WebSocketServerSingleton::getInstance(cfg.port).set_area_id(cfg.area_id);

#if XG_CHANXUECHE
    WebSocketServerSingleton::getInstance(cfg.port).delete_cloud_map_files(LOCAL_MAPS_DIR);
    WebSocketServerSingleton::getInstance(cfg.port).start();
#else
    WebSocketClientSingleton::getInstance().connect(cfg.url);
#endif

    rclcpp::WallRate loop_rate(10);  

    try {
        while (rclcpp::ok()) {
            try {
#if XG_CHANXUECHE
                WebSocketServerSingleton::getInstance(cfg.port).navTimeoutCallback();
                int cnt = WebSocketServerSingleton::getInstance().add_get_heart_cnt();
                if(cnt %50 == 0){
                    if(startNrFlag == 0){
                        // WebSocketServerSingleton::getInstance().start_localization();
                        // std::this_thread::sleep_for(std::chrono::seconds(3));
                        WebSocketServerSingleton::getInstance().start_nr();
                        startNrFlag = 1;
                        std::this_thread::sleep_for(std::chrono::seconds(15));
                        WebSocketServerSingleton::getInstance().publishStartNavigation(1);

                    } 

                    WebSocketServerSingleton::getInstance().createRobotStatus(0, 0);
                    WebSocketServerSingleton::getInstance().clear_heart_cnt(); 
                }

                // WebSocketServerSingleton::getInstance(cfg.port).navTimeoutCallback();
                // int cnt = WebSocketServerSingleton::getInstance().add_get_heart_cnt();
                // if(cnt %50 == 0){
                //     std::string url = URL_FILE_UPLOAD + "map_id=" +  WebSocketServerSingleton::getInstance().get_map_id();
                //     std::string data = file_upload(url, "/home/<USER>/web_ws/src/web/maps/123456789/35/pose.txt");
                //     // std::string data = WebSocketServerSingleton::getInstance().upload_file_and_get_url("/home/<USER>/web_ws/src/web/maps/123456789/35/pose.txt");
                //     std::cout << "update data = " << data << std::endl;

                //     // 解析 JSON 字符串
                //     json j = json::parse(data);
                //     // 提取字段
                //     std::string filename = j["filename"];
                //     std::string url_pose = j["url"];
                //     // 输出结果
                //     std::cout << "filename: " << filename << std::endl;
                //     std::cout << "url: " << url_pose << std::endl;

                //     std::string output_file = "/home/<USER>/web_ws/pose.txt";
                //     file_download(url_pose, output_file);

                //     WebSocketServerSingleton::getInstance().clear_heart_cnt();
                // }
   
#else
                WebSocketClientSingleton::getInstance().navTimeoutCallback();
                int cnt = WebSocketClientSingleton::getInstance().add_get_heart_cnt();
                if(cnt %50 == 0){
                    WebSocketClientSingleton::getInstance().createRobotStatus(0, 0);
                    WebSocketClientSingleton::getInstance().clear_heart_cnt();
                }
#endif
                rclcpp::spin_some(node);
                loop_rate.sleep();
            } catch (const std::exception& e) {
                RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "Error in main loop: %s", e.what());
            }
        }
    } catch (const std::exception& e) {
        RCLCPP_INFO(rclcpp::get_logger(NODE_NAME), "Fatal error: %s", e.what());
    }

    // 退出
    rclcpp::shutdown();
    return 0;
}

