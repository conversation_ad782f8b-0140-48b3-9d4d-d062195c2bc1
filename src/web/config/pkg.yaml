pkg_rtk:
    path: "/home/<USER>/rtk_ws"
    pkg_name: "rtk"
    node_name: ["rtk_node"]
    launch_file: "rtk_launch.py"
    script_path: ""
    launch_param: ""
    script_param: ""

pkg_base:
    path: "/home/<USER>/base_ws"
    pkg_name: "base"
    node_name: ["base_node"]
    launch_file: "base_launch.py"
    script_path: ""
    launch_param: ""
    script_param: ""

pkg_lidar:
    path: "/home/<USER>/ros2_ws"
    pkg_name: "lslidar_cx_driver"
    node_name: ["lslidar_node"]
    launch_file: "lslidar_cx.launch"
    script_path: ""
    launch_param: ""
    script_param: ""

na_mapping:   # bev pcd start
    path: "/home/<USER>/code/na_localization_lslidar"
    pkg_name: "na_mapping"
    node_name: ["laserMapping"]
    launch_file: "mapping_lslidar.launch.py"
    script_path: "/home/<USER>/code/na_localization_lslidar/src/na_mapping/savemap.sh && sudo cp /home/<USER>/code/na_localization_lslidar/src/na_mapping/PCD/cloud_map.pcd /home/<USER>/code/na_localization_lslidar/src/na_localization/PCD/cloud_map.pcd && sudo cp /home/<USER>/code/na_localization_lslidar/src/na_mapping/PCD/pose.txt /home/<USER>/code/na_localization_lslidar/src/na_localization/PCD/pose.txt"
    launch_param: ""
    script_param: ""

topological_grid:  #pgm
    path: "/home/<USER>/code/topological_grid_map"
    pkg_name: "topological_grid_map"
    node_name: ["start_mapping"]
    launch_file: "start_mapping.launch.py"
    script_path: ""
    launch_param: ""
    script_param: ""

na_localization:   # relocation
    path: "/home/<USER>/code/na_localization_lslidar"
    pkg_name: "na_localization"
    node_name: ["laser_localization"]
    launch_file: "mapping_lslidar.launch.py"
    script_path: ""
    launch_param: ""
    script_param: ""

NR_Navigation:   # navigation
    path: "/home/<USER>/nav_zhouliu_backup/nav"
    pkg_name: "vehicle_simulator"
    node_name: ["differential_tracked_navigator", "global_traj_generate", "point_publisher", "map_server", "map_odom_broadcaster", "map_to_odom", "velodyne/gazebo_ros_laser_controller","robot_state_publisher"]
    launch_file: "system_garage.launch"
    script_path: ""
    launch_param: ""
    script_param: ""

