#ifndef COMMON_H
#define COMMON_H

#include <string>

#define NODE_NAME "web"

#define XG_CHANXUECHE 1

//配置参数目录
const std::string PKG_YAML_PATH = "/home/<USER>/xugong_web_ws/src/web/config/pkg.yaml"; //"/home/<USER>/web_ws/src/web/config/pkg.yaml";
const std::string config_yaml_path = "/home/<USER>/xugong_web_ws/src/web/config/config.yaml"; //"/home/<USER>/web_ws/src/web/config/config.yaml";
const std::string params_yaml_path = "/home/<USER>/xugong_web_ws/src/web/config/params.yaml"; //"/home/<USER>/web_ws/src/web/config/params.yaml";
const std::string tcp_yaml_path = "/home/<USER>/xugong_web_ws/src/web/config/tcp.yaml"; //"/home/<USER>/web_ws/src/web/config/tcp.yaml";

const std::string g_navigation_filename = "/home/<USER>/web_ws/navigation_task.json";

const std::string LOCAL_MAPS_DIR = "/home/<USER>/web_ws/src/web/maps"; //"/home/<USER>/web_ws/src/web/maps";

const std::string ALG_PGM_DIR = "/home/<USER>/code/topological_grid_map/maps";
const std::string ALG_PCD_MAPPING_DIR = "/home/<USER>/code/na_mapping_Lslidar/src/na_mapping/PCD";
const std::string ALG_PCD_LOCALIZATION_DIR = "/home/<USER>/code/na_localization_lslidar/src/na_localization/PCD";

const std::string URL_FILE_DOWNLOAD = "http://58.48.53.254:50080/showroom-api/minio/download/";
const std::string URL_FILE_UPLOAD = "http://10.42.0.90:9009/fileupload?";
// const std::string URL_FILE_UPLOAD = "http://47.122.25.244:64401/fileupload?";


#endif
