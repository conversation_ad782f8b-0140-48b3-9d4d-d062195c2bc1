Invoking command in '/home/<USER>/xugong_web_ws/build/global_traj_generate': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/global_traj_generate -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/global_traj_generate
Invoked command in '/home/<USER>/xugong_web_ws/build/global_traj_generate' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/global_traj_generate -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/global_traj_generate
Invoking command in '/home/<USER>/xugong_web_ws/build/global_traj_generate': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/global_traj_generate -- -j16 -l16
Invoked command in '/home/<USER>/xugong_web_ws/build/global_traj_generate' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/global_traj_generate -- -j16 -l16
Invoking command in '/home/<USER>/xugong_web_ws/build/global_traj_generate': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/global_traj_generate
Invoked command in '/home/<USER>/xugong_web_ws/build/global_traj_generate' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/global_traj_generate
