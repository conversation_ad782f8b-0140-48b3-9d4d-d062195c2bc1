[0.015s] Invoking command in '/home/<USER>/xugong_web_ws/build/can_bridge': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/can_bridge -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/can_bridge
[0.113s] -- The C compiler identification is GNU 11.4.0
[0.180s] -- The CXX compiler identification is GNU 11.4.0
[0.187s] -- Detecting C compiler ABI info
[0.237s] -- Detecting C compiler ABI info - done
[0.243s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.244s] -- Detecting C compile features
[0.244s] -- Detecting C compile features - done
[0.247s] -- Detecting CXX compiler ABI info
[0.302s] -- Detecting CXX compiler ABI info - done
[0.308s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.308s] -- Detecting CXX compile features
[0.309s] -- Detecting CXX compile features - done
[0.315s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.424s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.500s] -- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)
[0.530s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.534s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.541s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.552s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.565s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.584s] -- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[0.594s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.607s] -- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
[0.838s] -- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
[1.201s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.339s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.358s] -- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") 
[1.359s] -- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
[1.380s] -- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") 
[1.380s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[1.380s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
[1.380s] -- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so
[1.394s] -- Found PythonExtra: .so  
[1.446s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.468s] -- Added test 'lint_cmake' to check CMake code style
[1.470s] -- Added test 'xmllint' to check XML markup files
[1.473s] -- Configuring done
[1.499s] -- Generating done
[1.507s] -- Build files have been written to: /home/<USER>/xugong_web_ws/build/can_bridge
[1.515s] Invoked command in '/home/<USER>/xugong_web_ws/build/can_bridge' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/xugong_web_ws/src/can_bridge -DCMAKE_INSTALL_PREFIX=/home/<USER>/xugong_web_ws/install/can_bridge
[1.516s] Invoking command in '/home/<USER>/xugong_web_ws/build/can_bridge': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/can_bridge -- -j16 -l16
[1.548s] [  2%] [34m[1mGenerating C code for ROS interfaces[0m
[1.555s] [  4%] [34m[1mGenerating C++ code for ROS interfaces[0m
[1.566s] [  4%] Built target ament_cmake_python_copy_can_bridge
[1.729s] running egg_info
[1.729s] creating can_bridge.egg-info
[1.729s] writing can_bridge.egg-info/PKG-INFO
[1.729s] writing dependency_links to can_bridge.egg-info/dependency_links.txt
[1.729s] writing top-level names to can_bridge.egg-info/top_level.txt
[1.730s] writing manifest file 'can_bridge.egg-info/SOURCES.txt'
[1.731s] reading manifest file 'can_bridge.egg-info/SOURCES.txt'
[1.732s] writing manifest file 'can_bridge.egg-info/SOURCES.txt'
[1.760s] [  4%] Built target ament_cmake_python_build_can_bridge_egg
[1.900s] [  4%] Built target can_bridge__cpp
[1.905s] [  8%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_c.dir/rosidl_generator_c/can_bridge/msg/detail/vehicle_control_flags__functions.c.o[0m
[1.905s] [ 10%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_c.dir/rosidl_generator_c/can_bridge/msg/detail/vehicle_diagnostic__functions.c.o[0m
[1.907s] [ 10%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_c.dir/rosidl_generator_c/can_bridge/msg/detail/vehicle_motion__functions.c.o[0m
[1.910s] [ 12%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[1.912s] [ 14%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[1.917s] [ 17%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[1.963s] [ 19%] [32m[1mLinking C shared library libcan_bridge__rosidl_generator_c.so[0m
[2.010s] [ 19%] Built target can_bridge__rosidl_generator_c
[2.025s] [ 21%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[2.025s] [ 23%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
[2.026s] [ 25%] [34m[1mGenerating C introspection for ROS interfaces[0m
[2.237s] [ 27%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/can_bridge/msg/vehicle_control_flags__type_support.cpp.o[0m
[2.247s] [ 29%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/can_bridge/msg/vehicle_motion__type_support.cpp.o[0m
[2.250s] [ 31%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/can_bridge/msg/vehicle_diagnostic__type_support.cpp.o[0m
[2.337s] [ 34%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/can_bridge/msg/detail/vehicle_control_flags__type_support.cpp.o[0m
[2.340s] [ 36%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/can_bridge/msg/detail/vehicle_motion__type_support.cpp.o[0m
[2.341s] [ 38%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/can_bridge/msg/detail/vehicle_diagnostic__type_support.cpp.o[0m
[2.352s] [ 40%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/can_bridge/msg/detail/dds_fastrtps/vehicle_motion__type_support.cpp.o[0m
[2.355s] [ 42%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/can_bridge/msg/detail/dds_fastrtps/vehicle_diagnostic__type_support.cpp.o[0m
[2.358s] [ 44%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/can_bridge/msg/detail/dds_fastrtps/vehicle_control_flags__type_support.cpp.o[0m
[2.404s] [ 48%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_c.dir/rosidl_typesupport_c/can_bridge/msg/vehicle_control_flags__type_support.cpp.o[0m
[2.405s] [ 48%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_c.dir/rosidl_typesupport_c/can_bridge/msg/vehicle_diagnostic__type_support.cpp.o[0m
[2.408s] [ 51%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_c.dir/rosidl_typesupport_c/can_bridge/msg/vehicle_motion__type_support.cpp.o[0m
[2.453s] [ 53%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/can_bridge/msg/detail/vehicle_control_flags__type_support_c.cpp.o[0m
[2.459s] [ 55%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/can_bridge/msg/detail/vehicle_diagnostic__type_support_c.cpp.o[0m
[2.477s] [ 57%] [32mBuilding CXX object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/can_bridge/msg/detail/vehicle_motion__type_support_c.cpp.o[0m
[2.480s] [ 59%] [32m[1mLinking CXX shared library libcan_bridge__rosidl_typesupport_c.so[0m
[2.521s] [ 61%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/can_bridge/msg/detail/vehicle_control_flags__type_support.c.o[0m
[2.534s] [ 63%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/can_bridge/msg/detail/vehicle_motion__type_support.c.o[0m
[2.534s] [ 65%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/can_bridge/msg/detail/vehicle_diagnostic__type_support.c.o[0m
[2.580s] [ 65%] Built target can_bridge__rosidl_typesupport_c
[2.608s] [ 68%] [32m[1mLinking C shared library libcan_bridge__rosidl_typesupport_introspection_c.so[0m
[2.686s] [ 68%] Built target can_bridge__rosidl_typesupport_introspection_c
[2.864s] [ 70%] [32m[1mLinking CXX shared library libcan_bridge__rosidl_typesupport_cpp.so[0m
[2.979s] [ 70%] Built target can_bridge__rosidl_typesupport_cpp
[3.134s] [ 72%] [32m[1mLinking CXX shared library libcan_bridge__rosidl_typesupport_introspection_cpp.so[0m
[3.213s] [ 74%] [32m[1mLinking CXX shared library libcan_bridge__rosidl_typesupport_fastrtps_c.so[0m
[3.225s] [ 74%] Built target can_bridge__rosidl_typesupport_introspection_cpp
[3.254s] [ 76%] [32m[1mLinking CXX shared library libcan_bridge__rosidl_typesupport_fastrtps_cpp.so[0m
[3.330s] [ 76%] Built target can_bridge__rosidl_typesupport_fastrtps_c
[3.341s] [ 76%] Built target can_bridge__rosidl_typesupport_fastrtps_cpp
[3.370s] [ 76%] Built target can_bridge
[3.401s] [ 78%] [34m[1mGenerating Python code for ROS interfaces[0m
[4.053s] [ 78%] Built target can_bridge__py
[4.110s] [ 80%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_py.dir/rosidl_generator_py/can_bridge/msg/_vehicle_diagnostic_s.c.o[0m
[4.114s] [ 82%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_py.dir/rosidl_generator_py/can_bridge/msg/_vehicle_motion_s.c.o[0m
[4.115s] [ 85%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_generator_py.dir/rosidl_generator_py/can_bridge/msg/_vehicle_control_flags_s.c.o[0m
[4.338s] [ 87%] [32m[1mLinking C shared library rosidl_generator_py/can_bridge/libcan_bridge__rosidl_generator_py.so[0m
[4.431s] [ 87%] Built target can_bridge__rosidl_generator_py
[4.482s] [ 89%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_fastrtps_c__pyext.dir/rosidl_generator_py/can_bridge/_can_bridge_s.ep.rosidl_typesupport_fastrtps_c.c.o[0m
[4.483s] [ 91%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_introspection_c__pyext.dir/rosidl_generator_py/can_bridge/_can_bridge_s.ep.rosidl_typesupport_introspection_c.c.o[0m
[4.501s] [ 93%] [32mBuilding C object CMakeFiles/can_bridge__rosidl_typesupport_c__pyext.dir/rosidl_generator_py/can_bridge/_can_bridge_s.ep.rosidl_typesupport_c.c.o[0m
[4.614s] [ 95%] [32m[1mLinking C shared library rosidl_generator_py/can_bridge/can_bridge_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so[0m
[4.616s] [ 97%] [32m[1mLinking C shared library rosidl_generator_py/can_bridge/can_bridge_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so[0m
[4.680s] [100%] [32m[1mLinking C shared library rosidl_generator_py/can_bridge/can_bridge_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so[0m
[4.683s] [100%] Built target can_bridge__rosidl_typesupport_c__pyext
[4.691s] [100%] Built target can_bridge__rosidl_typesupport_fastrtps_c__pyext
[4.757s] [100%] Built target can_bridge__rosidl_typesupport_introspection_c__pyext
[4.797s] Invoked command in '/home/<USER>/xugong_web_ws/build/can_bridge' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/xugong_web_ws/build/can_bridge -- -j16 -l16
[4.834s] Invoking command in '/home/<USER>/xugong_web_ws/build/can_bridge': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/can_bridge
[4.836s] -- Install configuration: ""
[4.837s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/rosidl_interfaces/can_bridge
[4.837s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
[4.837s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
[4.838s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_diagnostic.h
[4.838s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_generator_c__visibility_control.h
[4.838s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_motion.h
[4.839s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_control_flags.h
[4.839s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
[4.839s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.h
[4.839s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.h
[4.839s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__functions.h
[4.839s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__struct.h
[4.839s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__functions.c
[4.839s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.h
[4.839s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__functions.c
[4.839s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__functions.c
[4.839s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__struct.h
[4.839s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__functions.h
[4.839s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__functions.h
[4.839s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__struct.h
[4.839s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/library_path.sh
[4.839s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/library_path.dsv
[4.839s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_generator_c.so
[4.842s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_generator_c.so" to ""
[4.842s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
[4.843s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
[4.843s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
[4.843s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
[4.843s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_fastrtps_c.h
[4.843s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_fastrtps_c.h
[4.843s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_fastrtps_c.h
[4.843s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_fastrtps_c.so
[4.843s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_fastrtps_c.so" to ""
[4.843s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
[4.843s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
[4.843s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_control_flags.hpp
[4.844s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_generator_cpp__visibility_control.hpp
[4.844s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_diagnostic.hpp
[4.844s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
[4.844s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__traits.hpp
[4.844s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.hpp
[4.844s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__traits.hpp
[4.844s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.hpp
[4.844s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__traits.hpp
[4.844s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__struct.hpp
[4.844s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__builder.hpp
[4.844s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__builder.hpp
[4.844s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.hpp
[4.844s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__builder.hpp
[4.844s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__struct.hpp
[4.844s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__struct.hpp
[4.845s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/vehicle_motion.hpp
[4.845s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
[4.845s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
[4.845s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
[4.845s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
[4.845s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/dds_fastrtps
[4.845s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_fastrtps_cpp.hpp
[4.845s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_fastrtps_cpp.hpp
[4.845s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_fastrtps_cpp.hpp
[4.846s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_fastrtps_cpp.so
[4.846s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_fastrtps_cpp.so" to ""
[4.846s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
[4.846s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
[4.846s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/rosidl_typesupport_introspection_c__visibility_control.h
[4.846s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
[4.847s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_introspection_c.h
[4.847s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_introspection_c.h
[4.847s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_introspection_c.h
[4.847s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.c
[4.847s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.c
[4.847s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.c
[4.847s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_introspection_c.so
[4.847s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_introspection_c.so" to ""
[4.847s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_c.so
[4.847s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_c.so" to ""
[4.847s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge
[4.849s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg
[4.849s] -- Up-to-date: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail
[4.849s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__type_support.cpp
[4.849s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_motion__rosidl_typesupport_introspection_cpp.hpp
[4.849s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__type_support.cpp
[4.850s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__rosidl_typesupport_introspection_cpp.hpp
[4.850s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_diagnostic__type_support.cpp
[4.850s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/include/can_bridge/can_bridge/msg/detail/vehicle_control_flags__rosidl_typesupport_introspection_cpp.hpp
[4.850s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_introspection_cpp.so
[4.850s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_introspection_cpp.so" to ""
[4.850s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_cpp.so
[4.850s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_typesupport_cpp.so" to ""
[4.850s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/pythonpath.sh
[4.850s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/pythonpath.dsv
[4.850s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info
[4.850s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/dependency_links.txt
[4.850s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/SOURCES.txt
[4.850s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/top_level.txt
[4.850s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge-0.0.0-py3.10.egg-info/PKG-INFO
[4.850s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge
[4.850s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/_can_bridge_s.ep.rosidl_typesupport_c.c
[4.850s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
[4.850s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
[4.851s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
[4.851s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/_can_bridge_s.ep.rosidl_typesupport_fastrtps_c.c
[4.851s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg
[4.851s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_diagnostic_s.c
[4.851s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_motion_s.c
[4.851s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_motion.py
[4.851s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_diagnostic.py
[4.851s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_control_flags_s.c
[4.851s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_control_flags.py
[4.851s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/__init__.py
[4.851s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/__init__.py
[4.851s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/libcan_bridge__rosidl_generator_py.so
[4.855s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/_can_bridge_s.ep.rosidl_typesupport_introspection_c.c
[4.902s] Listing '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge'...
[4.902s] Compiling '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/__init__.py'...
[4.902s] Listing '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg'...
[4.902s] Compiling '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/__init__.py'...
[4.902s] Compiling '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_control_flags.py'...
[4.902s] Compiling '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_diagnostic.py'...
[4.902s] Compiling '/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/msg/_vehicle_motion.py'...
[4.910s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
[4.910s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so" to ""
[4.910s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
[4.910s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so" to ""
[4.911s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
[4.911s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/local/lib/python3.10/dist-packages/can_bridge/can_bridge_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so" to ""
[4.911s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_generator_py.so
[4.911s] -- Set runtime path of "/home/<USER>/xugong_web_ws/install/can_bridge/lib/libcan_bridge__rosidl_generator_py.so" to ""
[4.911s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleControlFlags.idl
[4.911s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleDiagnostic.idl
[4.911s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleMotion.idl
[4.911s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleControlFlags.msg
[4.911s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleDiagnostic.msg
[4.911s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/msg/VehicleMotion.msg
[4.911s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/package_run_dependencies/can_bridge
[4.911s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/parent_prefix_path/can_bridge
[4.911s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/ament_prefix_path.sh
[4.911s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/ament_prefix_path.dsv
[4.911s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/path.sh
[4.911s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/environment/path.dsv
[4.911s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.bash
[4.911s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.sh
[4.912s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.zsh
[4.912s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/local_setup.dsv
[4.912s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.dsv
[4.912s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/ament_index/resource_index/packages/can_bridge
[4.912s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_cExport.cmake
[4.912s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_cExport-noconfig.cmake
[4.912s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cExport.cmake
[4.912s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cExport-noconfig.cmake
[4.912s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_cppExport.cmake
[4.912s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cppExport.cmake
[4.912s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake
[4.912s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cExport.cmake
[4.912s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cExport-noconfig.cmake
[4.912s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cExport.cmake
[4.912s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cExport-noconfig.cmake
[4.913s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cppExport.cmake
[4.913s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_introspection_cppExport-noconfig.cmake
[4.913s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cppExport.cmake
[4.913s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridge__rosidl_typesupport_cppExport-noconfig.cmake
[4.913s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_pyExport.cmake
[4.913s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/export_can_bridge__rosidl_generator_pyExport-noconfig.cmake
[4.913s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/rosidl_cmake-extras.cmake
[4.913s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_dependencies-extras.cmake
[4.913s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_include_directories-extras.cmake
[4.913s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_libraries-extras.cmake
[4.913s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/ament_cmake_export_targets-extras.cmake
[4.913s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
[4.913s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
[4.913s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridgeConfig.cmake
[4.913s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/cmake/can_bridgeConfig-version.cmake
[4.913s] -- Installing: /home/<USER>/xugong_web_ws/install/can_bridge/share/can_bridge/package.xml
[4.919s] Invoked command in '/home/<USER>/xugong_web_ws/build/can_bridge' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/xugong_web_ws/build/can_bridge
